"""
Comprehensive Test Runner for TaoForge Integration Tests
Executes all integration tests and generates detailed reports
"""

import pytest
import sys
import os
import json
import time
import subprocess
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime
import coverage

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


class TaoForgeTestRunner:
    """Comprehensive test runner for TaoForge system"""
    
    def __init__(self, output_dir: str = "test_reports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.test_results = {}
        self.coverage_data = {}
        self.performance_metrics = {}
        
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all integration tests and collect results"""
        print("🚀 Starting TaoForge Comprehensive Integration Tests")
        print("=" * 60)
        
        start_time = time.time()
        
        # Test suites to run
        test_suites = [
            {
                "name": "Unit Tests",
                "pattern": "test_*.py",
                "markers": "",
                "timeout": 300
            },
            {
                "name": "Integration Tests", 
                "pattern": "test_*integration*.py",
                "markers": "integration",
                "timeout": 600
            },
            {
                "name": "VS Code Extension Tests",
                "pattern": "test_vscode*.py",
                "markers": "vscode",
                "timeout": 300
            },
            {
                "name": "Performance Tests",
                "pattern": "test_*performance*.py",
                "markers": "slow",
                "timeout": 900
            },
            {
                "name": "End-to-End Tests",
                "pattern": "test_end_to_end*.py",
                "markers": "integration",
                "timeout": 1200
            }
        ]
        
        # Run each test suite
        for suite in test_suites:
            print(f"\n📋 Running {suite['name']}...")
            result = self._run_test_suite(suite)
            self.test_results[suite['name']] = result
            
            # Print immediate results
            if result['success']:
                print(f"✅ {suite['name']}: {result['passed']}/{result['total']} tests passed")
            else:
                print(f"❌ {suite['name']}: {result['passed']}/{result['total']} tests passed")
        
        # Run coverage analysis
        print(f"\n📊 Running coverage analysis...")
        self.coverage_data = self._run_coverage_analysis()
        
        # Run performance benchmarks
        print(f"\n⚡ Running performance benchmarks...")
        self.performance_metrics = self._run_performance_benchmarks()
        
        # Generate comprehensive report
        total_time = time.time() - start_time
        report = self._generate_comprehensive_report(total_time)
        
        # Save report
        report_file = self.output_dir / f"integration_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Generate HTML report
        html_report = self._generate_html_report(report)
        html_file = self.output_dir / f"integration_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_report)
        
        print(f"\n📄 Reports saved to:")
        print(f"   JSON: {report_file}")
        print(f"   HTML: {html_file}")
        
        return report
    
    def _run_test_suite(self, suite: Dict[str, Any]) -> Dict[str, Any]:
        """Run a specific test suite"""
        cmd = [
            sys.executable, "-m", "pytest",
            "-v",
            "--tb=short",
            f"--timeout={suite['timeout']}",
            "--json-report",
            f"--json-report-file={self.output_dir}/pytest_{suite['name'].lower().replace(' ', '_')}.json"
        ]
        
        # Add markers if specified
        if suite['markers']:
            cmd.extend(["-m", suite['markers']])
        
        # Add pattern
        cmd.append(suite['pattern'])
        
        try:
            result = subprocess.run(
                cmd,
                cwd=Path(__file__).parent,
                capture_output=True,
                text=True,
                timeout=suite['timeout']
            )
            
            # Parse pytest JSON report
            json_report_file = self.output_dir / f"pytest_{suite['name'].lower().replace(' ', '_')}.json"
            if json_report_file.exists():
                with open(json_report_file) as f:
                    pytest_data = json.load(f)
                
                return {
                    "success": result.returncode == 0,
                    "total": pytest_data["summary"]["total"],
                    "passed": pytest_data["summary"].get("passed", 0),
                    "failed": pytest_data["summary"].get("failed", 0),
                    "skipped": pytest_data["summary"].get("skipped", 0),
                    "duration": pytest_data["duration"],
                    "stdout": result.stdout,
                    "stderr": result.stderr
                }
            else:
                # Fallback parsing
                return {
                    "success": result.returncode == 0,
                    "total": 0,
                    "passed": 0,
                    "failed": 0,
                    "skipped": 0,
                    "duration": 0,
                    "stdout": result.stdout,
                    "stderr": result.stderr
                }
                
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "total": 0,
                "passed": 0,
                "failed": 0,
                "skipped": 0,
                "duration": suite['timeout'],
                "stdout": "",
                "stderr": f"Test suite timed out after {suite['timeout']} seconds"
            }
        except Exception as e:
            return {
                "success": False,
                "total": 0,
                "passed": 0,
                "failed": 0,
                "skipped": 0,
                "duration": 0,
                "stdout": "",
                "stderr": str(e)
            }
    
    def _run_coverage_analysis(self) -> Dict[str, Any]:
        """Run coverage analysis on the codebase"""
        try:
            # Initialize coverage
            cov = coverage.Coverage(source=['src'])
            cov.start()
            
            # Run tests with coverage
            cmd = [
                sys.executable, "-m", "pytest",
                "--cov=src",
                "--cov-report=json",
                f"--cov-report-file={self.output_dir}/coverage.json",
                "--cov-report=html",
                f"--cov-report-dir={self.output_dir}/htmlcov",
                "test_*.py"
            ]
            
            subprocess.run(cmd, cwd=Path(__file__).parent, capture_output=True)
            
            # Read coverage data
            coverage_file = self.output_dir / "coverage.json"
            if coverage_file.exists():
                with open(coverage_file) as f:
                    coverage_data = json.load(f)
                return coverage_data
            else:
                return {"error": "Coverage data not generated"}
                
        except Exception as e:
            return {"error": str(e)}
    
    def _run_performance_benchmarks(self) -> Dict[str, Any]:
        """Run performance benchmarks"""
        try:
            cmd = [
                sys.executable, "-m", "pytest",
                "-v",
                "-m", "slow",
                "--benchmark-json",
                f"{self.output_dir}/benchmark.json",
                "test_performance*.py"
            ]
            
            result = subprocess.run(
                cmd,
                cwd=Path(__file__).parent,
                capture_output=True,
                text=True
            )
            
            # Read benchmark data
            benchmark_file = self.output_dir / "benchmark.json"
            if benchmark_file.exists():
                with open(benchmark_file) as f:
                    benchmark_data = json.load(f)
                return benchmark_data
            else:
                return {"error": "Benchmark data not generated"}
                
        except Exception as e:
            return {"error": str(e)}
    
    def _generate_comprehensive_report(self, total_time: float) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        # Calculate overall statistics
        total_tests = sum(suite.get('total', 0) for suite in self.test_results.values())
        total_passed = sum(suite.get('passed', 0) for suite in self.test_results.values())
        total_failed = sum(suite.get('failed', 0) for suite in self.test_results.values())
        total_skipped = sum(suite.get('skipped', 0) for suite in self.test_results.values())
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        # Get coverage percentage
        coverage_percent = 0
        if 'totals' in self.coverage_data:
            coverage_percent = self.coverage_data['totals'].get('percent_covered', 0)
        
        return {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed": total_passed,
                "failed": total_failed,
                "skipped": total_skipped,
                "success_rate": round(success_rate, 2),
                "total_duration": round(total_time, 2),
                "coverage_percent": round(coverage_percent, 2)
            },
            "test_suites": self.test_results,
            "coverage": self.coverage_data,
            "performance": self.performance_metrics,
            "recommendations": self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        # Check success rate
        total_tests = sum(suite.get('total', 0) for suite in self.test_results.values())
        total_passed = sum(suite.get('passed', 0) for suite in self.test_results.values())
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        if success_rate < 90:
            recommendations.append("🔴 Test success rate is below 90%. Review failed tests and fix issues.")
        elif success_rate < 95:
            recommendations.append("🟡 Test success rate is below 95%. Consider improving test reliability.")
        else:
            recommendations.append("✅ Excellent test success rate!")
        
        # Check coverage
        coverage_percent = 0
        if 'totals' in self.coverage_data:
            coverage_percent = self.coverage_data['totals'].get('percent_covered', 0)
        
        if coverage_percent < 80:
            recommendations.append("🔴 Code coverage is below 80%. Add more comprehensive tests.")
        elif coverage_percent < 90:
            recommendations.append("🟡 Code coverage is below 90%. Consider adding edge case tests.")
        else:
            recommendations.append("✅ Excellent code coverage!")
        
        # Check for failed test suites
        for suite_name, suite_data in self.test_results.items():
            if not suite_data.get('success', False):
                recommendations.append(f"🔴 {suite_name} failed. Review errors and fix issues.")
        
        return recommendations

    def _generate_html_report(self, report: Dict[str, Any]) -> str:
        """Generate HTML report"""
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>TaoForge Integration Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }}
        .summary {{ background: #ecf0f1; padding: 15px; margin: 20px 0; border-radius: 5px; }}
        .success {{ color: #27ae60; }}
        .warning {{ color: #f39c12; }}
        .error {{ color: #e74c3c; }}
        .test-suite {{ margin: 20px 0; padding: 15px; border: 1px solid #bdc3c7; border-radius: 5px; }}
        .recommendations {{ background: #fff3cd; padding: 15px; margin: 20px 0; border-radius: 5px; }}
        table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
        th, td {{ padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #f2f2f2; }}
        .progress-bar {{ width: 100%; background-color: #f0f0f0; border-radius: 5px; }}
        .progress-fill {{ height: 20px; background-color: #4CAF50; border-radius: 5px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 TaoForge Integration Test Report</h1>
        <p>Generated on {report['timestamp']}</p>
    </div>

    <div class="summary">
        <h2>📊 Test Summary</h2>
        <table>
            <tr><th>Metric</th><th>Value</th></tr>
            <tr><td>Total Tests</td><td>{report['summary']['total_tests']}</td></tr>
            <tr><td>Passed</td><td class="success">{report['summary']['passed']}</td></tr>
            <tr><td>Failed</td><td class="error">{report['summary']['failed']}</td></tr>
            <tr><td>Skipped</td><td class="warning">{report['summary']['skipped']}</td></tr>
            <tr><td>Success Rate</td><td>{report['summary']['success_rate']}%</td></tr>
            <tr><td>Coverage</td><td>{report['summary']['coverage_percent']}%</td></tr>
            <tr><td>Duration</td><td>{report['summary']['total_duration']}s</td></tr>
        </table>

        <h3>Success Rate</h3>
        <div class="progress-bar">
            <div class="progress-fill" style="width: {report['summary']['success_rate']}%"></div>
        </div>

        <h3>Code Coverage</h3>
        <div class="progress-bar">
            <div class="progress-fill" style="width: {report['summary']['coverage_percent']}%"></div>
        </div>
    </div>

    <div class="recommendations">
        <h2>💡 Recommendations</h2>
        <ul>
        {"".join(f"<li>{rec}</li>" for rec in report['recommendations'])}
        </ul>
    </div>

    <h2>🧪 Test Suites</h2>
    {"".join(self._generate_test_suite_html(name, data) for name, data in report['test_suites'].items())}

</body>
</html>
        """
        return html

    def _generate_test_suite_html(self, name: str, data: Dict[str, Any]) -> str:
        """Generate HTML for a test suite"""
        status_class = "success" if data.get('success', False) else "error"
        status_icon = "✅" if data.get('success', False) else "❌"

        return f"""
    <div class="test-suite">
        <h3 class="{status_class}">{status_icon} {name}</h3>
        <table>
            <tr><th>Metric</th><th>Value</th></tr>
            <tr><td>Total Tests</td><td>{data.get('total', 0)}</td></tr>
            <tr><td>Passed</td><td class="success">{data.get('passed', 0)}</td></tr>
            <tr><td>Failed</td><td class="error">{data.get('failed', 0)}</td></tr>
            <tr><td>Skipped</td><td class="warning">{data.get('skipped', 0)}</td></tr>
            <tr><td>Duration</td><td>{data.get('duration', 0)}s</td></tr>
        </table>
        {f'<pre class="error">{data["stderr"]}</pre>' if data.get('stderr') else ''}
    </div>
        """


def main():
    """Main execution function"""
    print("🚀 TaoForge Comprehensive Integration Test Suite")
    print("=" * 60)

    # Create test runner
    runner = TaoForgeTestRunner()

    # Run all tests
    report = runner.run_all_tests()

    # Print final summary
    print("\n" + "=" * 60)
    print("📋 FINAL TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {report['summary']['total_tests']}")
    print(f"Passed: {report['summary']['passed']} ✅")
    print(f"Failed: {report['summary']['failed']} ❌")
    print(f"Skipped: {report['summary']['skipped']} ⏭️")
    print(f"Success Rate: {report['summary']['success_rate']}%")
    print(f"Coverage: {report['summary']['coverage_percent']}%")
    print(f"Total Duration: {report['summary']['total_duration']}s")

    print("\n💡 RECOMMENDATIONS:")
    for rec in report['recommendations']:
        print(f"  {rec}")

    # Determine overall status
    if report['summary']['success_rate'] >= 95 and report['summary']['coverage_percent'] >= 80:
        print("\n🎉 INTEGRATION TESTS: EXCELLENT! System is ready for production.")
        return 0
    elif report['summary']['success_rate'] >= 90 and report['summary']['coverage_percent'] >= 70:
        print("\n✅ INTEGRATION TESTS: GOOD! Minor improvements recommended.")
        return 0
    elif report['summary']['success_rate'] >= 80:
        print("\n⚠️  INTEGRATION TESTS: NEEDS IMPROVEMENT! Address failing tests.")
        return 1
    else:
        print("\n❌ INTEGRATION TESTS: CRITICAL ISSUES! System not ready for production.")
        return 2


if __name__ == "__main__":
    exit(main())
