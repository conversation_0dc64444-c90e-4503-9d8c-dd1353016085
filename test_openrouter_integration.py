#!/usr/bin/env python3
"""
Test OpenRouter integration with Aetherforge API key management system
"""

import sys
import os
from pathlib import Path
import asyncio

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_openrouter_integration():
    """Test OpenRouter integration"""
    print("🌐 Testing OpenRouter Integration")
    print("=" * 40)
    
    try:
        from api_manager import APIManager, APIProvider, APIKeyValidator
        
        # Test 1: Provider enumeration
        print("\n1. Testing provider enumeration...")
        providers = list(APIProvider)
        provider_values = [p.value for p in providers]
        
        if "openrouter" in provider_values:
            print("   ✅ OpenRouter provider found in enum")
        else:
            print("   ❌ OpenRouter provider missing from enum")
            return False
        
        # Test 2: API Manager creation
        print("\n2. Testing API manager creation...")
        api_manager = APIManager()
        
        # Check if OpenRouter is in configured providers
        configured_providers = api_manager.list_configured_providers()
        openrouter_config = next((p for p in configured_providers if p["provider"] == "openrouter"), None)
        
        if openrouter_config:
            print(f"   ✅ OpenRouter configuration found")
            print(f"   📋 Model: {openrouter_config['model']}")
            print(f"   🔑 Has key: {openrouter_config['has_key']}")
        else:
            print("   ❌ OpenRouter configuration not found")
            return False
        
        # Test 3: Default model
        print("\n3. Testing default model...")
        default_model = api_manager._get_default_model(APIProvider.OPENROUTER)
        if default_model == "openai/gpt-3.5-turbo":
            print(f"   ✅ Correct default model: {default_model}")
        else:
            print(f"   ⚠️  Unexpected default model: {default_model}")
        
        # Test 4: Environment variable mapping
        print("\n4. Testing environment variable mapping...")
        test_key = "sk-or-test123456789"
        
        # Set environment variable
        os.environ["OPENROUTER_API_KEY"] = test_key
        
        # Get API key
        retrieved_key = api_manager.get_api_key(APIProvider.OPENROUTER)
        
        if retrieved_key == test_key:
            print("   ✅ Environment variable mapping works")
        else:
            print(f"   ❌ Environment variable mapping failed: {retrieved_key}")
            return False
        
        # Clean up
        del os.environ["OPENROUTER_API_KEY"]
        
        # Test 5: Validation function exists
        print("\n5. Testing validation function...")
        if hasattr(APIKeyValidator, 'validate_openrouter_key'):
            print("   ✅ OpenRouter validation function exists")
        else:
            print("   ❌ OpenRouter validation function missing")
            return False
        
        # Test 6: Mock validation (without real API call)
        print("\n6. Testing mock validation...")
        try:
            # This will fail without a real key, but we can test the function exists
            # and handles errors properly
            result = await APIKeyValidator.validate_openrouter_key("invalid-key")
            if result["provider"] == "openrouter" and not result["valid"]:
                print("   ✅ Validation function handles invalid keys correctly")
            else:
                print(f"   ⚠️  Unexpected validation result: {result}")
        except Exception as e:
            print(f"   ✅ Validation function exists and handles errors: {type(e).__name__}")
        
        # Test 7: Client initialization (with API key)
        print("\n7. Testing client initialization...")
        try:
            # Set a test API key to enable client initialization
            os.environ["OPENROUTER_API_KEY"] = "sk-or-test123"

            # Create a new API manager to pick up the environment variable
            test_manager = APIManager()

            if APIProvider.OPENROUTER in test_manager.providers:
                print("   ✅ OpenRouter provider configured with API key")

                # Test client initialization
                test_manager._initialize_clients()
                if APIProvider.OPENROUTER in test_manager.clients:
                    print("   ✅ OpenRouter client initialized")
                else:
                    print("   ⚠️  OpenRouter client not in clients dict (async initialization)")
            else:
                print("   ❌ OpenRouter provider not configured")
                return False

            # Clean up
            del os.environ["OPENROUTER_API_KEY"]

        except Exception as e:
            print(f"   ⚠️  Client initialization error: {e}")
            # Clean up on error
            if "OPENROUTER_API_KEY" in os.environ:
                del os.environ["OPENROUTER_API_KEY"]
        
        # Test 8: Fallback order (with configured provider)
        print("\n8. Testing fallback order...")

        # Test with the manager that has OpenRouter configured
        if APIProvider.OPENROUTER in test_manager.fallback_order:
            position = test_manager.fallback_order.index(APIProvider.OPENROUTER)
            print(f"   ✅ OpenRouter in fallback order at position {position + 1}")
        else:
            print("   ⚠️  OpenRouter not in fallback order (expected without API key)")

        # Test that OpenRouter is in the base fallback order definition
        base_fallback = [
            APIProvider.OPENAI,
            APIProvider.AZURE,
            APIProvider.OPENROUTER,
            APIProvider.ANTHROPIC,
            APIProvider.LOCAL
        ]
        if APIProvider.OPENROUTER in base_fallback:
            print("   ✅ OpenRouter in base fallback order definition")
        else:
            print("   ❌ OpenRouter missing from base fallback order")
            return False
        
        print("\n" + "=" * 40)
        print("🎉 All OpenRouter integration tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def test_cli_integration():
    """Test CLI integration for OpenRouter"""
    print("\n🖥️ Testing CLI Integration")
    print("=" * 30)
    
    try:
        from api_key_cli import APIKeyCLI
        
        # Test CLI creation
        cli = APIKeyCLI()
        print("   ✅ CLI created successfully")
        
        # Test provider validation
        try:
            from api_manager import APIProvider
            provider_enum = APIProvider("openrouter")
            print("   ✅ OpenRouter provider can be created from string")
        except ValueError:
            print("   ❌ OpenRouter provider string conversion failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ CLI integration error: {e}")
        return False

def test_vscode_integration():
    """Test VS Code extension integration"""
    print("\n🎨 Testing VS Code Integration")
    print("=" * 32)
    
    try:
        import json
        
        # Test package.json configuration
        with open("vscode-extension/package.json", "r") as f:
            package_data = json.load(f)
        
        # Check for OpenRouter settings
        properties = package_data.get("contributes", {}).get("configuration", {}).get("properties", {})
        
        if "aetherforge.apiKeys.openrouter" in properties:
            print("   ✅ OpenRouter API key setting found in package.json")
        else:
            print("   ❌ OpenRouter API key setting missing from package.json")
            return False
        
        # Check preferred provider enum
        preferred_provider = properties.get("aetherforge.apiKeys.preferredProvider", {})
        enum_values = preferred_provider.get("enum", [])
        
        if "openrouter" in enum_values:
            print("   ✅ OpenRouter in preferred provider enum")
        else:
            print("   ❌ OpenRouter missing from preferred provider enum")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ VS Code integration error: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 OpenRouter Integration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Core Integration", test_openrouter_integration()),
        ("CLI Integration", test_cli_integration()),
        ("VS Code Integration", test_vscode_integration())
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_coro in tests:
        print(f"\n🔬 Running {test_name}...")
        try:
            if asyncio.iscoroutine(test_coro):
                result = await test_coro
            else:
                result = test_coro
                
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 50)
    print("📊 OPENROUTER INTEGRATION TEST RESULTS")
    print("=" * 50)
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All OpenRouter integration tests passed!")
        print("🌐 OpenRouter is fully integrated into Aetherforge!")
        return True
    else:
        print(f"\n⚠️ {total - passed} test(s) failed.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
