"""
Enhanced Comprehensive QA Agent for Aetherforge
Performs thorough testing of generated code, including:
- Unit test generation for all code types
- Integration test planning and execution
- Code quality assessment
- Security vulnerability scanning
- Performance testing recommendations
- Test execution capabilities with reports
- Feedback loop integration with Developer agent
"""

import asyncio
import json
import logging
import os
import re
import subprocess
import tempfile
import ast
import shutil
from typing import Dict, Any, List, Optional, Tuple, Union, Set
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

# Import analyst and architect components for requirement validation
try:
    from src.analyst_agent import MCPRAGClient, ProjectSpecification
    from src.architect_agent import SystemArchitecture
    from src.developer_agent import ProjectStructure, CodeFile
except ImportError:
    from analyst_agent import MCPRAGClient, ProjectSpecification
    from architect_agent import SystemArchitecture
    from developer_agent import ProjectStructure, CodeFile

logger = logging.getLogger(__name__)

class QAAgentError(Exception):
    """Base exception for QA Agent errors"""
    pass

class TestExecutionError(QAAgentError):
    """Raised when test execution fails"""
    pass

class ValidationError(QAAgentError):
    """Raised when validation fails"""
    pass

class CoverageError(QAAgentError):
    """Raised when coverage requirements are not met"""
    pass

class TestType(Enum):
    """Types of tests that can be executed"""
    UNIT = "unit"
    INTEGRATION = "integration"
    E2E = "e2e"
    API = "api"
    PERFORMANCE = "performance"
    SECURITY = "security"
    ACCESSIBILITY = "accessibility"
    LOAD = "load"
    STRESS = "stress"
    SMOKE = "smoke"

class TestStatus(Enum):
    """Test execution status"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"

class QualityLevel(Enum):
    """Quality assurance levels"""
    BASIC = "basic"
    STANDARD = "standard"
    COMPREHENSIVE = "comprehensive"
    ENTERPRISE = "enterprise"

class SecuritySeverity(Enum):
    """Security vulnerability severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class TestGenerationStrategy(Enum):
    """Test generation strategies"""
    BASIC = "basic"
    COMPREHENSIVE = "comprehensive"
    AI_ENHANCED = "ai_enhanced"
    PROPERTY_BASED = "property_based"

class CodeLanguage(Enum):
    """Supported programming languages for test generation"""
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    PYTHON = "python"
    JAVA = "java"
    CSHARP = "csharp"
    GO = "go"
    RUST = "rust"
    PHP = "php"
    KOTLIN = "kotlin"
    SWIFT = "swift"
    RUBY = "ruby"
    SCALA = "scala"

@dataclass
class SecurityVulnerability:
    """Represents a security vulnerability"""
    vulnerability_type: str
    severity: SecuritySeverity
    description: str
    file_path: str
    line_number: Optional[int] = None
    recommendation: str = ""
    cwe_id: Optional[str] = None

@dataclass
class PerformanceMetric:
    """Represents a performance metric"""
    metric_name: str
    value: float
    unit: str
    threshold: Optional[float] = None
    status: str = "unknown"  # "pass", "fail", "warning"

@dataclass
class TestResult:
    """Represents a test execution result"""
    test_name: str
    test_type: TestType
    status: TestStatus
    duration: float
    error_message: Optional[str] = None
    coverage: Optional[float] = None
    assertions: int = 0
    file_path: Optional[str] = None
    stack_trace: Optional[str] = None
    performance_metrics: List[PerformanceMetric] = field(default_factory=list)

@dataclass
class TestSuite:
    """Represents a collection of tests"""
    name: str
    test_type: TestType
    tests: List[TestResult] = field(default_factory=list)
    total_tests: int = 0
    passed_tests: int = 0
    failed_tests: int = 0
    skipped_tests: int = 0
    total_duration: float = 0.0
    coverage_percentage: float = 0.0
    setup_time: float = 0.0
    teardown_time: float = 0.0

@dataclass
class GeneratedTest:
    """Represents a generated test case"""
    test_name: str
    test_code: str
    test_type: TestType
    target_function: str
    target_file: str
    language: CodeLanguage
    dependencies: List[str] = field(default_factory=list)
    setup_code: str = ""
    teardown_code: str = ""

@dataclass
class QualityReport:
    """Comprehensive quality assessment report"""
    project_name: str
    test_suites: List[TestSuite] = field(default_factory=list)
    generated_tests: List[GeneratedTest] = field(default_factory=list)
    security_vulnerabilities: List[SecurityVulnerability] = field(default_factory=list)
    performance_metrics: List[PerformanceMetric] = field(default_factory=list)
    overall_coverage: float = 0.0
    quality_score: float = 0.0
    requirements_compliance: float = 0.0
    security_score: float = 0.0
    performance_score: float = 0.0
    accessibility_score: float = 0.0
    maintainability_score: float = 0.0
    issues_found: List[Dict[str, Any]] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    failed_test_fixes: List[Dict[str, Any]] = field(default_factory=list)
    execution_summary: Dict[str, Any] = field(default_factory=dict)
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())

@dataclass
class TestGenerationContext:
    """Context for test generation"""
    source_files: List[Path] = field(default_factory=list)
    target_language: CodeLanguage = CodeLanguage.JAVASCRIPT
    test_framework: str = "jest"
    generation_strategy: TestGenerationStrategy = TestGenerationStrategy.COMPREHENSIVE
    coverage_target: float = 80.0
    include_edge_cases: bool = True
    include_error_cases: bool = True
    mock_external_dependencies: bool = True

@dataclass
class QAContext:
    """Enhanced context for QA execution"""
    project_path: Path
    project_specification: Optional[ProjectSpecification] = None
    architecture: Optional[SystemArchitecture] = None
    project_structure: Optional[ProjectStructure] = None
    quality_level: QualityLevel = QualityLevel.STANDARD
    requirements: Dict[str, Any] = field(default_factory=dict)
    test_config: Dict[str, Any] = field(default_factory=dict)
    test_generation_context: Optional[TestGenerationContext] = None
    enable_test_generation: bool = True
    enable_security_scanning: bool = True
    enable_performance_testing: bool = True
    enable_accessibility_testing: bool = False
    feedback_mode: bool = True  # Enable feedback to developer agent
    max_test_execution_time: int = 300  # 5 minutes
    parallel_test_execution: bool = True

class QAAgent:
    """Enhanced Comprehensive Quality Assurance Agent"""

    def __init__(self, mcp_url: str = None, api_manager=None):
        self.mcp_client = MCPRAGClient(mcp_url)

        # Use provided API manager or create new one
        if api_manager:
            self.api_manager = api_manager
        else:
            try:
                from .api_manager import APIManager
            except ImportError:
                from api_manager import APIManager
            self.api_manager = APIManager()

        # Test execution configurations
        self.test_frameworks = self._initialize_test_frameworks()
        self.quality_gates = self._initialize_quality_gates()
        self.validation_rules = self._initialize_validation_rules()
        self.test_generators = self._initialize_test_generators()
        self.security_scanners = self._initialize_security_scanners()
        self.performance_analyzers = self._initialize_performance_analyzers()

        # Coverage and quality thresholds
        self.coverage_thresholds = {
            QualityLevel.BASIC: 60.0,
            QualityLevel.STANDARD: 80.0,
            QualityLevel.COMPREHENSIVE: 90.0,
            QualityLevel.ENTERPRISE: 95.0
        }

        # Test generation templates
        self.test_templates = self._initialize_test_templates()

        # Security vulnerability patterns
        self.security_patterns = self._initialize_security_patterns()

        # Performance benchmarks
        self.performance_benchmarks = self._initialize_performance_benchmarks()
    
    async def execute_qa_process(self, context: QAContext) -> QualityReport:
        """Execute enhanced comprehensive QA process"""
        logger.info(f"Starting enhanced QA process for project at {context.project_path}")

        try:
            # Step 1: Validate inputs and setup
            await self._validate_qa_inputs(context)

            # Step 2: Discover and analyze project structure
            await self._analyze_project_structure(context)

            # Step 3: Generate missing tests if enabled
            generated_tests = []
            if context.enable_test_generation:
                generated_tests = await self._generate_missing_tests(context)
                await self._write_generated_tests(generated_tests, context)

            # Step 4: Execute test suites (including generated tests)
            test_suites = await self._execute_test_suites(context)

            # Step 5: Analyze failed tests and suggest fixes
            failed_test_fixes = await self._analyze_failed_tests(test_suites, context)

            # Step 6: Validate against requirements
            requirements_compliance = await self._validate_requirements_compliance(context)

            # Step 7: Perform enhanced security analysis
            security_vulnerabilities, security_score = await self._perform_enhanced_security_analysis(context)

            # Step 8: Analyze performance characteristics
            performance_metrics, performance_score = await self._analyze_enhanced_performance(context)

            # Step 9: Perform accessibility testing if enabled
            accessibility_score = 0.0
            if context.enable_accessibility_testing:
                accessibility_score = await self._perform_accessibility_testing(context)

            # Step 10: Calculate maintainability score
            maintainability_score = await self._calculate_maintainability_score(context)

            # Step 11: Calculate overall quality metrics
            quality_score = await self._calculate_enhanced_quality_score(
                test_suites, requirements_compliance, security_score,
                performance_score, accessibility_score, maintainability_score
            )

            # Step 12: Generate comprehensive recommendations
            recommendations = await self._generate_enhanced_recommendations(
                context, test_suites, security_vulnerabilities, performance_metrics
            )

            # Step 13: Create comprehensive report
            report = QualityReport(
                project_name=context.project_specification.project_name if context.project_specification else "Unknown Project",
                test_suites=test_suites,
                generated_tests=generated_tests,
                security_vulnerabilities=security_vulnerabilities,
                performance_metrics=performance_metrics,
                overall_coverage=self._calculate_overall_coverage(test_suites),
                quality_score=quality_score,
                requirements_compliance=requirements_compliance,
                security_score=security_score,
                performance_score=performance_score,
                accessibility_score=accessibility_score,
                maintainability_score=maintainability_score,
                recommendations=recommendations,
                failed_test_fixes=failed_test_fixes,
                execution_summary=self._create_execution_summary(test_suites, generated_tests)
            )

            # Step 14: Send feedback to developer agent if enabled
            if context.feedback_mode and failed_test_fixes:
                await self._send_feedback_to_developer(failed_test_fixes, context)

            # Step 15: Save report and artifacts
            await self._save_qa_artifacts(context, report)

            logger.info(f"Enhanced QA process completed with quality score: {quality_score:.2f}")
            return report

        except Exception as e:
            logger.error(f"Enhanced QA process failed: {e}")
            await self._handle_qa_error(e, context)
            raise QAAgentError(f"Enhanced QA process failed: {e}")
    
    def _initialize_test_frameworks(self) -> Dict[str, Dict[str, Any]]:
        """Initialize comprehensive test framework configurations"""
        return {
            # JavaScript/TypeScript Frameworks
            "jest": {
                "command": "npm test",
                "coverage_command": "npm run test:coverage",
                "config_file": "jest.config.js",
                "test_pattern": "**/*.test.{js,ts,jsx,tsx}",
                "coverage_threshold": 80,
                "supports_parallel": True,
                "supports_watch": True,
                "languages": [CodeLanguage.JAVASCRIPT, CodeLanguage.TYPESCRIPT],
                "test_types": [TestType.UNIT, TestType.INTEGRATION]
            },
            "vitest": {
                "command": "npx vitest run",
                "coverage_command": "npx vitest run --coverage",
                "config_file": "vitest.config.ts",
                "test_pattern": "**/*.test.{js,ts,jsx,tsx}",
                "coverage_threshold": 80,
                "supports_parallel": True,
                "supports_watch": True,
                "languages": [CodeLanguage.JAVASCRIPT, CodeLanguage.TYPESCRIPT],
                "test_types": [TestType.UNIT, TestType.INTEGRATION]
            },
            "playwright": {
                "command": "npx playwright test",
                "config_file": "playwright.config.ts",
                "test_pattern": "**/*.spec.{js,ts}",
                "browsers": ["chromium", "firefox", "webkit"],
                "supports_parallel": True,
                "supports_headless": True,
                "languages": [CodeLanguage.JAVASCRIPT, CodeLanguage.TYPESCRIPT],
                "test_types": [TestType.E2E, TestType.INTEGRATION]
            },
            "cypress": {
                "command": "npx cypress run",
                "config_file": "cypress.config.js",
                "test_pattern": "cypress/e2e/**/*.cy.{js,ts}",
                "supports_parallel": True,
                "supports_headless": True,
                "languages": [CodeLanguage.JAVASCRIPT, CodeLanguage.TYPESCRIPT],
                "test_types": [TestType.E2E, TestType.INTEGRATION]
            },
            "mocha": {
                "command": "npx mocha",
                "config_file": ".mocharc.json",
                "test_pattern": "test/**/*.js",
                "supports_parallel": True,
                "supports_watch": True,
                "languages": [CodeLanguage.JAVASCRIPT],
                "test_types": [TestType.UNIT, TestType.INTEGRATION]
            },

            # Python Frameworks
            "pytest": {
                "command": "pytest",
                "coverage_command": "pytest --cov",
                "config_file": "pytest.ini",
                "test_pattern": "test_*.py",
                "coverage_threshold": 80,
                "supports_parallel": True,
                "supports_markers": True,
                "languages": [CodeLanguage.PYTHON],
                "test_types": [TestType.UNIT, TestType.INTEGRATION, TestType.API]
            },
            "unittest": {
                "command": "python -m unittest discover",
                "coverage_command": "coverage run -m unittest discover && coverage report",
                "test_pattern": "test_*.py",
                "languages": [CodeLanguage.PYTHON],
                "test_types": [TestType.UNIT]
            },

            # Java Frameworks
            "junit5": {
                "command": "mvn test",
                "coverage_command": "mvn test jacoco:report",
                "config_file": "pom.xml",
                "test_pattern": "**/*Test.java",
                "languages": [CodeLanguage.JAVA],
                "test_types": [TestType.UNIT, TestType.INTEGRATION]
            },
            "testng": {
                "command": "mvn test",
                "config_file": "testng.xml",
                "test_pattern": "**/*Test.java",
                "languages": [CodeLanguage.JAVA],
                "test_types": [TestType.UNIT, TestType.INTEGRATION]
            },

            # C# Frameworks
            "nunit": {
                "command": "dotnet test",
                "coverage_command": "dotnet test --collect:\"XPlat Code Coverage\"",
                "test_pattern": "**/*Tests.cs",
                "languages": [CodeLanguage.CSHARP],
                "test_types": [TestType.UNIT, TestType.INTEGRATION]
            },
            "xunit": {
                "command": "dotnet test",
                "coverage_command": "dotnet test --collect:\"XPlat Code Coverage\"",
                "test_pattern": "**/*Tests.cs",
                "languages": [CodeLanguage.CSHARP],
                "test_types": [TestType.UNIT, TestType.INTEGRATION]
            },

            # Go Frameworks
            "go_test": {
                "command": "go test ./...",
                "coverage_command": "go test -cover ./...",
                "test_pattern": "*_test.go",
                "languages": [CodeLanguage.GO],
                "test_types": [TestType.UNIT, TestType.INTEGRATION]
            },

            # Rust Frameworks
            "cargo_test": {
                "command": "cargo test",
                "coverage_command": "cargo tarpaulin --out Html",
                "test_pattern": "**/*_test.rs",
                "languages": [CodeLanguage.RUST],
                "test_types": [TestType.UNIT, TestType.INTEGRATION]
            },

            # PHP Frameworks
            "phpunit": {
                "command": "vendor/bin/phpunit",
                "coverage_command": "vendor/bin/phpunit --coverage-html coverage",
                "config_file": "phpunit.xml",
                "test_pattern": "**/*Test.php",
                "languages": [CodeLanguage.PHP],
                "test_types": [TestType.UNIT, TestType.INTEGRATION]
            },

            # Ruby Frameworks
            "rspec": {
                "command": "bundle exec rspec",
                "coverage_command": "bundle exec rspec --format documentation",
                "config_file": ".rspec",
                "test_pattern": "**/*_spec.rb",
                "languages": [CodeLanguage.RUBY],
                "test_types": [TestType.UNIT, TestType.INTEGRATION]
            }
        }

    def _initialize_test_generators(self) -> Dict[str, Dict[str, Any]]:
        """Initialize comprehensive test generation configurations"""
        return {
            "javascript": {
                "unit_test_template": """
describe('{module_name}', () => {{
  {test_cases}
}});""",
                "test_case_template": """
  test('{test_name}', {async_keyword}() => {{
    {setup}
    {test_body}
    {assertions}
    {cleanup}
  }});""",
                "mock_template": """
jest.mock('{module_path}', () => ({{
  {mock_implementation}
}}));""",
                "edge_case_template": """
  test.each([
    {test_data}
  ])('{test_name} - %s', {async_keyword}(input, expected) => {{
    {test_body}
    expect(result).toBe(expected);
  }});""",
                "error_case_template": """
  test('{test_name} - should throw error', () => {{
    expect(() => {{
      {test_body}
    }}).toThrow('{expected_error}');
  }});"""
            },
            "typescript": {
                "unit_test_template": """
import {{ {imports} }} from '{module_path}';
import {{ jest }} from '@jest/globals';

describe('{module_name}', () => {{
  {test_cases}
}});""",
                "test_case_template": """
  test('{test_name}', {async_keyword}() => {{
    {setup}
    {test_body}
    {assertions}
    {cleanup}
  }});""",
                "interface_mock_template": """
const mock{interface_name}: jest.Mocked<{interface_name}> = {{
  {mock_properties}
}};""",
                "type_test_template": """
  test('{test_name} - type safety', () => {{
    const result: {expected_type} = {function_call};
    expect(typeof result).toBe('{type_check}');
  }});"""
            },
            "python": {
                "unit_test_template": """
import unittest
from unittest.mock import Mock, patch, MagicMock
import pytest
from {module_path} import {imports}

class Test{class_name}(unittest.TestCase):

    def setUp(self):
        {setup_code}

    def tearDown(self):
        {teardown_code}

    {test_methods}
""",
                "test_method_template": """
    def test_{test_name}(self):
        \"\"\"Test {test_description}\"\"\"
        {setup}
        {test_body}
        {assertions}
        {cleanup}""",
                "parametrized_test_template": """
    @pytest.mark.parametrize("input_data,expected", [
        {test_parameters}
    ])
    def test_{test_name}_parametrized(self, input_data, expected):
        \"\"\"Parametrized test for {test_description}\"\"\"
        result = {function_call}(input_data)
        assert result == expected""",
                "async_test_template": """
    @pytest.mark.asyncio
    async def test_{test_name}_async(self):
        \"\"\"Async test for {test_description}\"\"\"
        {setup}
        result = await {function_call}({test_args})
        {assertions}"""
            },
            "java": {
                "unit_test_template": """
package {package_name};

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@DisplayName("{class_name} Tests")
class {class_name}Test {{

    @Mock
    private {dependency_type} {dependency_name};

    private {class_name} {instance_name};

    @BeforeEach
    void setUp() {{
        MockitoAnnotations.openMocks(this);
        {setup_code}
    }}

    @AfterEach
    void tearDown() {{
        {teardown_code}
    }}

    {test_methods}
}}""",
                "test_method_template": """
    @Test
    @DisplayName("{test_description}")
    void {test_name}() {{
        // Given
        {given_code}

        // When
        {when_code}

        // Then
        {then_code}
    }}"""
            },
            "csharp": {
                "unit_test_template": """
using System;
using Xunit;
using Moq;
using FluentAssertions;
using {namespace_under_test};

namespace {test_namespace}
{{
    public class {class_name}Tests
    {{
        private readonly Mock<{dependency_interface}> _{dependency_name}Mock;
        private readonly {class_name} _sut;

        public {class_name}Tests()
        {{
            _{dependency_name}Mock = new Mock<{dependency_interface}>();
            _sut = new {class_name}(_{dependency_name}Mock.Object);
        }}

        {test_methods}
    }}
}}""",
                "test_method_template": """
        [Fact]
        public void {test_name}()
        {{
            // Arrange
            {arrange_code}

            // Act
            {act_code}

            // Assert
            {assert_code}
        }}"""
            },
            "go": {
                "unit_test_template": """
package {package_name}

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
)

{test_functions}""",
                "test_function_template": """
func Test{function_name}(t *testing.T) {{
    // Arrange
    {arrange_code}

    // Act
    {act_code}

    // Assert
    {assert_code}
}}"""
            },
            "rust": {
                "unit_test_template": """
#[cfg(test)]
mod tests {{
    use super::*;

    {test_functions}
}}""",
                "test_function_template": """
    #[test]
    fn test_{function_name}() {{
        // Arrange
        {arrange_code}

        // Act
        {act_code}

        // Assert
        {assert_code}
    }}"""
            }
        }

    def _initialize_security_scanners(self) -> Dict[str, Dict[str, Any]]:
        """Initialize security scanning configurations"""
        return {
            "eslint-security": {
                "command": "npx eslint --ext .js,.ts --config .eslintrc-security.js",
                "config_file": ".eslintrc-security.js",
                "rules": ["security/detect-object-injection", "security/detect-non-literal-fs-filename"]
            },
            "bandit": {
                "command": "bandit -r .",
                "config_file": ".bandit",
                "format": "json"
            },
            "semgrep": {
                "command": "semgrep --config=auto --json",
                "supports_multiple_languages": True
            },
            "snyk": {
                "command": "snyk test --json",
                "supports_dependencies": True
            },
            "npm-audit": {
                "command": "npm audit --json",
                "focuses_on": "dependencies"
            }
        }

    def _initialize_performance_analyzers(self) -> Dict[str, Dict[str, Any]]:
        """Initialize performance analysis configurations"""
        return {
            "lighthouse": {
                "command": "npx lighthouse {url} --output json --output-path {output_path}",
                "metrics": ["performance", "accessibility", "best-practices", "seo"],
                "type": "web_performance"
            },
            "webpack-bundle-analyzer": {
                "command": "npx webpack-bundle-analyzer build/static/js/*.js --mode static",
                "type": "bundle_analysis"
            },
            "clinic": {
                "command": "npx clinic doctor -- node {entry_point}",
                "type": "node_performance"
            },
            "py-spy": {
                "command": "py-spy record -o profile.svg -- python {script}",
                "type": "python_profiling"
            }
        }

    def _initialize_test_templates(self) -> Dict[str, str]:
        """Initialize test generation templates"""
        return {
            "react_component": """
import React from 'react';
import {{ render, screen, fireEvent }} from '@testing-library/react';
import {{ {component_name} }} from './{component_file}';

describe('{component_name}', () => {{
  test('renders without crashing', () => {{
    render(<{component_name} />);
  }});

  test('displays correct content', () => {{
    render(<{component_name} {props} />);
    expect(screen.getByText('{expected_text}')).toBeInTheDocument();
  }});

  test('handles user interactions', () => {{
    const mockHandler = jest.fn();
    render(<{component_name} onClick={{mockHandler}} />);
    fireEvent.click(screen.getByRole('button'));
    expect(mockHandler).toHaveBeenCalled();
  }});
}});""",
            "api_endpoint": """
import request from 'supertest';
import app from '../app';

describe('{endpoint_path}', () => {{
  test('GET {endpoint_path} returns 200', async () => {{
    const response = await request(app)
      .get('{endpoint_path}')
      .expect(200);

    expect(response.body).toHaveProperty('{expected_property}');
  }});

  test('POST {endpoint_path} creates resource', async () => {{
    const newData = {{ {test_data} }};
    const response = await request(app)
      .post('{endpoint_path}')
      .send(newData)
      .expect(201);

    expect(response.body).toMatchObject(newData);
  }});

  test('handles validation errors', async () => {{
    const invalidData = {{ {invalid_data} }};
    await request(app)
      .post('{endpoint_path}')
      .send(invalidData)
      .expect(400);
  }});
}});""",
            "utility_function": """
import {{ {function_name} }} from './{module_file}';

describe('{function_name}', () => {{
  test('returns expected result for valid input', () => {{
    const result = {function_name}({valid_input});
    expect(result).toBe({expected_output});
  }});

  test('handles edge cases', () => {{
    expect({function_name}({edge_case_input})).toBe({edge_case_output});
  }});

  test('throws error for invalid input', () => {{
    expect(() => {function_name}({invalid_input})).toThrow('{expected_error}');
  }});
}});"""
        }

    def _initialize_quality_gates(self) -> Dict[str, Dict[str, Any]]:
        """Initialize quality gate configurations"""
        return {
            QualityLevel.BASIC.value: {
                "min_coverage": 60.0,
                "max_critical_issues": 5,
                "max_high_issues": 10,
                "required_tests": ["unit"]
            },
            QualityLevel.STANDARD.value: {
                "min_coverage": 80.0,
                "max_critical_issues": 2,
                "max_high_issues": 5,
                "required_tests": ["unit", "integration"]
            },
            QualityLevel.COMPREHENSIVE.value: {
                "min_coverage": 90.0,
                "max_critical_issues": 0,
                "max_high_issues": 2,
                "required_tests": ["unit", "integration", "e2e"]
            },
            QualityLevel.ENTERPRISE.value: {
                "min_coverage": 95.0,
                "max_critical_issues": 0,
                "max_high_issues": 0,
                "required_tests": ["unit", "integration", "e2e", "performance", "security"]
            }
        }

    def _initialize_security_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Initialize security vulnerability patterns"""
        return {
            "sql_injection": {
                "patterns": [
                    r"query\s*\+\s*['\"]",
                    r"execute\s*\(\s*['\"][^'\"]*['\"]?\s*\+",
                    r"SELECT\s+.*\s+FROM\s+.*\s+WHERE\s+.*\+",
                ],
                "severity": SecuritySeverity.HIGH,
                "description": "Potential SQL injection vulnerability",
                "recommendation": "Use parameterized queries or prepared statements"
            },
            "xss": {
                "patterns": [
                    r"innerHTML\s*=\s*.*\+",
                    r"document\.write\s*\(",
                    r"eval\s*\(",
                    r"dangerouslySetInnerHTML"
                ],
                "severity": SecuritySeverity.MEDIUM,
                "description": "Potential XSS vulnerability",
                "recommendation": "Sanitize user input and use safe DOM manipulation methods"
            },
            "path_traversal": {
                "patterns": [
                    r"\.\.\/",
                    r"\.\.\\",
                    r"path\.join\s*\([^)]*req\.",
                    r"fs\.readFile\s*\([^)]*req\."
                ],
                "severity": SecuritySeverity.HIGH,
                "description": "Potential path traversal vulnerability",
                "recommendation": "Validate and sanitize file paths"
            },
            "hardcoded_secrets": {
                "patterns": [
                    r"password\s*=\s*['\"][^'\"]{8,}['\"]",
                    r"api_key\s*=\s*['\"][^'\"]{16,}['\"]",
                    r"secret\s*=\s*['\"][^'\"]{16,}['\"]",
                    r"token\s*=\s*['\"][^'\"]{20,}['\"]"
                ],
                "severity": SecuritySeverity.CRITICAL,
                "description": "Hardcoded secrets detected",
                "recommendation": "Use environment variables or secure secret management"
            },
            "weak_crypto": {
                "patterns": [
                    r"md5\s*\(",
                    r"sha1\s*\(",
                    r"DES\s*\(",
                    r"RC4\s*\("
                ],
                "severity": SecuritySeverity.MEDIUM,
                "description": "Weak cryptographic algorithm",
                "recommendation": "Use strong cryptographic algorithms like SHA-256 or AES"
            }
        }

    def _initialize_performance_benchmarks(self) -> Dict[str, Dict[str, Any]]:
        """Initialize performance benchmarks"""
        return {
            "web_vitals": {
                "first_contentful_paint": {"good": 1.8, "needs_improvement": 3.0},
                "largest_contentful_paint": {"good": 2.5, "needs_improvement": 4.0},
                "first_input_delay": {"good": 100, "needs_improvement": 300},
                "cumulative_layout_shift": {"good": 0.1, "needs_improvement": 0.25}
            },
            "api_performance": {
                "response_time": {"good": 200, "needs_improvement": 500},
                "throughput": {"good": 1000, "needs_improvement": 500},
                "error_rate": {"good": 0.01, "needs_improvement": 0.05}
            },
            "database_performance": {
                "query_time": {"good": 50, "needs_improvement": 200},
                "connection_pool_usage": {"good": 0.7, "needs_improvement": 0.9},
                "index_usage": {"good": 0.9, "needs_improvement": 0.7}
            },
            "memory_usage": {
                "heap_usage": {"good": 0.7, "needs_improvement": 0.9},
                "memory_leaks": {"good": 0, "needs_improvement": 1},
                "gc_frequency": {"good": 10, "needs_improvement": 50}
            }
        }

    def _initialize_validation_rules(self) -> Dict[str, List[str]]:
        """Initialize validation rules for different aspects"""
        return {
            "code_quality": [
                "No syntax errors",
                "Consistent code style",
                "Proper error handling",
                "Appropriate logging",
                "Security best practices"
            ],
            "test_quality": [
                "Test coverage meets threshold",
                "Tests are independent",
                "Tests have clear assertions",
                "Edge cases are covered",
                "Mock usage is appropriate"
            ],
            "architecture": [
                "Follows specified architecture pattern",
                "Proper separation of concerns",
                "Dependency injection used correctly",
                "Configuration externalized",
                "Scalability considerations"
            ],
            "security": [
                "Input validation implemented",
                "Authentication/authorization present",
                "Sensitive data protection",
                "HTTPS configuration",
                "Security headers configured"
            ]
        }

    async def _validate_qa_inputs(self, context: QAContext):
        """Validate QA inputs and setup"""
        if not context.project_path.exists():
            raise ValidationError(f"Project path does not exist: {context.project_path}")

        # Check for package.json or requirements.txt
        has_package_json = (context.project_path / "package.json").exists()
        has_requirements = (context.project_path / "requirements.txt").exists()

        if not has_package_json and not has_requirements:
            raise ValidationError("No package.json or requirements.txt found")

        logger.info("QA input validation passed")

    async def _analyze_project_structure(self, context: QAContext):
        """Analyze project structure and identify test files"""
        project_files = []
        test_files = []

        for file_path in context.project_path.rglob("*"):
            if file_path.is_file():
                if any(pattern in file_path.name for pattern in ["test", "spec"]):
                    test_files.append(file_path)
                elif file_path.suffix in [".js", ".ts", ".jsx", ".tsx", ".py"]:
                    project_files.append(file_path)

        context.test_config["project_files"] = project_files
        context.test_config["test_files"] = test_files

        logger.info(f"Found {len(project_files)} source files and {len(test_files)} test files")

    async def _generate_missing_tests(self, context: QAContext) -> List[GeneratedTest]:
        """Generate missing tests for source files"""
        logger.info("Generating missing tests...")

        generated_tests = []
        project_files = context.test_config.get("project_files", [])
        test_files = context.test_config.get("test_files", [])

        # Identify source files without corresponding tests
        untested_files = self._find_untested_files(project_files, test_files)

        for source_file in untested_files:
            try:
                # Analyze source file to understand its structure
                file_analysis = await self._analyze_source_file(source_file, context)

                # Generate tests based on file analysis
                file_tests = await self._generate_tests_for_file(file_analysis, context)
                generated_tests.extend(file_tests)

            except Exception as e:
                logger.warning(f"Failed to generate tests for {source_file}: {e}")

        logger.info(f"Generated {len(generated_tests)} test cases")
        return generated_tests

    def _find_untested_files(self, project_files: List[Path], test_files: List[Path]) -> List[Path]:
        """Find source files that don't have corresponding test files"""
        untested_files = []
        test_file_names = {self._get_source_name_from_test(tf) for tf in test_files}

        for source_file in project_files:
            source_name = source_file.stem
            if source_name not in test_file_names:
                # Skip certain files that typically don't need tests
                if not self._should_skip_testing(source_file):
                    untested_files.append(source_file)

        return untested_files

    def _get_source_name_from_test(self, test_file: Path) -> str:
        """Extract source file name from test file name"""
        name = test_file.stem
        # Remove common test suffixes
        for suffix in ['.test', '.spec', '_test', '_spec']:
            if name.endswith(suffix):
                name = name[:-len(suffix)]
                break
        return name

    def _should_skip_testing(self, source_file: Path) -> bool:
        """Determine if a source file should be skipped for testing"""
        skip_patterns = [
            'config', 'index', 'main', 'app', 'server',
            'types', 'interfaces', 'constants', 'enums'
        ]

        # Skip files in certain directories
        skip_dirs = ['node_modules', 'dist', 'build', '.git', 'coverage']
        if any(skip_dir in str(source_file) for skip_dir in skip_dirs):
            return True

        # Skip files with certain patterns
        if any(pattern in source_file.stem.lower() for pattern in skip_patterns):
            return True

        return False

    async def _analyze_source_file(self, source_file: Path, context: QAContext) -> Dict[str, Any]:
        """Analyze source file to understand its structure"""
        try:
            with open(source_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Determine language
            language = self._detect_language(source_file)

            # Extract functions, classes, and exports
            if language in [CodeLanguage.JAVASCRIPT, CodeLanguage.TYPESCRIPT]:
                analysis = self._analyze_js_ts_file(content, source_file)
            elif language == CodeLanguage.PYTHON:
                analysis = self._analyze_python_file(content, source_file)
            else:
                analysis = self._analyze_generic_file(content, source_file)

            analysis['language'] = language
            analysis['file_path'] = source_file

            return analysis

        except Exception as e:
            logger.error(f"Failed to analyze {source_file}: {e}")
            return {
                'language': CodeLanguage.JAVASCRIPT,
                'file_path': source_file,
                'functions': [],
                'classes': [],
                'exports': []
            }

    def _detect_language(self, file_path: Path) -> CodeLanguage:
        """Detect programming language from file extension"""
        extension = file_path.suffix.lower()

        language_map = {
            '.js': CodeLanguage.JAVASCRIPT,
            '.jsx': CodeLanguage.JAVASCRIPT,
            '.ts': CodeLanguage.TYPESCRIPT,
            '.tsx': CodeLanguage.TYPESCRIPT,
            '.py': CodeLanguage.PYTHON,
            '.java': CodeLanguage.JAVA,
            '.cs': CodeLanguage.CSHARP,
            '.go': CodeLanguage.GO,
            '.rs': CodeLanguage.RUST
        }

        return language_map.get(extension, CodeLanguage.JAVASCRIPT)

    def _analyze_js_ts_file(self, content: str, file_path: Path) -> Dict[str, Any]:
        """Analyze JavaScript/TypeScript file"""
        analysis = {
            'functions': [],
            'classes': [],
            'exports': [],
            'imports': []
        }

        # Extract function declarations
        function_patterns = [
            r'function\s+(\w+)\s*\([^)]*\)',
            r'const\s+(\w+)\s*=\s*\([^)]*\)\s*=>',
            r'(\w+)\s*:\s*\([^)]*\)\s*=>'
        ]

        for pattern in function_patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                analysis['functions'].append({
                    'name': match.group(1),
                    'line': content[:match.start()].count('\n') + 1
                })

        # Extract class declarations
        class_pattern = r'class\s+(\w+)'
        matches = re.finditer(class_pattern, content)
        for match in matches:
            analysis['classes'].append({
                'name': match.group(1),
                'line': content[:match.start()].count('\n') + 1
            })

        # Extract exports
        export_patterns = [
            r'export\s+(?:default\s+)?(?:function\s+)?(\w+)',
            r'export\s*\{\s*([^}]+)\s*\}',
            r'module\.exports\s*=\s*(\w+)'
        ]

        for pattern in export_patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                if '{' in match.group(0):
                    # Handle named exports
                    exports = [name.strip() for name in match.group(1).split(',')]
                    analysis['exports'].extend(exports)
                else:
                    analysis['exports'].append(match.group(1))

        return analysis

    def _analyze_python_file(self, content: str, file_path: Path) -> Dict[str, Any]:
        """Analyze Python file"""
        analysis = {
            'functions': [],
            'classes': [],
            'exports': [],
            'imports': []
        }

        try:
            import ast
            tree = ast.parse(content)

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    analysis['functions'].append({
                        'name': node.name,
                        'line': node.lineno,
                        'args': [arg.arg for arg in node.args.args]
                    })
                elif isinstance(node, ast.ClassDef):
                    analysis['classes'].append({
                        'name': node.name,
                        'line': node.lineno
                    })
                elif isinstance(node, ast.Import):
                    for alias in node.names:
                        analysis['imports'].append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        for alias in node.names:
                            analysis['imports'].append(f"{node.module}.{alias.name}")

        except SyntaxError as e:
            logger.warning(f"Syntax error in {file_path}: {e}")

        return analysis

    def _analyze_generic_file(self, content: str, file_path: Path) -> Dict[str, Any]:
        """Analyze generic file using regex patterns"""
        analysis = {
            'functions': [],
            'classes': [],
            'exports': [],
            'imports': []
        }

        # Basic function detection
        function_patterns = [
            r'def\s+(\w+)\s*\(',
            r'function\s+(\w+)\s*\(',
            r'(\w+)\s*\([^)]*\)\s*\{'
        ]

        for pattern in function_patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                analysis['functions'].append({
                    'name': match.group(1),
                    'line': content[:match.start()].count('\n') + 1
                })

        return analysis

    async def _generate_tests_for_file(self, file_analysis: Dict[str, Any], context: QAContext) -> List[GeneratedTest]:
        """Generate tests for a specific file"""
        generated_tests = []
        language = file_analysis['language']
        file_path = file_analysis['file_path']

        # Generate tests for functions
        for func in file_analysis['functions']:
            test = await self._generate_function_test(func, file_analysis, context)
            if test:
                generated_tests.append(test)

        # Generate tests for classes
        for cls in file_analysis['classes']:
            test = await self._generate_class_test(cls, file_analysis, context)
            if test:
                generated_tests.append(test)

        return generated_tests

    async def _generate_function_test(self, function_info: Dict[str, Any], file_analysis: Dict[str, Any], context: QAContext) -> Optional[GeneratedTest]:
        """Generate test for a specific function"""
        try:
            language = file_analysis['language']
            function_name = function_info['name']
            file_path = file_analysis['file_path']

            # Skip private functions
            if function_name.startswith('_'):
                return None

            # Generate test using AI if available
            if context.test_generation_context and context.test_generation_context.generation_strategy == TestGenerationStrategy.AI_ENHANCED:
                test_code = await self._generate_ai_enhanced_test(function_info, file_analysis, context)
            else:
                test_code = self._generate_template_based_test(function_info, file_analysis, context)

            return GeneratedTest(
                test_name=f"test_{function_name}",
                test_code=test_code,
                test_type=TestType.UNIT,
                target_function=function_name,
                target_file=str(file_path),
                language=language,
                dependencies=self._extract_test_dependencies(file_analysis)
            )

        except Exception as e:
            logger.error(f"Failed to generate test for function {function_info['name']}: {e}")
            return None

    async def _generate_class_test(self, class_info: Dict[str, Any], file_analysis: Dict[str, Any], context: QAContext) -> Optional[GeneratedTest]:
        """Generate test for a specific class"""
        try:
            language = file_analysis['language']
            class_name = class_info['name']
            file_path = file_analysis['file_path']

            # Generate test using AI if available
            if context.test_generation_context and context.test_generation_context.generation_strategy == TestGenerationStrategy.AI_ENHANCED:
                test_code = await self._generate_ai_enhanced_class_test(class_info, file_analysis, context)
            else:
                test_code = self._generate_template_based_class_test(class_info, file_analysis, context)

            return GeneratedTest(
                test_name=f"test_{class_name}",
                test_code=test_code,
                test_type=TestType.UNIT,
                target_function=class_name,
                target_file=str(file_path),
                language=language,
                dependencies=self._extract_test_dependencies(file_analysis)
            )

        except Exception as e:
            logger.error(f"Failed to generate test for class {class_info['name']}: {e}")
            return None

    def _extract_test_dependencies(self, file_analysis: Dict[str, Any]) -> List[str]:
        """Extract dependencies needed for testing"""
        dependencies = []
        language = file_analysis['language']

        if language in [CodeLanguage.JAVASCRIPT, CodeLanguage.TYPESCRIPT]:
            dependencies.extend(['jest', '@testing-library/jest-dom'])
            if 'react' in str(file_analysis['file_path']).lower():
                dependencies.extend(['@testing-library/react', '@testing-library/user-event'])
        elif language == CodeLanguage.PYTHON:
            dependencies.extend(['pytest', 'unittest.mock'])

        return dependencies

    async def _generate_ai_enhanced_test(self, function_info: Dict[str, Any], file_analysis: Dict[str, Any], context: QAContext) -> str:
        """Generate AI-enhanced test using language model"""
        try:
            # Prepare prompt for AI test generation
            prompt = self._create_test_generation_prompt(function_info, file_analysis, context)

            # Use API manager to generate test
            response = await self.api_manager.generate_completion(
                prompt=prompt,
                max_tokens=1000,
                temperature=0.3
            )

            if response and response.get('success'):
                return response['content']
            else:
                # Fallback to template-based generation
                return self._generate_template_based_test(function_info, file_analysis, context)

        except Exception as e:
            logger.warning(f"AI test generation failed, using template: {e}")
            return self._generate_template_based_test(function_info, file_analysis, context)

    def _create_test_generation_prompt(self, function_info: Dict[str, Any], file_analysis: Dict[str, Any], context: QAContext) -> str:
        """Create prompt for AI test generation"""
        language = file_analysis['language'].value
        function_name = function_info['name']

        prompt = f"""
Generate comprehensive unit tests for the following {language} function:

Function: {function_name}
File: {file_analysis['file_path']}

Requirements:
1. Test normal operation with valid inputs
2. Test edge cases and boundary conditions
3. Test error handling with invalid inputs
4. Include proper setup and teardown if needed
5. Use appropriate mocking for external dependencies
6. Follow {language} testing best practices

Generate only the test code without explanations.
"""

        return prompt

    def _generate_template_based_test(self, function_info: Dict[str, Any], file_analysis: Dict[str, Any], context: QAContext) -> str:
        """Generate test using templates"""
        language = file_analysis['language']
        function_name = function_info['name']

        if language in [CodeLanguage.JAVASCRIPT, CodeLanguage.TYPESCRIPT]:
            return self._generate_js_ts_function_test(function_info, file_analysis)
        elif language == CodeLanguage.PYTHON:
            return self._generate_python_function_test(function_info, file_analysis)
        else:
            return self._generate_generic_function_test(function_info, file_analysis)

    def _generate_js_ts_function_test(self, function_info: Dict[str, Any], file_analysis: Dict[str, Any]) -> str:
        """Generate JavaScript/TypeScript function test"""
        function_name = function_info['name']
        file_path = file_analysis['file_path']

        # Determine import path
        import_path = f"./{file_path.stem}"

        template = self.test_templates.get('utility_function', '')

        return template.format(
            function_name=function_name,
            module_file=file_path.stem,
            valid_input="'test input'",
            expected_output="'expected result'",
            edge_case_input="''",
            edge_case_output="''",
            invalid_input="null",
            expected_error="Error"
        )

    def _generate_python_function_test(self, function_info: Dict[str, Any], file_analysis: Dict[str, Any]) -> str:
        """Generate Python function test"""
        function_name = function_info['name']
        file_path = file_analysis['file_path']

        module_path = file_path.stem

        return f"""
import unittest
from unittest.mock import Mock, patch
from {module_path} import {function_name}

class Test{function_name.title()}(unittest.TestCase):

    def test_{function_name}_valid_input(self):
        # Test with valid input
        result = {function_name}('test_input')
        self.assertIsNotNone(result)

    def test_{function_name}_edge_cases(self):
        # Test edge cases
        result = {function_name}('')
        self.assertEqual(result, '')

    def test_{function_name}_invalid_input(self):
        # Test error handling
        with self.assertRaises(ValueError):
            {function_name}(None)

if __name__ == '__main__':
    unittest.main()
"""

    def _generate_generic_function_test(self, function_info: Dict[str, Any], file_analysis: Dict[str, Any]) -> str:
        """Generate generic function test"""
        function_name = function_info['name']

        return f"""
// Test for {function_name}
describe('{function_name}', () => {{
    test('should work with valid input', () => {{
        // Add test implementation
        expect(true).toBe(true);
    }});

    test('should handle edge cases', () => {{
        // Add edge case tests
        expect(true).toBe(true);
    }});

    test('should handle errors', () => {{
        // Add error handling tests
        expect(true).toBe(true);
    }});
}});
"""

    async def _generate_ai_enhanced_class_test(self, class_info: Dict[str, Any], file_analysis: Dict[str, Any], context: QAContext) -> str:
        """Generate AI-enhanced class test"""
        try:
            prompt = self._create_class_test_generation_prompt(class_info, file_analysis, context)

            response = await self.api_manager.generate_completion(
                prompt=prompt,
                max_tokens=1500,
                temperature=0.3
            )

            if response and response.get('success'):
                return response['content']
            else:
                return self._generate_template_based_class_test(class_info, file_analysis, context)

        except Exception as e:
            logger.warning(f"AI class test generation failed, using template: {e}")
            return self._generate_template_based_class_test(class_info, file_analysis, context)

    def _create_class_test_generation_prompt(self, class_info: Dict[str, Any], file_analysis: Dict[str, Any], context: QAContext) -> str:
        """Create prompt for AI class test generation"""
        language = file_analysis['language'].value
        class_name = class_info['name']

        prompt = f"""
Generate comprehensive unit tests for the following {language} class:

Class: {class_name}
File: {file_analysis['file_path']}

Requirements:
1. Test class instantiation
2. Test all public methods
3. Test property getters and setters
4. Test inheritance if applicable
5. Include proper setup and teardown
6. Use appropriate mocking for dependencies
7. Follow {language} testing best practices

Generate only the test code without explanations.
"""

        return prompt

    def _generate_template_based_class_test(self, class_info: Dict[str, Any], file_analysis: Dict[str, Any], context: QAContext) -> str:
        """Generate class test using templates"""
        language = file_analysis['language']
        class_name = class_info['name']

        if language in [CodeLanguage.JAVASCRIPT, CodeLanguage.TYPESCRIPT]:
            return self._generate_js_ts_class_test(class_info, file_analysis)
        elif language == CodeLanguage.PYTHON:
            return self._generate_python_class_test(class_info, file_analysis)
        else:
            return self._generate_generic_class_test(class_info, file_analysis)

    def _generate_js_ts_class_test(self, class_info: Dict[str, Any], file_analysis: Dict[str, Any]) -> str:
        """Generate JavaScript/TypeScript class test"""
        class_name = class_info['name']
        file_path = file_analysis['file_path']

        return f"""
import {{ {class_name} }} from './{file_path.stem}';

describe('{class_name}', () => {{
    let instance: {class_name};

    beforeEach(() => {{
        instance = new {class_name}();
    }});

    test('should instantiate correctly', () => {{
        expect(instance).toBeInstanceOf({class_name});
    }});

    test('should have expected properties', () => {{
        // Add property tests
        expect(instance).toBeDefined();
    }});

    test('should have expected methods', () => {{
        // Add method tests
        expect(typeof instance.someMethod).toBe('function');
    }});
}});
"""

    def _generate_python_class_test(self, class_info: Dict[str, Any], file_analysis: Dict[str, Any]) -> str:
        """Generate Python class test"""
        class_name = class_info['name']
        file_path = file_analysis['file_path']

        return f"""
import unittest
from unittest.mock import Mock, patch
from {file_path.stem} import {class_name}

class Test{class_name}(unittest.TestCase):

    def setUp(self):
        self.instance = {class_name}()

    def test_instantiation(self):
        self.assertIsInstance(self.instance, {class_name})

    def test_methods_exist(self):
        # Test that expected methods exist
        self.assertTrue(hasattr(self.instance, '__init__'))

    def tearDown(self):
        self.instance = None

if __name__ == '__main__':
    unittest.main()
"""

    def _generate_generic_class_test(self, class_info: Dict[str, Any], file_analysis: Dict[str, Any]) -> str:
        """Generate generic class test"""
        class_name = class_info['name']

        return f"""
// Test for {class_name}
describe('{class_name}', () => {{
    let instance;

    beforeEach(() => {{
        instance = new {class_name}();
    }});

    test('should instantiate correctly', () => {{
        expect(instance).toBeDefined();
    }});

    test('should have expected behavior', () => {{
        // Add behavior tests
        expect(true).toBe(true);
    }});
}});
"""

    async def _generate_functional_tests(self, context: QAContext) -> List[GeneratedTest]:
        """Generate functional tests based on requirements"""
        generated_tests = []

        if not context.project_specification or not context.project_specification.requirements:
            return generated_tests

        try:
            functional_requirements = context.project_specification.requirements.get("functional", [])

            for i, requirement in enumerate(functional_requirements):
                test_name = f"functional_requirement_{i+1}"
                test_code = self._generate_functional_test_code(requirement, context)

                generated_test = GeneratedTest(
                    test_name=test_name,
                    test_code=test_code,
                    test_type=TestType.UNIT,
                    target_function="functional_requirement",
                    target_file="requirements",
                    language=CodeLanguage.JAVASCRIPT,
                    dependencies=["@testing-library/react", "jest"]
                )
                generated_tests.append(generated_test)

        except Exception as e:
            logger.error(f"Failed to generate functional tests: {e}")

        return generated_tests

    async def _generate_edge_case_tests(self, context: QAContext) -> List[GeneratedTest]:
        """Generate edge case tests for boundary conditions"""
        generated_tests = []

        try:
            # Analyze project files to find functions that need edge case testing
            project_files = context.test_config.get("project_files", [])

            for file_path in project_files:
                if file_path.suffix in ['.js', '.ts', '.py']:
                    file_analysis = await self._analyze_source_file(file_path, context)

                    for func in file_analysis.get('functions', []):
                        edge_test_code = self._generate_edge_case_test_code(func, file_analysis, context)

                        generated_test = GeneratedTest(
                            test_name=f"edge_case_{func['name']}",
                            test_code=edge_test_code,
                            test_type=TestType.UNIT,
                            target_function=func['name'],
                            target_file=str(file_path),
                            language=file_analysis['language'],
                            dependencies=self._get_test_dependencies(file_analysis['language'])
                        )
                        generated_tests.append(generated_test)

        except Exception as e:
            logger.error(f"Failed to generate edge case tests: {e}")

        return generated_tests

    async def _generate_performance_tests(self, context: QAContext) -> List[GeneratedTest]:
        """Generate performance tests for critical paths"""
        generated_tests = []

        try:
            # Identify performance-critical functions
            critical_functions = await self._identify_critical_functions(context)

            for func_info in critical_functions:
                perf_test_code = self._generate_performance_test_code(func_info, context)

                generated_test = GeneratedTest(
                    test_name=f"performance_{func_info['name']}",
                    test_code=perf_test_code,
                    test_type=TestType.PERFORMANCE,
                    target_function=func_info['name'],
                    target_file=func_info['file'],
                    language=func_info['language'],
                    dependencies=self._get_performance_test_dependencies(func_info['language'])
                )
                generated_tests.append(generated_test)

        except Exception as e:
            logger.error(f"Failed to generate performance tests: {e}")

        return generated_tests

    async def _generate_security_tests(self, context: QAContext) -> List[GeneratedTest]:
        """Generate security tests for common vulnerabilities"""
        generated_tests = []

        try:
            # Generate tests for common security vulnerabilities
            security_test_cases = [
                ("sql_injection", "Test SQL injection protection"),
                ("xss_protection", "Test XSS protection"),
                ("csrf_protection", "Test CSRF protection"),
                ("authentication", "Test authentication mechanisms"),
                ("authorization", "Test authorization controls")
            ]

            for test_case, description in security_test_cases:
                security_test_code = self._generate_security_test_code(test_case, description, context)

                generated_test = GeneratedTest(
                    test_name=f"security_{test_case}",
                    test_code=security_test_code,
                    test_type=TestType.SECURITY,
                    target_function=test_case,
                    target_file="security",
                    language=CodeLanguage.JAVASCRIPT,
                    dependencies=["jest", "supertest", "@testing-library/react"]
                )
                generated_tests.append(generated_test)

        except Exception as e:
            logger.error(f"Failed to generate security tests: {e}")

        return generated_tests

    async def _generate_accessibility_tests(self, context: QAContext) -> List[GeneratedTest]:
        """Generate accessibility tests for UI components"""
        generated_tests = []

        try:
            # Find React/Vue components
            frontend_files = self._find_frontend_files(context)

            for file_path in frontend_files:
                if file_path.suffix in ['.jsx', '.tsx', '.vue']:
                    component_analysis = await self._analyze_component_file(file_path)

                    for component in component_analysis.get('components', []):
                        a11y_test_code = self._generate_accessibility_test_code(component, context)

                        generated_test = GeneratedTest(
                            test_name=f"accessibility_{component['name']}",
                            test_code=a11y_test_code,
                            test_type=TestType.ACCESSIBILITY,
                            target_function=component['name'],
                            target_file=str(file_path),
                            language=CodeLanguage.JAVASCRIPT,
                            dependencies=["@testing-library/react", "jest-axe", "@axe-core/react"]
                        )
                        generated_tests.append(generated_test)

        except Exception as e:
            logger.error(f"Failed to generate accessibility tests: {e}")

        return generated_tests

    async def _generate_load_tests(self, context: QAContext) -> List[GeneratedTest]:
        """Generate load tests for performance-critical applications"""
        generated_tests = []

        try:
            # Find API endpoints
            api_files = self._find_api_files(context)

            if api_files:
                load_test_code = self._generate_load_test_code(api_files, context)

                generated_test = GeneratedTest(
                    test_name="load_test_api_endpoints",
                    test_code=load_test_code,
                    test_type=TestType.LOAD,
                    target_function="api_endpoints",
                    target_file="api",
                    language=CodeLanguage.JAVASCRIPT,
                    dependencies=["k6", "artillery"]
                )
                generated_tests.append(generated_test)

        except Exception as e:
            logger.error(f"Failed to generate load tests: {e}")

        return generated_tests

    async def _generate_stress_tests(self, context: QAContext) -> List[GeneratedTest]:
        """Generate stress tests to find breaking points"""
        generated_tests = []

        try:
            stress_test_code = self._generate_stress_test_code(context)

            generated_test = GeneratedTest(
                test_name="stress_test_system",
                test_code=stress_test_code,
                test_type=TestType.STRESS,
                target_function="system",
                target_file="system",
                language=CodeLanguage.JAVASCRIPT,
                dependencies=["jest", "supertest"]
            )
            generated_tests.append(generated_test)

        except Exception as e:
            logger.error(f"Failed to generate stress tests: {e}")

        return generated_tests

    async def _generate_smoke_tests(self, context: QAContext) -> List[GeneratedTest]:
        """Generate smoke tests for basic functionality verification"""
        generated_tests = []

        try:
            smoke_test_code = self._generate_smoke_test_code(context)

            generated_test = GeneratedTest(
                test_name="smoke_test_basic_functionality",
                test_code=smoke_test_code,
                test_type=TestType.SMOKE,
                target_function="basic_functionality",
                target_file="smoke",
                language=CodeLanguage.JAVASCRIPT,
                dependencies=["jest", "supertest"]
            )
            generated_tests.append(generated_test)

        except Exception as e:
            logger.error(f"Failed to generate smoke tests: {e}")

        return generated_tests

    async def _write_generated_tests(self, generated_tests: List[GeneratedTest], context: QAContext):
        """Write generated tests to files"""
        logger.info(f"Writing {len(generated_tests)} generated tests...")

        test_dir = context.project_path / "tests" / "generated"
        test_dir.mkdir(parents=True, exist_ok=True)

        for test in generated_tests:
            try:
                # Determine test file name and extension
                if test.language in [CodeLanguage.JAVASCRIPT, CodeLanguage.TYPESCRIPT]:
                    extension = ".test.ts" if test.language == CodeLanguage.TYPESCRIPT else ".test.js"
                elif test.language == CodeLanguage.PYTHON:
                    extension = "_test.py"
                else:
                    extension = ".test.js"  # Default

                test_file_name = f"{test.target_function}{extension}"
                test_file_path = test_dir / test_file_name

                # Write test file
                with open(test_file_path, 'w', encoding='utf-8') as f:
                    f.write(test.test_code)

                logger.info(f"Generated test written to {test_file_path}")

            except Exception as e:
                logger.error(f"Failed to write test for {test.target_function}: {e}")

        # Update test config to include generated tests
        if "test_files" not in context.test_config:
            context.test_config["test_files"] = []

        context.test_config["test_files"].extend([
            test_dir / f"{test.target_function}.test.{test.language.value}"
            for test in generated_tests
        ])

    def _generate_functional_test_code(self, requirement: str, context: QAContext) -> str:
        """Generate functional test code for a requirement"""
        return f"""
describe('Functional Requirement: {requirement[:50]}...', () => {{
    test('should implement requirement functionality', async () => {{
        // Test implementation for: {requirement}
        // TODO: Implement specific test logic based on requirement
        expect(true).toBe(true); // Placeholder
    }});

    test('should handle edge cases for requirement', async () => {{
        // Test edge cases for: {requirement}
        expect(true).toBe(true); // Placeholder
    }});
}});"""

    def _generate_edge_case_test_code(self, func_info: Dict[str, Any], file_analysis: Dict[str, Any], context: QAContext) -> str:
        """Generate edge case test code for a function"""
        func_name = func_info.get('name', 'unknown')

        return f"""
describe('Edge Cases for {func_name}', () => {{
    test('should handle null input', () => {{
        expect(() => {func_name}(null)).not.toThrow();
    }});

    test('should handle undefined input', () => {{
        expect(() => {func_name}(undefined)).not.toThrow();
    }});

    test('should handle empty string input', () => {{
        expect(() => {func_name}('')).not.toThrow();
    }});

    test('should handle large input values', () => {{
        const largeInput = 'x'.repeat(10000);
        expect(() => {func_name}(largeInput)).not.toThrow();
    }});

    test('should handle negative numbers', () => {{
        expect(() => {func_name}(-1)).not.toThrow();
    }});
}});"""

    def _generate_performance_test_code(self, func_info: Dict[str, Any], context: QAContext) -> str:
        """Generate performance test code for a function"""
        func_name = func_info.get('name', 'unknown')

        return f"""
describe('Performance Tests for {func_name}', () => {{
    test('should execute within acceptable time limit', async () => {{
        const startTime = performance.now();

        // Execute function multiple times
        for (let i = 0; i < 1000; i++) {{
            await {func_name}(testData);
        }}

        const endTime = performance.now();
        const executionTime = endTime - startTime;

        // Should complete 1000 iterations in less than 1 second
        expect(executionTime).toBeLessThan(1000);
    }});

    test('should handle concurrent executions', async () => {{
        const promises = [];

        for (let i = 0; i < 10; i++) {{
            promises.push({func_name}(testData));
        }}

        const startTime = performance.now();
        await Promise.all(promises);
        const endTime = performance.now();

        expect(endTime - startTime).toBeLessThan(500);
    }});

    test('should not cause memory leaks', async () => {{
        const initialMemory = process.memoryUsage().heapUsed;

        for (let i = 0; i < 100; i++) {{
            await {func_name}(testData);
        }}

        // Force garbage collection if available
        if (global.gc) {{
            global.gc();
        }}

        const finalMemory = process.memoryUsage().heapUsed;
        const memoryIncrease = finalMemory - initialMemory;

        // Memory increase should be minimal
        expect(memoryIncrease).toBeLessThan(1024 * 1024); // Less than 1MB
    }});
}});"""

    def _generate_security_test_code(self, test_case: str, description: str, context: QAContext) -> str:
        """Generate security test code for a specific vulnerability"""
        if test_case == "sql_injection":
            return """
describe('SQL Injection Protection', () => {{
    test('should prevent SQL injection in user input', async () => {{
        const maliciousInput = "'; DROP TABLE users; --";

        // Test that malicious input is properly sanitized
        const result = await queryDatabase(maliciousInput);
        expect(result).not.toContain('DROP TABLE');
    }});

    test('should use parameterized queries', async () => {{
        const userInput = "<EMAIL>";
        const query = "SELECT * FROM users WHERE email = ?";

        // Verify parameterized query usage
        expect(query).toContain('?');
    }});
}});"""
        elif test_case == "xss_protection":
            return """
describe('XSS Protection', () => {{
    test('should sanitize user input to prevent XSS', () => {{
        const maliciousScript = '<script>alert("XSS")</script>';
        const sanitized = sanitizeInput(maliciousScript);

        expect(sanitized).not.toContain('<script>');
        expect(sanitized).not.toContain('alert');
    }});

    test('should escape HTML entities', () => {{
        const htmlInput = '<div>Hello & goodbye</div>';
        const escaped = escapeHtml(htmlInput);

        expect(escaped).toContain('&lt;');
        expect(escaped).toContain('&gt;');
        expect(escaped).toContain('&amp;');
    }});
}});"""
        else:
            return f"""
describe('{description}', () => {{
    test('should implement {test_case} protection', () => {{
        // TODO: Implement specific security test for {test_case}
        expect(true).toBe(true); // Placeholder
    }});
}});"""

    def _generate_accessibility_test_code(self, component_info: Dict[str, Any], context: QAContext) -> str:
        """Generate accessibility test code for a component"""
        component_name = component_info.get('name', 'Component')

        return f"""
import {{ render }} from '@testing-library/react';
import {{ axe, toHaveNoViolations }} from 'jest-axe';
import {component_name} from './{component_name}';

expect.extend(toHaveNoViolations);

describe('Accessibility Tests for {component_name}', () => {{
    test('should not have accessibility violations', async () => {{
        const {{ container }} = render(<{component_name} />);
        const results = await axe(container);
        expect(results).toHaveNoViolations();
    }});

    test('should have proper ARIA labels', () => {{
        const {{ getByRole }} = render(<{component_name} />);

        // Check for proper ARIA labels
        const elements = container.querySelectorAll('[aria-label], [aria-labelledby]');
        expect(elements.length).toBeGreaterThan(0);
    }});

    test('should support keyboard navigation', () => {{
        const {{ container }} = render(<{component_name} />);

        // Check for focusable elements
        const focusableElements = container.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        expect(focusableElements.length).toBeGreaterThan(0);
    }});

    test('should have sufficient color contrast', async () => {{
        const {{ container }} = render(<{component_name} />);
        const results = await axe(container, {{
            rules: {{
                'color-contrast': {{ enabled: true }}
            }}
        }});
        expect(results).toHaveNoViolations();
    }});
}});"""

    def _generate_load_test_code(self, api_files: List[Path], context: QAContext) -> str:
        """Generate load test code for API endpoints"""
        return """
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
    stages: [
        { duration: '30s', target: 20 },
        { duration: '1m', target: 20 },
        { duration: '20s', target: 0 },
    ],
};

export default function () {
    let response = http.get('http://localhost:3000/api/health');

    check(response, {
        'status is 200': (r) => r.status === 200,
        'response time < 500ms': (r) => r.timings.duration < 500,
    });

    sleep(1);
}"""

    def _generate_stress_test_code(self, context: QAContext) -> str:
        """Generate stress test code to find breaking points"""
        return """
describe('Stress Tests', () => {
    test('should handle high memory usage', async () => {
        const largeArray = [];

        // Gradually increase memory usage
        for (let i = 0; i < 100000; i++) {
            largeArray.push(new Array(1000).fill(i));
        }

        // System should still be responsive
        expect(largeArray.length).toBe(100000);
    });

    test('should handle many concurrent requests', async () => {
        const promises = [];

        // Create 100 concurrent requests
        for (let i = 0; i < 100; i++) {
            promises.push(fetch('/api/test'));
        }

        const results = await Promise.allSettled(promises);
        const successful = results.filter(r => r.status === 'fulfilled').length;

        // At least 90% should succeed
        expect(successful / results.length).toBeGreaterThan(0.9);
    });
});"""

    def _generate_smoke_test_code(self, context: QAContext) -> str:
        """Generate smoke test code for basic functionality"""
        return """
describe('Smoke Tests', () => {
    test('application should start successfully', async () => {
        // Test basic application startup
        expect(process.env.NODE_ENV).toBeDefined();
    });

    test('database connection should work', async () => {
        // Test database connectivity
        const connection = await connectToDatabase();
        expect(connection).toBeTruthy();
        await connection.close();
    });

    test('API endpoints should be accessible', async () => {
        const response = await fetch('/api/health');
        expect(response.status).toBe(200);
    });

    test('critical services should be running', async () => {
        // Test that critical services are operational
        const services = ['auth', 'database', 'cache'];

        for (const service of services) {
            const status = await checkServiceStatus(service);
            expect(status).toBe('healthy');
        }
    });
});"""

    async def _identify_critical_functions(self, context: QAContext) -> List[Dict[str, Any]]:
        """Identify performance-critical functions in the codebase"""
        critical_functions = []

        try:
            project_files = context.test_config.get("project_files", [])

            # Patterns that indicate performance-critical functions
            critical_patterns = [
                r'async\s+function\s+(\w+)',  # Async functions
                r'function\s+(\w+).*loop',    # Functions with loops
                r'function\s+(\w+).*database', # Database functions
                r'function\s+(\w+).*api',     # API functions
            ]

            for file_path in project_files:
                if file_path.suffix in ['.js', '.ts', '.py']:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                        for pattern in critical_patterns:
                            matches = re.finditer(pattern, content, re.IGNORECASE)
                            for match in matches:
                                critical_functions.append({
                                    'name': match.group(1),
                                    'file': str(file_path),
                                    'language': self._detect_language(file_path),
                                    'pattern': pattern
                                })

                    except Exception as e:
                        logger.warning(f"Failed to analyze {file_path}: {e}")

        except Exception as e:
            logger.error(f"Failed to identify critical functions: {e}")

        return critical_functions

    async def _analyze_component_file(self, file_path: Path) -> Dict[str, Any]:
        """Analyze a component file to extract component information"""
        components = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # React component patterns
            react_patterns = [
                r'function\s+(\w+)\s*\(',
                r'const\s+(\w+)\s*=\s*\(',
                r'class\s+(\w+)\s+extends\s+Component',
            ]

            for pattern in react_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    components.append({
                        'name': match.group(1),
                        'type': 'react_component',
                        'file': str(file_path)
                    })

        except Exception as e:
            logger.warning(f"Failed to analyze component file {file_path}: {e}")

        return {'components': components}

    def _get_test_dependencies(self, language: CodeLanguage) -> List[str]:
        """Get test dependencies for a specific language"""
        dependency_map = {
            CodeLanguage.JAVASCRIPT: ["jest", "@testing-library/jest-dom"],
            CodeLanguage.TYPESCRIPT: ["jest", "@types/jest", "ts-jest"],
            CodeLanguage.PYTHON: ["pytest", "pytest-cov", "unittest"],
            CodeLanguage.JAVA: ["junit", "mockito"],
            CodeLanguage.CSHARP: ["xunit", "moq"],
            CodeLanguage.GO: ["testify"],
            CodeLanguage.RUST: ["tokio-test"],
        }

        return dependency_map.get(language, ["jest"])

    def _get_performance_test_dependencies(self, language: CodeLanguage) -> List[str]:
        """Get performance test dependencies for a specific language"""
        dependency_map = {
            CodeLanguage.JAVASCRIPT: ["jest", "benchmark", "clinic"],
            CodeLanguage.TYPESCRIPT: ["jest", "@types/benchmark", "clinic"],
            CodeLanguage.PYTHON: ["pytest", "pytest-benchmark", "memory-profiler"],
            CodeLanguage.JAVA: ["junit", "jmh"],
            CodeLanguage.CSHARP: ["xunit", "BenchmarkDotNet"],
            CodeLanguage.GO: ["testing", "go-benchmark"],
            CodeLanguage.RUST: ["criterion"],
        }

        return dependency_map.get(language, ["jest", "benchmark"])

    async def _perform_enhanced_security_analysis(self, context: QAContext) -> Tuple[List[SecurityVulnerability], float]:
        """Perform enhanced security analysis"""
        logger.info("Performing enhanced security analysis...")

        vulnerabilities = []

        try:
            # Static code analysis for security patterns
            static_vulns = await self._perform_static_security_analysis(context)
            vulnerabilities.extend(static_vulns)

            # Dependency vulnerability scanning
            dep_vulns = await self._scan_dependency_vulnerabilities(context)
            vulnerabilities.extend(dep_vulns)

            # Configuration security analysis
            config_vulns = await self._analyze_security_configuration(context)
            vulnerabilities.extend(config_vulns)

            # Calculate security score
            security_score = self._calculate_security_score(vulnerabilities)

            logger.info(f"Found {len(vulnerabilities)} security issues, score: {security_score:.2f}")
            return vulnerabilities, security_score

        except Exception as e:
            logger.error(f"Enhanced security analysis failed: {e}")
            return [], 0.0

    async def _perform_static_security_analysis(self, context: QAContext) -> List[SecurityVulnerability]:
        """Perform static code analysis for security vulnerabilities"""
        vulnerabilities = []
        project_files = context.test_config.get("project_files", [])

        for file_path in project_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Check against security patterns
                for vuln_type, pattern_info in self.security_patterns.items():
                    for pattern in pattern_info['patterns']:
                        matches = re.finditer(pattern, content, re.IGNORECASE)
                        for match in matches:
                            line_number = content[:match.start()].count('\n') + 1

                            vulnerability = SecurityVulnerability(
                                vulnerability_type=vuln_type,
                                severity=pattern_info['severity'],
                                description=pattern_info['description'],
                                file_path=str(file_path),
                                line_number=line_number,
                                recommendation=pattern_info['recommendation']
                            )
                            vulnerabilities.append(vulnerability)

            except Exception as e:
                logger.warning(f"Failed to analyze {file_path} for security: {e}")

        return vulnerabilities

    async def _scan_dependency_vulnerabilities(self, context: QAContext) -> List[SecurityVulnerability]:
        """Scan dependencies for known vulnerabilities"""
        vulnerabilities = []

        try:
            # Check for package.json (Node.js)
            package_json = context.project_path / "package.json"
            if package_json.exists():
                npm_vulns = await self._run_npm_audit(context.project_path)
                vulnerabilities.extend(npm_vulns)

            # Check for requirements.txt (Python)
            requirements_txt = context.project_path / "requirements.txt"
            if requirements_txt.exists():
                python_vulns = await self._run_safety_check(context.project_path)
                vulnerabilities.extend(python_vulns)

        except Exception as e:
            logger.warning(f"Dependency vulnerability scanning failed: {e}")

        return vulnerabilities

    async def _run_npm_audit(self, project_path: Path) -> List[SecurityVulnerability]:
        """Run npm audit for Node.js dependencies"""
        vulnerabilities = []

        try:
            result = await self._run_test_command(project_path, "npm audit --json")

            if result["returncode"] != 0 and result["stdout"]:
                audit_data = json.loads(result["stdout"])

                for vuln_id, vuln_info in audit_data.get("vulnerabilities", {}).items():
                    vulnerability = SecurityVulnerability(
                        vulnerability_type="dependency_vulnerability",
                        severity=SecuritySeverity(vuln_info.get("severity", "medium")),
                        description=f"Vulnerable dependency: {vuln_info.get('name', vuln_id)}",
                        file_path="package.json",
                        recommendation=f"Update to version {vuln_info.get('fixAvailable', 'latest')}"
                    )
                    vulnerabilities.append(vulnerability)

        except Exception as e:
            logger.warning(f"npm audit failed: {e}")

        return vulnerabilities

    async def _run_safety_check(self, project_path: Path) -> List[SecurityVulnerability]:
        """Run safety check for Python dependencies"""
        vulnerabilities = []

        try:
            result = await self._run_test_command(project_path, "safety check --json")

            if result["returncode"] != 0 and result["stdout"]:
                safety_data = json.loads(result["stdout"])

                for vuln in safety_data:
                    vulnerability = SecurityVulnerability(
                        vulnerability_type="dependency_vulnerability",
                        severity=SecuritySeverity.MEDIUM,  # Safety doesn't provide severity
                        description=f"Vulnerable dependency: {vuln.get('package')}",
                        file_path="requirements.txt",
                        recommendation=f"Update to version {vuln.get('analyzed_version')}"
                    )
                    vulnerabilities.append(vulnerability)

        except Exception as e:
            logger.warning(f"safety check failed: {e}")

        return vulnerabilities

    async def _analyze_security_configuration(self, context: QAContext) -> List[SecurityVulnerability]:
        """Analyze security configuration"""
        vulnerabilities = []

        # Check for common security misconfigurations
        config_checks = [
            self._check_cors_configuration,
            self._check_https_configuration,
            self._check_authentication_configuration,
            self._check_environment_variables
        ]

        for check in config_checks:
            try:
                check_vulns = await check(context)
                vulnerabilities.extend(check_vulns)
            except Exception as e:
                logger.warning(f"Security configuration check failed: {e}")

        return vulnerabilities

    def _calculate_security_score(self, vulnerabilities: List[SecurityVulnerability]) -> float:
        """Calculate security score based on vulnerabilities"""
        if not vulnerabilities:
            return 100.0

        # Weight vulnerabilities by severity
        severity_weights = {
            SecuritySeverity.LOW: 1,
            SecuritySeverity.MEDIUM: 3,
            SecuritySeverity.HIGH: 7,
            SecuritySeverity.CRITICAL: 15
        }

        total_weight = sum(severity_weights[vuln.severity] for vuln in vulnerabilities)

        # Calculate score (100 - penalty)
        max_penalty = 100
        penalty = min(total_weight * 2, max_penalty)

        return max(0.0, 100.0 - penalty)

    async def _analyze_enhanced_performance(self, context: QAContext) -> Tuple[List[PerformanceMetric], float]:
        """Perform enhanced performance analysis"""
        logger.info("Performing enhanced performance analysis...")

        performance_metrics = []

        try:
            # Bundle size analysis for frontend projects
            bundle_metrics = await self._analyze_bundle_performance(context)
            performance_metrics.extend(bundle_metrics)

            # API performance analysis
            api_metrics = await self._analyze_api_performance(context)
            performance_metrics.extend(api_metrics)

            # Database performance analysis
            db_metrics = await self._analyze_database_performance(context)
            performance_metrics.extend(db_metrics)

            # Memory usage analysis
            memory_metrics = await self._analyze_memory_usage(context)
            performance_metrics.extend(memory_metrics)

            # Calculate overall performance score
            performance_score = self._calculate_performance_score(performance_metrics)

            logger.info(f"Collected {len(performance_metrics)} performance metrics, score: {performance_score:.2f}")
            return performance_metrics, performance_score

        except Exception as e:
            logger.error(f"Enhanced performance analysis failed: {e}")
            return [], 0.0

    async def _analyze_bundle_performance(self, context: QAContext) -> List[PerformanceMetric]:
        """Analyze frontend bundle performance"""
        metrics = []

        try:
            # Check for build output directory
            build_dirs = [
                context.project_path / "build",
                context.project_path / "dist",
                context.project_path / ".next"
            ]

            for build_dir in build_dirs:
                if build_dir.exists():
                    bundle_size = self._calculate_bundle_size(build_dir)

                    metric = PerformanceMetric(
                        metric_name="bundle_size",
                        value=bundle_size,
                        unit="KB",
                        threshold=500.0,  # 500KB threshold
                        status="pass" if bundle_size < 500 else "fail"
                    )
                    metrics.append(metric)
                    break

        except Exception as e:
            logger.warning(f"Bundle performance analysis failed: {e}")

        return metrics

    def _calculate_bundle_size(self, build_dir: Path) -> float:
        """Calculate total bundle size"""
        total_size = 0

        for file_path in build_dir.rglob("*.js"):
            if file_path.is_file():
                total_size += file_path.stat().st_size

        return total_size / 1024  # Convert to KB

    async def _analyze_api_performance(self, context: QAContext) -> List[PerformanceMetric]:
        """Analyze API performance"""
        metrics = []

        try:
            # Look for API endpoints
            api_files = self._find_api_files(context)

            if api_files:
                # Simulate API performance metrics
                # In a real implementation, this would make actual API calls

                response_time_metric = PerformanceMetric(
                    metric_name="api_response_time",
                    value=150.0,  # Simulated value
                    unit="ms",
                    threshold=200.0,
                    status="pass"
                )
                metrics.append(response_time_metric)

                throughput_metric = PerformanceMetric(
                    metric_name="api_throughput",
                    value=800.0,  # Simulated value
                    unit="requests/sec",
                    threshold=500.0,
                    status="pass"
                )
                metrics.append(throughput_metric)

        except Exception as e:
            logger.warning(f"API performance analysis failed: {e}")

        return metrics

    def _find_api_files(self, context: QAContext) -> List[Path]:
        """Find API-related files"""
        api_files = []
        project_files = context.test_config.get("project_files", [])

        api_patterns = ['api', 'route', 'endpoint', 'controller', 'handler']

        for file_path in project_files:
            if any(pattern in str(file_path).lower() for pattern in api_patterns):
                api_files.append(file_path)

        return api_files

    async def _analyze_database_performance(self, context: QAContext) -> List[PerformanceMetric]:
        """Analyze database performance"""
        metrics = []

        try:
            # Look for database-related files
            db_files = self._find_database_files(context)

            if db_files:
                # Check for database optimization patterns
                has_indexes = await self._check_database_indexes(db_files)
                has_query_optimization = await self._check_query_optimization(db_files)

                index_metric = PerformanceMetric(
                    metric_name="database_indexes",
                    value=1.0 if has_indexes else 0.0,
                    unit="boolean",
                    threshold=1.0,
                    status="pass" if has_indexes else "fail"
                )
                metrics.append(index_metric)

                query_metric = PerformanceMetric(
                    metric_name="query_optimization",
                    value=1.0 if has_query_optimization else 0.0,
                    unit="boolean",
                    threshold=1.0,
                    status="pass" if has_query_optimization else "fail"
                )
                metrics.append(query_metric)

        except Exception as e:
            logger.warning(f"Database performance analysis failed: {e}")

        return metrics

    def _find_database_files(self, context: QAContext) -> List[Path]:
        """Find database-related files"""
        db_files = []
        project_files = context.test_config.get("project_files", [])

        db_patterns = ['model', 'schema', 'migration', 'database', 'db', 'sql']

        for file_path in project_files:
            if any(pattern in str(file_path).lower() for pattern in db_patterns):
                db_files.append(file_path)

        return db_files

    async def _check_database_indexes(self, db_files: List[Path]) -> bool:
        """Check if database indexes are properly defined"""
        index_patterns = [
            r'createIndex',
            r'ensureIndex',
            r'@Index',
            r'INDEX\s+',
            r'\.index\('
        ]

        for file_path in db_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                if any(re.search(pattern, content, re.IGNORECASE) for pattern in index_patterns):
                    return True

            except Exception:
                continue

        return False

    async def _check_query_optimization(self, db_files: List[Path]) -> bool:
        """Check for query optimization patterns"""
        optimization_patterns = [
            r'\.select\(',
            r'\.limit\(',
            r'\.where\(',
            r'\.join\(',
            r'EXPLAIN',
            r'pagination'
        ]

        for file_path in db_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                if any(re.search(pattern, content, re.IGNORECASE) for pattern in optimization_patterns):
                    return True

            except Exception:
                continue

        return False

    async def _analyze_memory_usage(self, context: QAContext) -> List[PerformanceMetric]:
        """Analyze memory usage patterns"""
        metrics = []

        try:
            # Look for memory-related patterns in code
            memory_issues = await self._detect_memory_issues(context)

            memory_leak_metric = PerformanceMetric(
                metric_name="memory_leaks",
                value=len(memory_issues),
                unit="count",
                threshold=0.0,
                status="pass" if len(memory_issues) == 0 else "fail"
            )
            metrics.append(memory_leak_metric)

        except Exception as e:
            logger.warning(f"Memory usage analysis failed: {e}")

        return metrics

    async def _detect_memory_issues(self, context: QAContext) -> List[str]:
        """Detect potential memory issues in code"""
        issues = []
        project_files = context.test_config.get("project_files", [])

        memory_issue_patterns = [
            r'setInterval\s*\(',  # Potential memory leaks in JS
            r'addEventListener\s*\(',  # Event listeners without cleanup
            r'new\s+Array\s*\(\s*\d{6,}\s*\)',  # Large array allocations
            r'while\s*\(\s*true\s*\)',  # Infinite loops
        ]

        for file_path in project_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                for pattern in memory_issue_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        issues.append(f"Potential memory issue in {file_path}")

            except Exception:
                continue

        return issues

    def _calculate_performance_score(self, metrics: List[PerformanceMetric]) -> float:
        """Calculate overall performance score"""
        if not metrics:
            return 50.0  # Neutral score when no metrics available

        passed_metrics = sum(1 for metric in metrics if metric.status == "pass")
        total_metrics = len(metrics)

        return (passed_metrics / total_metrics) * 100.0

    async def _analyze_failed_tests(self, test_suites: List[TestSuite], context: QAContext) -> List[Dict[str, Any]]:
        """Analyze failed tests and suggest fixes"""
        logger.info("Analyzing failed tests...")

        failed_test_fixes = []

        for suite in test_suites:
            for test in suite.tests:
                if test.status == TestStatus.FAILED:
                    fix_suggestion = await self._suggest_test_fix(test, context)
                    if fix_suggestion:
                        failed_test_fixes.append(fix_suggestion)

        logger.info(f"Generated {len(failed_test_fixes)} fix suggestions")
        return failed_test_fixes

    async def _suggest_test_fix(self, test_result: TestResult, context: QAContext) -> Optional[Dict[str, Any]]:
        """Suggest fix for a failed test"""
        try:
            # Analyze error message to determine fix type
            error_analysis = self._analyze_test_error(test_result.error_message or "")

            # Generate fix suggestion using AI if available
            if context.feedback_mode:
                fix_suggestion = await self._generate_ai_test_fix(test_result, error_analysis, context)
            else:
                fix_suggestion = self._generate_template_test_fix(test_result, error_analysis)

            return {
                "test_name": test_result.test_name,
                "test_file": test_result.file_path,
                "error_type": error_analysis["error_type"],
                "error_message": test_result.error_message,
                "suggested_fix": fix_suggestion,
                "confidence": error_analysis["confidence"],
                "fix_type": error_analysis["fix_type"]
            }

        except Exception as e:
            logger.error(f"Failed to suggest fix for test {test_result.test_name}: {e}")
            return None

    def _analyze_test_error(self, error_message: str) -> Dict[str, Any]:
        """Analyze test error message to categorize the issue"""
        error_patterns = {
            "assertion_error": {
                "patterns": [r"AssertionError", r"expect.*toBe", r"assert.*failed"],
                "fix_type": "assertion_fix",
                "confidence": 0.8
            },
            "timeout_error": {
                "patterns": [r"timeout", r"exceeded.*time", r"async.*timeout"],
                "fix_type": "timeout_fix",
                "confidence": 0.9
            },
            "reference_error": {
                "patterns": [r"ReferenceError", r"is not defined", r"undefined"],
                "fix_type": "reference_fix",
                "confidence": 0.85
            },
            "type_error": {
                "patterns": [r"TypeError", r"is not a function", r"Cannot read property"],
                "fix_type": "type_fix",
                "confidence": 0.8
            },
            "import_error": {
                "patterns": [r"ImportError", r"ModuleNotFoundError", r"Cannot resolve module"],
                "fix_type": "import_fix",
                "confidence": 0.9
            }
        }

        for error_type, config in error_patterns.items():
            for pattern in config["patterns"]:
                if re.search(pattern, error_message, re.IGNORECASE):
                    return {
                        "error_type": error_type,
                        "fix_type": config["fix_type"],
                        "confidence": config["confidence"]
                    }

        return {
            "error_type": "unknown",
            "fix_type": "manual_review",
            "confidence": 0.3
        }

    async def _generate_ai_test_fix(self, test_result: TestResult, error_analysis: Dict[str, Any], context: QAContext) -> str:
        """Generate AI-powered test fix suggestion"""
        try:
            prompt = f"""
Analyze this failed test and suggest a fix:

Test Name: {test_result.test_name}
Error Type: {error_analysis['error_type']}
Error Message: {test_result.error_message}
Test File: {test_result.file_path}

Provide a specific fix suggestion that addresses the root cause of the failure.
Focus on the most likely solution based on the error type and message.
"""

            response = await self.api_manager.generate_completion(
                prompt=prompt,
                max_tokens=500,
                temperature=0.3
            )

            if response and response.get('success'):
                return response['content']
            else:
                return self._generate_template_test_fix(test_result, error_analysis)

        except Exception as e:
            logger.warning(f"AI test fix generation failed: {e}")
            return self._generate_template_test_fix(test_result, error_analysis)

    def _generate_template_test_fix(self, test_result: TestResult, error_analysis: Dict[str, Any]) -> str:
        """Generate template-based test fix suggestion"""
        fix_type = error_analysis["fix_type"]

        fix_templates = {
            "assertion_fix": "Check if the expected value matches the actual implementation. Verify the test assertion logic.",
            "timeout_fix": "Increase test timeout or optimize async operations. Consider using proper await/async patterns.",
            "reference_fix": "Ensure all variables and functions are properly defined and imported before use.",
            "type_fix": "Verify object types and method availability. Add proper type checking or mocking.",
            "import_fix": "Check import paths and ensure all dependencies are properly installed and available.",
            "manual_review": "This error requires manual review. Check the test logic and implementation details."
        }

        return fix_templates.get(fix_type, fix_templates["manual_review"])

    async def _perform_accessibility_testing(self, context: QAContext) -> float:
        """Perform accessibility testing"""
        logger.info("Performing accessibility testing...")

        try:
            # Look for frontend files
            frontend_files = self._find_frontend_files(context)

            if not frontend_files:
                return 0.0  # No frontend files to test

            # Check for accessibility patterns
            accessibility_score = await self._analyze_accessibility_patterns(frontend_files)

            logger.info(f"Accessibility score: {accessibility_score:.2f}")
            return accessibility_score

        except Exception as e:
            logger.error(f"Accessibility testing failed: {e}")
            return 0.0

    def _find_frontend_files(self, context: QAContext) -> List[Path]:
        """Find frontend-related files"""
        frontend_files = []
        project_files = context.test_config.get("project_files", [])

        frontend_patterns = ['.jsx', '.tsx', '.vue', '.html', '.css']

        for file_path in project_files:
            if any(file_path.suffix.lower() == pattern for pattern in frontend_patterns):
                frontend_files.append(file_path)

        return frontend_files

    async def _analyze_accessibility_patterns(self, frontend_files: List[Path]) -> float:
        """Analyze accessibility patterns in frontend files"""
        total_checks = 0
        passed_checks = 0

        accessibility_patterns = {
            "alt_attributes": r'<img[^>]*alt\s*=',
            "aria_labels": r'aria-label\s*=',
            "semantic_html": r'<(header|nav|main|section|article|aside|footer)',
            "keyboard_navigation": r'tabIndex|onKeyDown|onKeyPress',
            "focus_management": r'focus\(\)|autoFocus'
        }

        for file_path in frontend_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                for pattern_name, pattern in accessibility_patterns.items():
                    total_checks += 1
                    if re.search(pattern, content, re.IGNORECASE):
                        passed_checks += 1

            except Exception:
                continue

        if total_checks == 0:
            return 50.0  # Neutral score

        return (passed_checks / total_checks) * 100.0

    async def _calculate_maintainability_score(self, context: QAContext) -> float:
        """Calculate code maintainability score"""
        logger.info("Calculating maintainability score...")

        try:
            project_files = context.test_config.get("project_files", [])

            if not project_files:
                return 0.0

            # Analyze various maintainability factors
            complexity_score = await self._analyze_code_complexity(project_files)
            documentation_score = await self._analyze_documentation_coverage(project_files)
            structure_score = await self._analyze_code_structure(project_files)

            # Weighted average
            maintainability_score = (
                complexity_score * 0.4 +
                documentation_score * 0.3 +
                structure_score * 0.3
            )

            logger.info(f"Maintainability score: {maintainability_score:.2f}")
            return maintainability_score

        except Exception as e:
            logger.error(f"Maintainability calculation failed: {e}")
            return 0.0

    async def _analyze_code_complexity(self, project_files: List[Path]) -> float:
        """Analyze code complexity"""
        total_files = len(project_files)
        low_complexity_files = 0

        complexity_patterns = [
            r'if\s*\(',  # Conditional statements
            r'for\s*\(',  # Loops
            r'while\s*\(',  # While loops
            r'switch\s*\(',  # Switch statements
            r'catch\s*\(',  # Exception handling
        ]

        for file_path in project_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                complexity_count = sum(
                    len(re.findall(pattern, content, re.IGNORECASE))
                    for pattern in complexity_patterns
                )

                # Consider low complexity if less than 10 complex constructs per 100 lines
                lines_count = content.count('\n') + 1
                complexity_ratio = complexity_count / max(lines_count / 100, 1)

                if complexity_ratio < 10:
                    low_complexity_files += 1

            except Exception:
                continue

        if total_files == 0:
            return 50.0

        return (low_complexity_files / total_files) * 100.0

    async def _analyze_documentation_coverage(self, project_files: List[Path]) -> float:
        """Analyze documentation coverage"""
        total_functions = 0
        documented_functions = 0

        for file_path in project_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Find functions
                function_patterns = [
                    r'function\s+\w+\s*\(',
                    r'const\s+\w+\s*=\s*\([^)]*\)\s*=>',
                    r'def\s+\w+\s*\(',
                ]

                # Find documentation patterns
                doc_patterns = [
                    r'/\*\*.*?\*/',  # JSDoc
                    r'""".*?"""',    # Python docstring
                    r"'''.*?'''",    # Python docstring
                ]

                for pattern in function_patterns:
                    functions = re.findall(pattern, content, re.DOTALL)
                    total_functions += len(functions)

                for pattern in doc_patterns:
                    docs = re.findall(pattern, content, re.DOTALL)
                    documented_functions += len(docs)

            except Exception:
                continue

        if total_functions == 0:
            return 50.0

        return min((documented_functions / total_functions) * 100.0, 100.0)

    async def _analyze_code_structure(self, project_files: List[Path]) -> float:
        """Analyze code structure quality"""
        structure_score = 0.0
        total_checks = 0

        # Check for good structure patterns
        structure_checks = [
            ("separation_of_concerns", r'(controller|service|model|view)'),
            ("error_handling", r'(try|catch|except|finally)'),
            ("constants", r'(const|final|readonly)'),
            ("type_definitions", r'(interface|type|class)'),
        ]

        for file_path in project_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                for check_name, pattern in structure_checks:
                    total_checks += 1
                    if re.search(pattern, content, re.IGNORECASE):
                        structure_score += 1

            except Exception:
                continue

        if total_checks == 0:
            return 50.0

        return (structure_score / total_checks) * 100.0

    async def _send_feedback_to_developer(self, failed_test_fixes: List[Dict[str, Any]], context: QAContext):
        """Send comprehensive feedback to developer agent"""
        logger.info("Sending comprehensive feedback to developer agent...")

        try:
            # Generate comprehensive feedback report
            feedback_data = await self._generate_comprehensive_feedback(failed_test_fixes, context)

            # Save feedback to multiple formats
            await self._save_feedback_artifacts(feedback_data, context)

            # Send feedback through pheromone system if available
            await self._send_pheromone_feedback(feedback_data, context)

            logger.info(f"Comprehensive feedback generated and sent")

        except Exception as e:
            logger.error(f"Failed to send feedback to developer: {e}")

    async def _generate_comprehensive_feedback(self, failed_test_fixes: List[Dict[str, Any]], context: QAContext) -> Dict[str, Any]:
        """Generate comprehensive feedback report"""

        # Analyze failure patterns
        failure_patterns = self._analyze_failure_patterns(failed_test_fixes)

        # Generate code quality recommendations
        quality_recommendations = await self._generate_code_quality_recommendations(context)

        # Generate security recommendations
        security_recommendations = await self._generate_security_recommendations(context)

        # Generate performance recommendations
        performance_recommendations = await self._generate_performance_recommendations(context)

        # Generate refactoring suggestions
        refactoring_suggestions = await self._generate_refactoring_suggestions(context)

        # Calculate priority scores for recommendations
        prioritized_recommendations = self._prioritize_recommendations(
            quality_recommendations + security_recommendations + performance_recommendations
        )

        feedback_data = {
            "type": "comprehensive_qa_feedback",
            "project_path": str(context.project_path),
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_failed_tests": len(failed_test_fixes),
                "failure_patterns": failure_patterns,
                "critical_issues": len([r for r in prioritized_recommendations if r.get('priority') == 'critical']),
                "high_issues": len([r for r in prioritized_recommendations if r.get('priority') == 'high']),
                "medium_issues": len([r for r in prioritized_recommendations if r.get('priority') == 'medium'])
            },
            "failed_tests": {
                "details": failed_test_fixes,
                "patterns": failure_patterns,
                "common_causes": self._identify_common_failure_causes(failed_test_fixes)
            },
            "recommendations": {
                "code_quality": quality_recommendations,
                "security": security_recommendations,
                "performance": performance_recommendations,
                "refactoring": refactoring_suggestions,
                "prioritized": prioritized_recommendations
            },
            "action_items": self._generate_action_items(prioritized_recommendations),
            "learning_resources": self._suggest_learning_resources(failure_patterns),
            "next_steps": self._generate_next_steps(context, prioritized_recommendations)
        }

        return feedback_data

    def _analyze_failure_patterns(self, failed_test_fixes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze patterns in test failures"""
        patterns = {
            "error_types": {},
            "affected_files": {},
            "common_keywords": [],
            "failure_frequency": {}
        }

        for fix in failed_test_fixes:
            error_type = fix.get('error_type', 'unknown')
            patterns['error_types'][error_type] = patterns['error_types'].get(error_type, 0) + 1

            test_file = fix.get('test_file', 'unknown')
            patterns['affected_files'][test_file] = patterns['affected_files'].get(test_file, 0) + 1

        # Identify most common error types
        patterns['most_common_error'] = max(patterns['error_types'].items(), key=lambda x: x[1]) if patterns['error_types'] else None
        patterns['most_affected_file'] = max(patterns['affected_files'].items(), key=lambda x: x[1]) if patterns['affected_files'] else None

        return patterns

    async def _generate_code_quality_recommendations(self, context: QAContext) -> List[Dict[str, Any]]:
        """Generate code quality improvement recommendations"""
        recommendations = []

        try:
            # Analyze code complexity
            complexity_issues = await self._analyze_code_complexity_issues(context)
            recommendations.extend(complexity_issues)

            # Analyze documentation coverage
            doc_issues = await self._analyze_documentation_issues(context)
            recommendations.extend(doc_issues)

            # Analyze code duplication
            duplication_issues = await self._analyze_code_duplication(context)
            recommendations.extend(duplication_issues)

            # Analyze naming conventions
            naming_issues = await self._analyze_naming_conventions(context)
            recommendations.extend(naming_issues)

        except Exception as e:
            logger.error(f"Failed to generate code quality recommendations: {e}")

        return recommendations

    async def _generate_security_recommendations(self, context: QAContext) -> List[Dict[str, Any]]:
        """Generate security improvement recommendations"""
        recommendations = []

        try:
            # Check for hardcoded secrets
            secret_issues = await self._check_hardcoded_secrets(context)
            recommendations.extend(secret_issues)

            # Check for insecure dependencies
            dependency_issues = await self._check_insecure_dependencies(context)
            recommendations.extend(dependency_issues)

            # Check for missing security headers
            header_issues = await self._check_missing_security_headers(context)
            recommendations.extend(header_issues)

            # Check for input validation issues
            validation_issues = await self._check_input_validation_issues(context)
            recommendations.extend(validation_issues)

        except Exception as e:
            logger.error(f"Failed to generate security recommendations: {e}")

        return recommendations

    async def _generate_performance_recommendations(self, context: QAContext) -> List[Dict[str, Any]]:
        """Generate performance improvement recommendations"""
        recommendations = []

        try:
            # Check for inefficient algorithms
            algorithm_issues = await self._check_inefficient_algorithms(context)
            recommendations.extend(algorithm_issues)

            # Check for memory leaks
            memory_issues = await self._check_memory_leak_patterns(context)
            recommendations.extend(memory_issues)

            # Check for database optimization opportunities
            db_issues = await self._check_database_optimization_opportunities(context)
            recommendations.extend(db_issues)

            # Check for caching opportunities
            cache_issues = await self._check_caching_opportunities(context)
            recommendations.extend(cache_issues)

        except Exception as e:
            logger.error(f"Failed to generate performance recommendations: {e}")

        return recommendations

    async def _generate_refactoring_suggestions(self, context: QAContext) -> List[Dict[str, Any]]:
        """Generate refactoring suggestions"""
        suggestions = []

        try:
            # Check for long methods
            long_method_issues = await self._check_long_methods(context)
            suggestions.extend(long_method_issues)

            # Check for large classes
            large_class_issues = await self._check_large_classes(context)
            suggestions.extend(large_class_issues)

            # Check for code smells
            code_smell_issues = await self._check_code_smells(context)
            suggestions.extend(code_smell_issues)

        except Exception as e:
            logger.error(f"Failed to generate refactoring suggestions: {e}")

        return suggestions

    def _prioritize_recommendations(self, recommendations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prioritize recommendations based on impact and effort"""

        for rec in recommendations:
            # Calculate priority score based on impact and effort
            impact = rec.get('impact', 'medium')
            effort = rec.get('effort', 'medium')

            impact_scores = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
            effort_scores = {'low': 3, 'medium': 2, 'high': 1}

            priority_score = impact_scores.get(impact, 2) * effort_scores.get(effort, 2)

            if priority_score >= 9:
                rec['priority'] = 'critical'
            elif priority_score >= 6:
                rec['priority'] = 'high'
            elif priority_score >= 4:
                rec['priority'] = 'medium'
            else:
                rec['priority'] = 'low'

        # Sort by priority
        priority_order = {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}
        recommendations.sort(key=lambda x: priority_order.get(x.get('priority', 'low'), 1), reverse=True)

        return recommendations

    def _identify_common_failure_causes(self, failed_test_fixes: List[Dict[str, Any]]) -> List[str]:
        """Identify common causes of test failures"""
        causes = []

        error_types = [fix.get('error_type', '') for fix in failed_test_fixes]

        if 'assertion_error' in error_types:
            causes.append("Incorrect test expectations or implementation logic")
        if 'timeout_error' in error_types:
            causes.append("Slow async operations or infinite loops")
        if 'reference_error' in error_types:
            causes.append("Missing imports or undefined variables")
        if 'type_error' in error_types:
            causes.append("Type mismatches or incorrect method calls")

        return causes

    def _generate_action_items(self, recommendations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate actionable items from recommendations"""
        action_items = []

        # Group recommendations by priority
        critical_items = [r for r in recommendations if r.get('priority') == 'critical']
        high_items = [r for r in recommendations if r.get('priority') == 'high']

        # Create immediate action items for critical issues
        for item in critical_items[:3]:  # Top 3 critical items
            action_items.append({
                'priority': 'immediate',
                'title': f"Fix: {item.get('title', 'Critical Issue')}",
                'description': item.get('description', ''),
                'estimated_effort': item.get('effort', 'medium'),
                'files_affected': item.get('files', []),
                'deadline': 'within 24 hours'
            })

        # Create short-term action items for high priority issues
        for item in high_items[:5]:  # Top 5 high priority items
            action_items.append({
                'priority': 'short_term',
                'title': f"Improve: {item.get('title', 'High Priority Issue')}",
                'description': item.get('description', ''),
                'estimated_effort': item.get('effort', 'medium'),
                'files_affected': item.get('files', []),
                'deadline': 'within 1 week'
            })

        return action_items

    def _suggest_learning_resources(self, failure_patterns: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Suggest learning resources based on failure patterns"""
        resources = []

        most_common_error = failure_patterns.get('most_common_error')
        if most_common_error:
            error_type = most_common_error[0]

            resource_map = {
                'assertion_error': {
                    'title': 'Writing Better Test Assertions',
                    'url': 'https://jestjs.io/docs/using-matchers',
                    'description': 'Learn how to write more effective test assertions'
                },
                'timeout_error': {
                    'title': 'Async Testing Best Practices',
                    'url': 'https://jestjs.io/docs/asynchronous',
                    'description': 'Master async testing patterns and avoid timeouts'
                },
                'reference_error': {
                    'title': 'JavaScript Modules and Imports',
                    'url': 'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Modules',
                    'description': 'Understanding module imports and exports'
                }
            }

            if error_type in resource_map:
                resources.append(resource_map[error_type])

        # Add general testing resources
        resources.extend([
            {
                'title': 'Test-Driven Development Guide',
                'url': 'https://testdriven.io/',
                'description': 'Comprehensive guide to TDD practices'
            },
            {
                'title': 'Code Quality Best Practices',
                'url': 'https://clean-code-developer.com/',
                'description': 'Learn clean code principles and practices'
            }
        ])

        return resources

    def _generate_next_steps(self, context: QAContext, recommendations: List[Dict[str, Any]]) -> List[str]:
        """Generate next steps based on analysis"""
        next_steps = []

        critical_count = len([r for r in recommendations if r.get('priority') == 'critical'])
        high_count = len([r for r in recommendations if r.get('priority') == 'high'])

        if critical_count > 0:
            next_steps.append(f"Address {critical_count} critical issues immediately")

        if high_count > 0:
            next_steps.append(f"Plan to fix {high_count} high-priority issues this sprint")

        # Add specific next steps based on project state
        overall_coverage = self._calculate_overall_coverage([])  # Would need actual test suites
        target_coverage = self.coverage_thresholds[context.quality_level]

        if overall_coverage < target_coverage:
            next_steps.append(f"Increase test coverage from {overall_coverage:.1f}% to {target_coverage:.1f}%")

        next_steps.extend([
            "Run security scan on all dependencies",
            "Review and update documentation",
            "Set up automated quality gates in CI/CD pipeline"
        ])

        return next_steps

    async def _save_feedback_artifacts(self, feedback_data: Dict[str, Any], context: QAContext):
        """Save feedback artifacts in multiple formats"""
        try:
            feedback_dir = context.project_path / "qa_feedback"
            feedback_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            # Save JSON report
            json_file = feedback_dir / f"feedback_report_{timestamp}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(feedback_data, f, indent=2)

            # Save markdown summary
            md_file = feedback_dir / f"feedback_summary_{timestamp}.md"
            await self._generate_markdown_feedback(feedback_data, md_file)

            # Save CSV for tracking
            csv_file = feedback_dir / f"issues_tracking_{timestamp}.csv"
            await self._generate_csv_tracking(feedback_data, csv_file)

            logger.info(f"Feedback artifacts saved to {feedback_dir}")

        except Exception as e:
            logger.error(f"Failed to save feedback artifacts: {e}")

    async def _send_pheromone_feedback(self, feedback_data: Dict[str, Any], context: QAContext):
        """Send feedback through pheromone system"""
        try:
            # This would integrate with the actual pheromone system
            pheromone_data = {
                "type": "qa_feedback",
                "source": "qa_agent",
                "target": "developer_agent",
                "priority": "high" if feedback_data['summary']['critical_issues'] > 0 else "medium",
                "data": {
                    "failed_tests": feedback_data['summary']['total_failed_tests'],
                    "critical_issues": feedback_data['summary']['critical_issues'],
                    "action_items": feedback_data['action_items'][:3],  # Top 3 action items
                    "next_steps": feedback_data['next_steps'][:3]  # Top 3 next steps
                }
            }

            # Save pheromone for now (would be sent through actual pheromone bus)
            pheromone_file = context.project_path / "qa_feedback" / "pheromone_feedback.json"
            with open(pheromone_file, 'w', encoding='utf-8') as f:
                json.dump(pheromone_data, f, indent=2)

            logger.info("Pheromone feedback prepared")

        except Exception as e:
            logger.error(f"Failed to send pheromone feedback: {e}")

    # Workflow Engine Integration Methods

    async def register_workflow_hooks(self, workflow_engine):
        """Register QA agent hooks with the workflow engine"""
        try:
            # Register pre-development hook
            workflow_engine.register_hook("pre_development", self._pre_development_hook)

            # Register post-development hook
            workflow_engine.register_hook("post_development", self._post_development_hook)

            # Register quality gate hook
            workflow_engine.register_hook("quality_gate", self._quality_gate_hook)

            # Register final verification hook
            workflow_engine.register_hook("final_verification", self._final_verification_hook)

            logger.info("QA agent hooks registered with workflow engine")

        except Exception as e:
            logger.error(f"Failed to register workflow hooks: {e}")

    async def _pre_development_hook(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Hook called before development starts"""
        logger.info("QA Agent: Pre-development analysis")

        try:
            # Analyze requirements for testability
            testability_analysis = await self._analyze_requirements_testability(context)

            # Generate test plan
            test_plan = await self._generate_test_plan(context)

            # Set up test infrastructure
            test_infrastructure = await self._setup_test_infrastructure(context)

            return {
                "success": True,
                "testability_analysis": testability_analysis,
                "test_plan": test_plan,
                "test_infrastructure": test_infrastructure,
                "recommendations": [
                    "Ensure all requirements are testable",
                    "Set up automated testing pipeline",
                    "Define acceptance criteria for each feature"
                ]
            }

        except Exception as e:
            logger.error(f"Pre-development hook failed: {e}")
            return {"success": False, "error": str(e)}

    async def _post_development_hook(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Hook called after development is complete"""
        logger.info("QA Agent: Post-development validation")

        try:
            # Create QA context from workflow context
            qa_context = self._create_qa_context_from_workflow(context)

            # Execute comprehensive QA process
            qa_report = await self.execute_qa_process(qa_context)

            # Generate feedback for developer
            feedback = await self._generate_post_development_feedback(qa_report, context)

            return {
                "success": True,
                "qa_report": qa_report,
                "feedback": feedback,
                "quality_score": qa_report.quality_score,
                "recommendations": qa_report.recommendations
            }

        except Exception as e:
            logger.error(f"Post-development hook failed: {e}")
            return {"success": False, "error": str(e)}

    async def _quality_gate_hook(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Quality gate validation"""
        logger.info("QA Agent: Quality gate validation")

        try:
            # Get quality level from context
            quality_level = QualityLevel(context.get("quality_level", "standard"))

            # Create QA context
            qa_context = self._create_qa_context_from_workflow(context)
            qa_context.quality_level = quality_level

            # Run quality gate checks
            gate_results = await self._run_quality_gate_checks(qa_context)

            # Determine if quality gate passes
            gate_passed = self._evaluate_quality_gate(gate_results, quality_level)

            return {
                "success": True,
                "gate_passed": gate_passed,
                "gate_results": gate_results,
                "quality_level": quality_level.value,
                "next_action": "proceed" if gate_passed else "fix_issues"
            }

        except Exception as e:
            logger.error(f"Quality gate hook failed: {e}")
            return {"success": False, "error": str(e)}

    async def _final_verification_hook(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Final verification before project completion"""
        logger.info("QA Agent: Final verification")

        try:
            # Create QA context
            qa_context = self._create_qa_context_from_workflow(context)

            # Run final comprehensive validation
            final_report = await self._run_final_validation(qa_context)

            # Generate completion certificate
            completion_certificate = await self._generate_completion_certificate(final_report, context)

            return {
                "success": True,
                "final_report": final_report,
                "completion_certificate": completion_certificate,
                "project_ready": final_report.quality_score >= 80.0,
                "final_recommendations": final_report.recommendations
            }

        except Exception as e:
            logger.error(f"Final verification hook failed: {e}")
            return {"success": False, "error": str(e)}

    def _create_qa_context_from_workflow(self, workflow_context: Dict[str, Any]) -> QAContext:
        """Create QA context from workflow context"""
        project_path = Path(workflow_context.get("project_path", "."))

        qa_context = QAContext(
            project_path=project_path,
            quality_level=QualityLevel(workflow_context.get("quality_level", "standard")),
            enable_test_generation=workflow_context.get("enable_test_generation", True),
            enable_security_scanning=workflow_context.get("enable_security_scanning", True),
            enable_performance_testing=workflow_context.get("enable_performance_testing", True),
            enable_accessibility_testing=workflow_context.get("enable_accessibility_testing", False),
            feedback_mode=workflow_context.get("feedback_mode", True)
        )

        # Add project specification if available
        if "project_specification" in workflow_context:
            qa_context.project_specification = workflow_context["project_specification"]

        # Add architecture if available
        if "architecture" in workflow_context:
            qa_context.architecture = workflow_context["architecture"]

        return qa_context

    async def _analyze_requirements_testability(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze requirements for testability"""
        analysis = {
            "testable_requirements": 0,
            "untestable_requirements": 0,
            "ambiguous_requirements": 0,
            "recommendations": []
        }

        try:
            requirements = context.get("requirements", [])

            for req in requirements:
                if self._is_requirement_testable(req):
                    analysis["testable_requirements"] += 1
                elif self._is_requirement_ambiguous(req):
                    analysis["ambiguous_requirements"] += 1
                else:
                    analysis["untestable_requirements"] += 1
                    analysis["recommendations"].append(f"Make requirement more specific: {req[:50]}...")

        except Exception as e:
            logger.error(f"Requirements testability analysis failed: {e}")

        return analysis

    async def _generate_test_plan(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive test plan"""
        test_plan = {
            "test_types": [],
            "test_coverage_target": 80.0,
            "test_frameworks": [],
            "test_schedule": {},
            "resource_requirements": []
        }

        try:
            project_type = context.get("project_type", "web")
            quality_level = context.get("quality_level", "standard")

            # Determine test types based on project type
            if project_type in ["web", "frontend"]:
                test_plan["test_types"] = ["unit", "integration", "e2e", "accessibility"]
                test_plan["test_frameworks"] = ["jest", "playwright", "axe-core"]
            elif project_type in ["api", "backend"]:
                test_plan["test_types"] = ["unit", "integration", "api", "security", "performance"]
                test_plan["test_frameworks"] = ["jest", "supertest", "k6"]
            else:
                test_plan["test_types"] = ["unit", "integration"]
                test_plan["test_frameworks"] = ["jest"]

            # Set coverage target based on quality level
            coverage_targets = {
                "basic": 60.0,
                "standard": 80.0,
                "comprehensive": 90.0,
                "enterprise": 95.0
            }
            test_plan["test_coverage_target"] = coverage_targets.get(quality_level, 80.0)

        except Exception as e:
            logger.error(f"Test plan generation failed: {e}")

        return test_plan

    async def _setup_test_infrastructure(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Set up test infrastructure"""
        infrastructure = {
            "test_directories_created": False,
            "test_configs_created": False,
            "ci_pipeline_configured": False,
            "test_data_prepared": False
        }

        try:
            project_path = Path(context.get("project_path", "."))

            # Create test directories
            test_dirs = ["tests", "tests/unit", "tests/integration", "tests/e2e"]
            for test_dir in test_dirs:
                (project_path / test_dir).mkdir(parents=True, exist_ok=True)
            infrastructure["test_directories_created"] = True

            # Create basic test configuration files
            await self._create_test_config_files(project_path, context)
            infrastructure["test_configs_created"] = True

            logger.info("Test infrastructure set up successfully")

        except Exception as e:
            logger.error(f"Test infrastructure setup failed: {e}")

        return infrastructure

    # Configuration Management Methods

    def configure_qa_settings(self, config: Dict[str, Any]):
        """Configure QA agent settings"""
        try:
            # Update coverage thresholds
            if "coverage_thresholds" in config:
                self.coverage_thresholds.update(config["coverage_thresholds"])

            # Update test frameworks
            if "test_frameworks" in config:
                self.test_frameworks.update(config["test_frameworks"])

            # Update quality gates
            if "quality_gates" in config:
                self.quality_gates.update(config["quality_gates"])

            # Update security patterns
            if "security_patterns" in config:
                self.security_patterns.update(config["security_patterns"])

            # Update performance benchmarks
            if "performance_benchmarks" in config:
                self.performance_benchmarks.update(config["performance_benchmarks"])

            logger.info("QA agent configuration updated successfully")

        except Exception as e:
            logger.error(f"Failed to configure QA settings: {e}")

    def get_default_configuration(self) -> Dict[str, Any]:
        """Get default QA agent configuration"""
        return {
            "coverage_thresholds": {
                "basic": 60.0,
                "standard": 80.0,
                "comprehensive": 90.0,
                "enterprise": 95.0
            },
            "test_types": {
                "enabled": ["unit", "integration", "e2e"],
                "optional": ["performance", "security", "accessibility", "load", "stress"],
                "required_for_quality_level": {
                    "basic": ["unit"],
                    "standard": ["unit", "integration"],
                    "comprehensive": ["unit", "integration", "e2e", "security"],
                    "enterprise": ["unit", "integration", "e2e", "security", "performance", "accessibility"]
                }
            },
            "frameworks": {
                "javascript": {
                    "unit": ["jest", "vitest", "mocha"],
                    "e2e": ["playwright", "cypress"],
                    "performance": ["k6", "artillery"]
                },
                "typescript": {
                    "unit": ["jest", "vitest"],
                    "e2e": ["playwright", "cypress"]
                },
                "python": {
                    "unit": ["pytest", "unittest"],
                    "performance": ["locust", "pytest-benchmark"]
                }
            },
            "reporting": {
                "formats": ["json", "html", "markdown", "csv"],
                "include_charts": True,
                "include_trends": True,
                "auto_generate": True,
                "save_artifacts": True
            },
            "security": {
                "scan_dependencies": True,
                "scan_code": True,
                "check_configurations": True,
                "severity_threshold": "medium"
            },
            "performance": {
                "enable_profiling": True,
                "memory_threshold_mb": 100,
                "response_time_threshold_ms": 200,
                "throughput_threshold_rps": 100
            },
            "accessibility": {
                "enable_axe_core": True,
                "wcag_level": "AA",
                "include_color_contrast": True,
                "include_keyboard_navigation": True
            },
            "quality_gates": {
                "basic": {
                    "min_coverage": 60.0,
                    "max_critical_issues": 5,
                    "max_high_issues": 10,
                    "required_tests": ["unit"]
                },
                "standard": {
                    "min_coverage": 80.0,
                    "max_critical_issues": 2,
                    "max_high_issues": 5,
                    "required_tests": ["unit", "integration"]
                },
                "comprehensive": {
                    "min_coverage": 90.0,
                    "max_critical_issues": 0,
                    "max_high_issues": 2,
                    "required_tests": ["unit", "integration", "e2e"]
                },
                "enterprise": {
                    "min_coverage": 95.0,
                    "max_critical_issues": 0,
                    "max_high_issues": 0,
                    "required_tests": ["unit", "integration", "e2e", "security", "performance"]
                }
            },
            "feedback": {
                "enable_ai_suggestions": True,
                "include_learning_resources": True,
                "generate_action_items": True,
                "send_to_developer": True,
                "save_feedback_history": True
            },
            "execution": {
                "parallel_execution": True,
                "max_parallel_workers": 4,
                "timeout_seconds": 300,
                "retry_failed_tests": True,
                "max_retries": 2
            }
        }

    def validate_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate QA configuration"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }

        try:
            # Validate coverage thresholds
            if "coverage_thresholds" in config:
                for level, threshold in config["coverage_thresholds"].items():
                    if not isinstance(threshold, (int, float)) or threshold < 0 or threshold > 100:
                        validation_result["errors"].append(f"Invalid coverage threshold for {level}: {threshold}")
                        validation_result["valid"] = False

            # Validate test types
            if "test_types" in config:
                valid_test_types = ["unit", "integration", "e2e", "api", "performance", "security", "accessibility", "load", "stress", "smoke"]
                for test_type in config["test_types"].get("enabled", []):
                    if test_type not in valid_test_types:
                        validation_result["warnings"].append(f"Unknown test type: {test_type}")

            # Validate quality gates
            if "quality_gates" in config:
                for level, gates in config["quality_gates"].items():
                    if "min_coverage" in gates:
                        if gates["min_coverage"] < 0 or gates["min_coverage"] > 100:
                            validation_result["errors"].append(f"Invalid min_coverage for {level}: {gates['min_coverage']}")
                            validation_result["valid"] = False

            # Validate performance thresholds
            if "performance" in config:
                perf_config = config["performance"]
                if "memory_threshold_mb" in perf_config and perf_config["memory_threshold_mb"] <= 0:
                    validation_result["errors"].append("Memory threshold must be positive")
                    validation_result["valid"] = False

                if "response_time_threshold_ms" in perf_config and perf_config["response_time_threshold_ms"] <= 0:
                    validation_result["errors"].append("Response time threshold must be positive")
                    validation_result["valid"] = False

        except Exception as e:
            validation_result["errors"].append(f"Configuration validation error: {e}")
            validation_result["valid"] = False

        return validation_result

    def export_configuration(self, file_path: Path):
        """Export current configuration to file"""
        try:
            config = {
                "coverage_thresholds": self.coverage_thresholds,
                "test_frameworks": self.test_frameworks,
                "quality_gates": self.quality_gates,
                "security_patterns": self.security_patterns,
                "performance_benchmarks": self.performance_benchmarks
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, default=str)

            logger.info(f"Configuration exported to {file_path}")

        except Exception as e:
            logger.error(f"Failed to export configuration: {e}")

    def import_configuration(self, file_path: Path):
        """Import configuration from file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # Validate configuration
            validation = self.validate_configuration(config)
            if not validation["valid"]:
                raise ValueError(f"Invalid configuration: {validation['errors']}")

            # Apply configuration
            self.configure_qa_settings(config)

            logger.info(f"Configuration imported from {file_path}")

        except Exception as e:
            logger.error(f"Failed to import configuration: {e}")

    def get_configuration_template(self, project_type: str = "web") -> Dict[str, Any]:
        """Get configuration template for specific project type"""
        base_config = self.get_default_configuration()

        # Customize based on project type
        if project_type == "web":
            base_config["test_types"]["enabled"] = ["unit", "integration", "e2e", "accessibility"]
            base_config["frameworks"]["javascript"]["accessibility"] = ["axe-core", "jest-axe"]

        elif project_type == "api":
            base_config["test_types"]["enabled"] = ["unit", "integration", "api", "security", "performance"]
            base_config["frameworks"]["javascript"]["api"] = ["supertest", "newman"]

        elif project_type == "mobile":
            base_config["test_types"]["enabled"] = ["unit", "integration", "e2e"]
            base_config["frameworks"]["javascript"]["e2e"] = ["detox", "appium"]

        elif project_type == "desktop":
            base_config["test_types"]["enabled"] = ["unit", "integration"]
            base_config["frameworks"]["javascript"]["e2e"] = ["spectron", "playwright"]

        return base_config

    def _create_execution_summary(self, test_suites: List[TestSuite], generated_tests: List[GeneratedTest]) -> Dict[str, Any]:
        """Create execution summary"""
        total_tests = sum(suite.total_tests for suite in test_suites)
        total_passed = sum(suite.passed_tests for suite in test_suites)
        total_failed = sum(suite.failed_tests for suite in test_suites)
        total_skipped = sum(suite.skipped_tests for suite in test_suites)
        total_duration = sum(suite.total_duration for suite in test_suites)

        return {
            "total_test_suites": len(test_suites),
            "total_tests": total_tests,
            "total_passed": total_passed,
            "total_failed": total_failed,
            "total_skipped": total_skipped,
            "total_duration": total_duration,
            "generated_tests_count": len(generated_tests),
            "pass_rate": (total_passed / total_tests * 100) if total_tests > 0 else 0,
            "test_types": list(set(suite.test_type.value for suite in test_suites))
        }

    async def _calculate_enhanced_quality_score(
        self,
        test_suites: List[TestSuite],
        requirements_compliance: float,
        security_score: float,
        performance_score: float,
        accessibility_score: float,
        maintainability_score: float
    ) -> float:
        """Calculate enhanced quality score with multiple factors"""

        # Calculate test coverage score
        overall_coverage = self._calculate_overall_coverage(test_suites)

        # Calculate test pass rate
        total_tests = sum(suite.total_tests for suite in test_suites)
        total_passed = sum(suite.passed_tests for suite in test_suites)
        test_pass_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0

        # Weighted quality score calculation
        quality_score = (
            test_pass_rate * 0.25 +           # 25% - Test pass rate
            overall_coverage * 0.20 +         # 20% - Test coverage
            requirements_compliance * 0.15 +  # 15% - Requirements compliance
            security_score * 0.20 +           # 20% - Security score
            performance_score * 0.10 +        # 10% - Performance score
            accessibility_score * 0.05 +      # 5% - Accessibility score
            maintainability_score * 0.05      # 5% - Maintainability score
        )

        return min(quality_score, 100.0)

    async def _generate_enhanced_recommendations(
        self,
        context: QAContext,
        test_suites: List[TestSuite],
        security_vulnerabilities: List[SecurityVulnerability],
        performance_metrics: List[PerformanceMetric]
    ) -> List[str]:
        """Generate enhanced recommendations"""
        recommendations = []

        # Test-related recommendations
        total_tests = sum(suite.total_tests for suite in test_suites)
        total_failed = sum(suite.failed_tests for suite in test_suites)

        if total_failed > 0:
            recommendations.append(f"Fix {total_failed} failing tests to improve quality score")

        overall_coverage = self._calculate_overall_coverage(test_suites)
        target_coverage = self.coverage_thresholds[context.quality_level]

        if overall_coverage < target_coverage:
            recommendations.append(f"Increase test coverage from {overall_coverage:.1f}% to {target_coverage:.1f}%")

        # Security recommendations
        critical_vulns = [v for v in security_vulnerabilities if v.severity == SecuritySeverity.CRITICAL]
        high_vulns = [v for v in security_vulnerabilities if v.severity == SecuritySeverity.HIGH]

        if critical_vulns:
            recommendations.append(f"Address {len(critical_vulns)} critical security vulnerabilities immediately")

        if high_vulns:
            recommendations.append(f"Fix {len(high_vulns)} high-severity security issues")

        # Performance recommendations
        failed_performance = [m for m in performance_metrics if m.status == "fail"]

        if failed_performance:
            recommendations.append(f"Optimize {len(failed_performance)} performance metrics")

        # General recommendations
        if not test_suites:
            recommendations.append("Add comprehensive test suites for better quality assurance")

        if context.enable_test_generation and not any("generated" in str(suite.name) for suite in test_suites):
            recommendations.append("Consider enabling automatic test generation for better coverage")

        return recommendations

    # Security configuration check methods
    async def _check_cors_configuration(self, context: QAContext) -> List[SecurityVulnerability]:
        """Check CORS configuration"""
        vulnerabilities = []
        project_files = context.test_config.get("project_files", [])

        cors_patterns = [
            r'cors\s*\(\s*\{\s*origin\s*:\s*["\']?\*["\']?',  # Wildcard CORS
            r'Access-Control-Allow-Origin\s*:\s*\*',
        ]

        for file_path in project_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                for pattern in cors_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        vulnerabilities.append(SecurityVulnerability(
                            vulnerability_type="cors_misconfiguration",
                            severity=SecuritySeverity.MEDIUM,
                            description="Overly permissive CORS configuration",
                            file_path=str(file_path),
                            recommendation="Restrict CORS to specific origins"
                        ))

            except Exception:
                continue

        return vulnerabilities

    async def _check_https_configuration(self, context: QAContext) -> List[SecurityVulnerability]:
        """Check HTTPS configuration"""
        vulnerabilities = []
        project_files = context.test_config.get("project_files", [])

        http_patterns = [
            r'http://(?!localhost|127\.0\.0\.1)',  # HTTP URLs (not localhost)
            r'secure\s*:\s*false',  # Insecure cookies
        ]

        for file_path in project_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                for pattern in http_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        vulnerabilities.append(SecurityVulnerability(
                            vulnerability_type="insecure_communication",
                            severity=SecuritySeverity.MEDIUM,
                            description="Insecure HTTP communication detected",
                            file_path=str(file_path),
                            recommendation="Use HTTPS for all external communications"
                        ))

            except Exception:
                continue

        return vulnerabilities

    async def _check_authentication_configuration(self, context: QAContext) -> List[SecurityVulnerability]:
        """Check authentication configuration"""
        vulnerabilities = []
        project_files = context.test_config.get("project_files", [])

        auth_patterns = [
            r'jwt\s*\(\s*\{\s*secret\s*:\s*["\'][^"\']{1,10}["\']',  # Weak JWT secret
            r'session\s*\(\s*\{\s*secret\s*:\s*["\'][^"\']{1,10}["\']',  # Weak session secret
        ]

        for file_path in project_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                for pattern in auth_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        vulnerabilities.append(SecurityVulnerability(
                            vulnerability_type="weak_authentication",
                            severity=SecuritySeverity.HIGH,
                            description="Weak authentication configuration",
                            file_path=str(file_path),
                            recommendation="Use strong, randomly generated secrets"
                        ))

            except Exception:
                continue

        return vulnerabilities

    async def _check_environment_variables(self, context: QAContext) -> List[SecurityVulnerability]:
        """Check environment variable usage"""
        vulnerabilities = []

        # Check for .env files in version control
        env_files = [
            context.project_path / ".env",
            context.project_path / ".env.local",
            context.project_path / ".env.production"
        ]

        for env_file in env_files:
            if env_file.exists():
                vulnerabilities.append(SecurityVulnerability(
                    vulnerability_type="exposed_secrets",
                    severity=SecuritySeverity.HIGH,
                    description="Environment file may contain secrets",
                    file_path=str(env_file),
                    recommendation="Add .env files to .gitignore and use secure secret management"
                ))

        return vulnerabilities

    async def _execute_test_suites(self, context: QAContext) -> List[TestSuite]:
        """Execute comprehensive test suites"""
        test_suites = []

        # Execute unit tests
        unit_suite = await self._execute_unit_tests(context)
        if unit_suite:
            test_suites.append(unit_suite)

        # Execute integration tests
        integration_suite = await self._execute_integration_tests(context)
        if integration_suite:
            test_suites.append(integration_suite)

        # Execute E2E tests if available
        e2e_suite = await self._execute_e2e_tests(context)
        if e2e_suite:
            test_suites.append(e2e_suite)

        # Execute API tests if applicable
        api_suite = await self._execute_api_tests(context)
        if api_suite:
            test_suites.append(api_suite)

        # Execute functional tests
        functional_suite = await self._execute_functional_tests(context)
        if functional_suite:
            test_suites.append(functional_suite)

        # Execute edge case tests
        edge_case_suite = await self._execute_edge_case_tests(context)
        if edge_case_suite:
            test_suites.append(edge_case_suite)

        # Execute performance tests
        if context.enable_performance_testing:
            performance_suite = await self._execute_performance_tests(context)
            if performance_suite:
                test_suites.append(performance_suite)

        # Execute security tests
        if context.enable_security_scanning:
            security_suite = await self._execute_security_tests(context)
            if security_suite:
                test_suites.append(security_suite)

        # Execute accessibility tests
        if context.enable_accessibility_testing:
            accessibility_suite = await self._execute_accessibility_tests(context)
            if accessibility_suite:
                test_suites.append(accessibility_suite)

        # Execute load tests for performance-critical applications
        load_suite = await self._execute_load_tests(context)
        if load_suite:
            test_suites.append(load_suite)

        # Execute stress tests
        stress_suite = await self._execute_stress_tests(context)
        if stress_suite:
            test_suites.append(stress_suite)

        # Execute smoke tests
        smoke_suite = await self._execute_smoke_tests(context)
        if smoke_suite:
            test_suites.append(smoke_suite)

        return test_suites

    async def _execute_unit_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute unit tests using appropriate framework"""
        logger.info("Executing unit tests...")

        # Detect test framework
        framework = self._detect_test_framework(context.project_path)
        if not framework:
            logger.warning("No test framework detected")
            return None

        framework_config = self.test_frameworks[framework]

        try:
            # Run tests with coverage
            result = await self._run_test_command(
                context.project_path,
                framework_config.get("coverage_command", framework_config["command"])
            )

            # Parse test results
            test_results = await self._parse_test_results(result, TestType.UNIT)

            # Calculate coverage
            coverage = await self._extract_coverage_info(result, context.project_path)

            suite = TestSuite(
                name="Unit Tests",
                test_type=TestType.UNIT,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                skipped_tests=len([t for t in test_results if t.status == TestStatus.SKIPPED]),
                total_duration=sum(t.duration for t in test_results),
                coverage_percentage=coverage
            )

            logger.info(f"Unit tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"Unit test execution failed: {e}")
            return TestSuite(
                name="Unit Tests",
                test_type=TestType.UNIT,
                tests=[TestResult(
                    test_name="Unit Test Execution",
                    test_type=TestType.UNIT,
                    status=TestStatus.ERROR,
                    duration=0.0,
                    error_message=str(e)
                )]
            )

    async def _execute_integration_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute integration tests"""
        logger.info("Executing integration tests...")

        # Look for integration test files
        integration_files = [
            f for f in context.test_config.get("test_files", [])
            if "integration" in str(f).lower()
        ]

        if not integration_files:
            logger.info("No integration tests found")
            return None

        try:
            # Run integration tests
            result = await self._run_test_command(
                context.project_path,
                "npm test -- --testPathPattern=integration"
            )

            test_results = await self._parse_test_results(result, TestType.INTEGRATION)

            suite = TestSuite(
                name="Integration Tests",
                test_type=TestType.INTEGRATION,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                total_duration=sum(t.duration for t in test_results)
            )

            logger.info(f"Integration tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"Integration test execution failed: {e}")
            return None

    async def _execute_e2e_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute end-to-end tests"""
        logger.info("Executing E2E tests...")

        # Check for Playwright or Cypress configuration
        has_playwright = (context.project_path / "playwright.config.ts").exists()
        has_cypress = (context.project_path / "cypress.config.js").exists()

        if not has_playwright and not has_cypress:
            logger.info("No E2E test framework detected")
            return None

        try:
            if has_playwright:
                result = await self._run_test_command(context.project_path, "npx playwright test")
            else:
                result = await self._run_test_command(context.project_path, "npx cypress run")

            test_results = await self._parse_test_results(result, TestType.E2E)

            suite = TestSuite(
                name="End-to-End Tests",
                test_type=TestType.E2E,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                total_duration=sum(t.duration for t in test_results)
            )

            logger.info(f"E2E tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"E2E test execution failed: {e}")
            return None

    async def _execute_api_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute API tests"""
        logger.info("Executing API tests...")

        # Look for API test files or server files
        has_server = any(
            "server" in str(f).lower() or "app" in str(f).lower()
            for f in context.test_config.get("project_files", [])
        )

        if not has_server:
            logger.info("No API server detected")
            return None

        try:
            # Run API tests (typically integration tests for APIs)
            result = await self._run_test_command(
                context.project_path,
                "npm test -- --testPathPattern=api"
            )

            test_results = await self._parse_test_results(result, TestType.API)

            suite = TestSuite(
                name="API Tests",
                test_type=TestType.API,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                total_duration=sum(t.duration for t in test_results)
            )

            logger.info(f"API tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"API test execution failed: {e}")
            return None

    async def _execute_functional_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute functional tests to verify requirements"""
        logger.info("Executing functional tests...")

        try:
            # Look for functional test files
            functional_files = [
                f for f in context.test_config.get("test_files", [])
                if "functional" in str(f).lower() or "feature" in str(f).lower()
            ]

            if not functional_files and context.project_specification:
                # Generate functional tests based on requirements
                functional_tests = await self._generate_functional_tests(context)
                if functional_tests:
                    await self._write_generated_tests(functional_tests, context)

            # Run functional tests
            result = await self._run_test_command(
                context.project_path,
                "npm test -- --testPathPattern=functional"
            )

            test_results = await self._parse_test_results(result, TestType.UNIT)

            suite = TestSuite(
                name="Functional Tests",
                test_type=TestType.UNIT,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                total_duration=sum(t.duration for t in test_results)
            )

            logger.info(f"Functional tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"Functional test execution failed: {e}")
            return None

    async def _execute_edge_case_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute edge case tests for boundary conditions"""
        logger.info("Executing edge case tests...")

        try:
            # Generate edge case tests if not present
            edge_case_tests = await self._generate_edge_case_tests(context)
            if edge_case_tests:
                await self._write_generated_tests(edge_case_tests, context)

            # Run edge case tests
            result = await self._run_test_command(
                context.project_path,
                "npm test -- --testPathPattern=edge"
            )

            test_results = await self._parse_test_results(result, TestType.UNIT)

            suite = TestSuite(
                name="Edge Case Tests",
                test_type=TestType.UNIT,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                total_duration=sum(t.duration for t in test_results)
            )

            logger.info(f"Edge case tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"Edge case test execution failed: {e}")
            return None

    async def _execute_performance_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute performance tests for critical paths"""
        logger.info("Executing performance tests...")

        try:
            # Generate performance tests if not present
            performance_tests = await self._generate_performance_tests(context)
            if performance_tests:
                await self._write_generated_tests(performance_tests, context)

            # Run performance tests with specific configuration
            result = await self._run_test_command(
                context.project_path,
                "npm test -- --testPathPattern=performance --maxWorkers=1"
            )

            test_results = await self._parse_test_results(result, TestType.PERFORMANCE)

            suite = TestSuite(
                name="Performance Tests",
                test_type=TestType.PERFORMANCE,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                total_duration=sum(t.duration for t in test_results)
            )

            logger.info(f"Performance tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"Performance test execution failed: {e}")
            return None

    async def _execute_security_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute security tests for common vulnerabilities"""
        logger.info("Executing security tests...")

        try:
            # Generate security tests if not present
            security_tests = await self._generate_security_tests(context)
            if security_tests:
                await self._write_generated_tests(security_tests, context)

            # Run security tests
            result = await self._run_test_command(
                context.project_path,
                "npm test -- --testPathPattern=security"
            )

            test_results = await self._parse_test_results(result, TestType.SECURITY)

            suite = TestSuite(
                name="Security Tests",
                test_type=TestType.SECURITY,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                total_duration=sum(t.duration for t in test_results)
            )

            logger.info(f"Security tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"Security test execution failed: {e}")
            return None

    async def _execute_accessibility_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute accessibility tests for UI components"""
        logger.info("Executing accessibility tests...")

        try:
            # Check if this is a frontend project
            frontend_files = self._find_frontend_files(context)
            if not frontend_files:
                logger.info("No frontend files detected for accessibility testing")
                return None

            # Generate accessibility tests if not present
            accessibility_tests = await self._generate_accessibility_tests(context)
            if accessibility_tests:
                await self._write_generated_tests(accessibility_tests, context)

            # Run accessibility tests using axe-core or similar
            result = await self._run_test_command(
                context.project_path,
                "npm test -- --testPathPattern=accessibility"
            )

            test_results = await self._parse_test_results(result, TestType.ACCESSIBILITY)

            suite = TestSuite(
                name="Accessibility Tests",
                test_type=TestType.ACCESSIBILITY,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                total_duration=sum(t.duration for t in test_results)
            )

            logger.info(f"Accessibility tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"Accessibility test execution failed: {e}")
            return None

    async def _execute_load_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute load tests for performance-critical applications"""
        logger.info("Executing load tests...")

        try:
            # Check if load testing is applicable
            has_server = any(
                "server" in str(f).lower() or "app" in str(f).lower()
                for f in context.test_config.get("project_files", [])
            )

            if not has_server:
                logger.info("No server detected for load testing")
                return None

            # Generate load tests if not present
            load_tests = await self._generate_load_tests(context)
            if load_tests:
                await self._write_generated_tests(load_tests, context)

            # Run load tests using k6, Artillery, or similar
            result = await self._run_test_command(
                context.project_path,
                "npm test -- --testPathPattern=load"
            )

            test_results = await self._parse_test_results(result, TestType.LOAD)

            suite = TestSuite(
                name="Load Tests",
                test_type=TestType.LOAD,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                total_duration=sum(t.duration for t in test_results)
            )

            logger.info(f"Load tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"Load test execution failed: {e}")
            return None

    async def _execute_stress_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute stress tests to find breaking points"""
        logger.info("Executing stress tests...")

        try:
            # Generate stress tests if not present
            stress_tests = await self._generate_stress_tests(context)
            if stress_tests:
                await self._write_generated_tests(stress_tests, context)

            # Run stress tests
            result = await self._run_test_command(
                context.project_path,
                "npm test -- --testPathPattern=stress --timeout=60000"
            )

            test_results = await self._parse_test_results(result, TestType.STRESS)

            suite = TestSuite(
                name="Stress Tests",
                test_type=TestType.STRESS,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                total_duration=sum(t.duration for t in test_results)
            )

            logger.info(f"Stress tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"Stress test execution failed: {e}")
            return None

    async def _execute_smoke_tests(self, context: QAContext) -> Optional[TestSuite]:
        """Execute smoke tests for basic functionality verification"""
        logger.info("Executing smoke tests...")

        try:
            # Generate smoke tests if not present
            smoke_tests = await self._generate_smoke_tests(context)
            if smoke_tests:
                await self._write_generated_tests(smoke_tests, context)

            # Run smoke tests
            result = await self._run_test_command(
                context.project_path,
                "npm test -- --testPathPattern=smoke"
            )

            test_results = await self._parse_test_results(result, TestType.SMOKE)

            suite = TestSuite(
                name="Smoke Tests",
                test_type=TestType.SMOKE,
                tests=test_results,
                total_tests=len(test_results),
                passed_tests=len([t for t in test_results if t.status == TestStatus.PASSED]),
                failed_tests=len([t for t in test_results if t.status == TestStatus.FAILED]),
                total_duration=sum(t.duration for t in test_results)
            )

            logger.info(f"Smoke tests completed: {suite.passed_tests}/{suite.total_tests} passed")
            return suite

        except Exception as e:
            logger.error(f"Smoke test execution failed: {e}")
            return None

    async def _validate_requirements_compliance(self, context: QAContext) -> float:
        """Validate compliance with original requirements"""
        logger.info("Validating requirements compliance...")

        if not context.project_specification:
            logger.warning("No project specification available for validation")
            return 0.0

        compliance_score = 0.0
        total_checks = 0

        try:
            # Check functional requirements
            functional_score = await self._check_functional_requirements(context)
            compliance_score += functional_score
            total_checks += 1

            # Check non-functional requirements
            nonfunctional_score = await self._check_nonfunctional_requirements(context)
            compliance_score += nonfunctional_score
            total_checks += 1

            # Check user stories implementation
            user_stories_score = await self._check_user_stories_implementation(context)
            compliance_score += user_stories_score
            total_checks += 1

            final_score = compliance_score / total_checks if total_checks > 0 else 0.0
            logger.info(f"Requirements compliance score: {final_score:.2f}")
            return final_score

        except Exception as e:
            logger.error(f"Requirements validation failed: {e}")
            return 0.0

    async def _perform_security_analysis(self, context: QAContext) -> float:
        """Perform security analysis of the generated code"""
        logger.info("Performing security analysis...")

        security_score = 0.0
        total_checks = 0

        try:
            # Check for common security vulnerabilities
            vuln_score = await self._check_security_vulnerabilities(context)
            security_score += vuln_score
            total_checks += 1

            # Check authentication/authorization implementation
            auth_score = await self._check_authentication_implementation(context)
            security_score += auth_score
            total_checks += 1

            # Check input validation
            validation_score = await self._check_input_validation(context)
            security_score += validation_score
            total_checks += 1

            # Check HTTPS and security headers
            headers_score = await self._check_security_headers(context)
            security_score += headers_score
            total_checks += 1

            final_score = security_score / total_checks if total_checks > 0 else 0.0
            logger.info(f"Security score: {final_score:.2f}")
            return final_score

        except Exception as e:
            logger.error(f"Security analysis failed: {e}")
            return 0.0

    async def _analyze_performance(self, context: QAContext) -> float:
        """Analyze performance characteristics"""
        logger.info("Analyzing performance...")

        performance_score = 0.0
        total_checks = 0

        try:
            # Check for performance best practices
            practices_score = await self._check_performance_practices(context)
            performance_score += practices_score
            total_checks += 1

            # Analyze bundle size (for frontend projects)
            bundle_score = await self._analyze_bundle_size(context)
            performance_score += bundle_score
            total_checks += 1

            # Check database query optimization (if applicable)
            db_score = await self._check_database_optimization(context)
            performance_score += db_score
            total_checks += 1

            final_score = performance_score / total_checks if total_checks > 0 else 0.0
            logger.info(f"Performance score: {final_score:.2f}")
            return final_score

        except Exception as e:
            logger.error(f"Performance analysis failed: {e}")
            return 0.0

    async def _calculate_quality_score(self, test_suites: List[TestSuite],
                                     requirements_compliance: float,
                                     security_score: float,
                                     performance_score: float) -> float:
        """Calculate overall quality score"""
        # Weight factors for different aspects
        weights = {
            "test_coverage": 0.3,
            "test_success": 0.2,
            "requirements": 0.2,
            "security": 0.15,
            "performance": 0.15
        }

        # Calculate test metrics
        total_tests = sum(suite.total_tests for suite in test_suites)
        passed_tests = sum(suite.passed_tests for suite in test_suites)
        overall_coverage = self._calculate_overall_coverage(test_suites)

        test_success_rate = passed_tests / total_tests if total_tests > 0 else 0.0
        coverage_score = min(overall_coverage / 100.0, 1.0)

        # Calculate weighted score
        quality_score = (
            weights["test_coverage"] * coverage_score +
            weights["test_success"] * test_success_rate +
            weights["requirements"] * requirements_compliance +
            weights["security"] * security_score +
            weights["performance"] * performance_score
        ) * 100.0

        return min(quality_score, 100.0)

    def _calculate_overall_coverage(self, test_suites: List[TestSuite]) -> float:
        """Calculate overall test coverage"""
        coverage_values = [suite.coverage_percentage for suite in test_suites if suite.coverage_percentage > 0]
        return sum(coverage_values) / len(coverage_values) if coverage_values else 0.0

    async def _generate_recommendations(self, context: QAContext, test_suites: List[TestSuite]) -> List[str]:
        """Generate improvement recommendations"""
        recommendations = []

        # Coverage recommendations
        overall_coverage = self._calculate_overall_coverage(test_suites)
        threshold = self.coverage_thresholds[context.quality_level]

        if overall_coverage < threshold:
            recommendations.append(f"Increase test coverage from {overall_coverage:.1f}% to at least {threshold}%")

        # Failed test recommendations
        for suite in test_suites:
            if suite.failed_tests > 0:
                recommendations.append(f"Fix {suite.failed_tests} failing {suite.test_type.value} tests")

        # Security recommendations
        recommendations.extend(await self._get_security_recommendations(context))

        # Performance recommendations
        recommendations.extend(await self._get_performance_recommendations(context))

        return recommendations

    def _detect_test_framework(self, project_path: Path) -> Optional[str]:
        """Detect the test framework used in the project"""
        package_json = project_path / "package.json"

        if package_json.exists():
            try:
                with open(package_json, 'r') as f:
                    package_data = json.load(f)

                dependencies = {**package_data.get("dependencies", {}), **package_data.get("devDependencies", {})}

                if "jest" in dependencies:
                    return "jest"
                elif "playwright" in dependencies:
                    return "playwright"
                elif "cypress" in dependencies:
                    return "cypress"
            except Exception as e:
                logger.warning(f"Failed to parse package.json: {e}")

        # Check for Python projects
        if (project_path / "requirements.txt").exists() or (project_path / "pyproject.toml").exists():
            return "pytest"

        return None

    async def _run_test_command(self, project_path: Path, command: str) -> Dict[str, Any]:
        """Run a test command and capture output"""
        logger.info(f"Running command: {command}")

        try:
            process = await asyncio.create_subprocess_shell(
                command,
                cwd=project_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            return {
                "returncode": process.returncode,
                "stdout": stdout.decode('utf-8'),
                "stderr": stderr.decode('utf-8'),
                "command": command
            }
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            return {
                "returncode": 1,
                "stdout": "",
                "stderr": str(e),
                "command": command
            }

    async def _parse_test_results(self, command_result: Dict[str, Any], test_type: TestType) -> List[TestResult]:
        """Parse test results from command output"""
        test_results = []

        if command_result["returncode"] != 0:
            # Command failed
            test_results.append(TestResult(
                test_name="Test Execution",
                test_type=test_type,
                status=TestStatus.ERROR,
                duration=0.0,
                error_message=command_result["stderr"]
            ))
            return test_results

        stdout = command_result["stdout"]

        # Parse Jest output
        if "jest" in command_result["command"].lower():
            test_results.extend(self._parse_jest_output(stdout, test_type))

        # Parse Playwright output
        elif "playwright" in command_result["command"].lower():
            test_results.extend(self._parse_playwright_output(stdout, test_type))

        # Parse Cypress output
        elif "cypress" in command_result["command"].lower():
            test_results.extend(self._parse_cypress_output(stdout, test_type))

        # Default parsing for unknown formats
        else:
            test_results.extend(self._parse_generic_output(stdout, test_type))

        return test_results

    def _parse_jest_output(self, output: str, test_type: TestType) -> List[TestResult]:
        """Parse Jest test output"""
        test_results = []

        # Look for test results in Jest format
        test_pattern = r'(PASS|FAIL)\s+(.+?)\s+\((\d+(?:\.\d+)?)\s*s\)'
        matches = re.findall(test_pattern, output)

        for status_str, test_name, duration_str in matches:
            status = TestStatus.PASSED if status_str == "PASS" else TestStatus.FAILED
            duration = float(duration_str)

            test_results.append(TestResult(
                test_name=test_name,
                test_type=test_type,
                status=status,
                duration=duration
            ))

        return test_results

    def _parse_playwright_output(self, output: str, test_type: TestType) -> List[TestResult]:
        """Parse Playwright test output"""
        test_results = []

        # Look for Playwright test results
        test_pattern = r'(\d+)\s+passed.*?(\d+)\s+failed'
        match = re.search(test_pattern, output)

        if match:
            passed_count = int(match.group(1))
            failed_count = int(match.group(2))

            # Create summary results
            for i in range(passed_count):
                test_results.append(TestResult(
                    test_name=f"E2E Test {i+1}",
                    test_type=test_type,
                    status=TestStatus.PASSED,
                    duration=1.0  # Default duration
                ))

            for i in range(failed_count):
                test_results.append(TestResult(
                    test_name=f"E2E Test {passed_count + i + 1}",
                    test_type=test_type,
                    status=TestStatus.FAILED,
                    duration=1.0
                ))

        return test_results

    def _parse_cypress_output(self, output: str, test_type: TestType) -> List[TestResult]:
        """Parse Cypress test output"""
        test_results = []

        # Look for Cypress test results
        test_pattern = r'(\d+)\s+passing.*?(\d+)\s+failing'
        match = re.search(test_pattern, output)

        if match:
            passed_count = int(match.group(1))
            failed_count = int(match.group(2))

            for i in range(passed_count):
                test_results.append(TestResult(
                    test_name=f"Cypress Test {i+1}",
                    test_type=test_type,
                    status=TestStatus.PASSED,
                    duration=1.0
                ))

            for i in range(failed_count):
                test_results.append(TestResult(
                    test_name=f"Cypress Test {passed_count + i + 1}",
                    test_type=test_type,
                    status=TestStatus.FAILED,
                    duration=1.0
                ))

        return test_results

    def _parse_generic_output(self, output: str, test_type: TestType) -> List[TestResult]:
        """Parse generic test output"""
        # Create a basic result based on command success
        return [TestResult(
            test_name="Generic Test",
            test_type=test_type,
            status=TestStatus.PASSED,
            duration=1.0
        )]

    async def _extract_coverage_info(self, command_result: Dict[str, Any], project_path: Path) -> float:
        """Extract coverage information from test output"""
        stdout = command_result["stdout"]

        # Look for coverage percentage in output
        coverage_patterns = [
            r'All files\s+\|\s+(\d+(?:\.\d+)?)',  # Jest coverage
            r'Statements\s+:\s+(\d+(?:\.\d+)?)%',  # Istanbul coverage
            r'Coverage:\s+(\d+(?:\.\d+)?)%'        # Generic coverage
        ]

        for pattern in coverage_patterns:
            match = re.search(pattern, stdout)
            if match:
                return float(match.group(1))

        # Look for coverage files
        coverage_files = [
            project_path / "coverage" / "lcov-report" / "index.html",
            project_path / "coverage" / "coverage-summary.json"
        ]

        for coverage_file in coverage_files:
            if coverage_file.exists():
                try:
                    if coverage_file.suffix == ".json":
                        with open(coverage_file, 'r') as f:
                            coverage_data = json.load(f)
                            return coverage_data.get("total", {}).get("lines", {}).get("pct", 0.0)
                except Exception as e:
                    logger.warning(f"Failed to parse coverage file: {e}")

        return 0.0

    async def _check_functional_requirements(self, context: QAContext) -> float:
        """Check if functional requirements are implemented"""
        if not context.project_specification:
            return 0.0

        # Get functional requirements from the requirements dict
        functional_reqs = context.project_specification.requirements.get("functional", [])
        if not functional_reqs:
            return 0.0

        # Analyze source code for requirement implementation
        implemented_count = 0
        total_requirements = len(functional_reqs)

        for requirement in functional_reqs:
            if await self._check_requirement_implementation(requirement, context):
                implemented_count += 1

        return implemented_count / total_requirements if total_requirements > 0 else 0.0

    async def _check_requirement_implementation(self, requirement: str, context: QAContext) -> bool:
        """Check if a specific requirement is implemented"""
        # Simple keyword-based check in source files
        keywords = self._extract_keywords_from_requirement(requirement)

        for file_path in context.test_config.get("project_files", []):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                    if any(keyword.lower() in content for keyword in keywords):
                        return True
            except Exception:
                continue

        return False

    def _extract_keywords_from_requirement(self, requirement: str) -> List[str]:
        """Extract keywords from a requirement description"""
        # Simple keyword extraction
        common_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"}
        words = re.findall(r'\b\w+\b', requirement.lower())
        return [word for word in words if len(word) > 3 and word not in common_words]

    async def _check_nonfunctional_requirements(self, context: QAContext) -> float:
        """Check non-functional requirements compliance"""
        score = 0.0
        checks = 0

        # Check performance requirements
        if await self._has_performance_optimizations(context):
            score += 1.0
        checks += 1

        # Check security requirements
        if await self._has_security_measures(context):
            score += 1.0
        checks += 1

        # Check scalability considerations
        if await self._has_scalability_features(context):
            score += 1.0
        checks += 1

        return score / checks if checks > 0 else 0.0

    async def _check_user_stories_implementation(self, context: QAContext) -> float:
        """Check if user stories are implemented"""
        if not context.project_specification or not context.project_specification.user_stories:
            return 0.0

        implemented_count = 0
        total_stories = len(context.project_specification.user_stories)

        for story in context.project_specification.user_stories:
            if await self._check_user_story_implementation(story, context):
                implemented_count += 1

        return implemented_count / total_stories if total_stories > 0 else 0.0

    async def _check_user_story_implementation(self, story: Dict[str, Any], context: QAContext) -> bool:
        """Check if a user story is implemented"""
        # Extract key actions and features from user story
        story_text = story.get("description", "")
        keywords = self._extract_keywords_from_requirement(story_text)

        # Check for implementation in source files
        for file_path in context.test_config.get("project_files", []):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                    if any(keyword.lower() in content for keyword in keywords):
                        return True
            except Exception:
                continue

        return False

    async def _check_security_vulnerabilities(self, context: QAContext) -> float:
        """Check for common security vulnerabilities"""
        vulnerabilities_found = 0
        total_checks = 0

        # Check for SQL injection vulnerabilities
        if await self._check_sql_injection_protection(context):
            vulnerabilities_found += 1
        total_checks += 1

        # Check for XSS protection
        if await self._check_xss_protection(context):
            vulnerabilities_found += 1
        total_checks += 1

        # Check for CSRF protection
        if await self._check_csrf_protection(context):
            vulnerabilities_found += 1
        total_checks += 1

        return vulnerabilities_found / total_checks if total_checks > 0 else 0.0

    async def _check_authentication_implementation(self, context: QAContext) -> float:
        """Check authentication implementation"""
        auth_features = 0
        total_features = 0

        # Check for JWT implementation
        if await self._has_jwt_implementation(context):
            auth_features += 1
        total_features += 1

        # Check for password hashing
        if await self._has_password_hashing(context):
            auth_features += 1
        total_features += 1

        # Check for session management
        if await self._has_session_management(context):
            auth_features += 1
        total_features += 1

        return auth_features / total_features if total_features > 0 else 0.0

    async def _check_input_validation(self, context: QAContext) -> float:
        """Check input validation implementation"""
        validation_patterns = [
            r'validate\(',
            r'joi\.',
            r'yup\.',
            r'zod\.',
            r'express-validator'
        ]

        validation_found = False
        for file_path in context.test_config.get("project_files", []):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if any(re.search(pattern, content, re.IGNORECASE) for pattern in validation_patterns):
                        validation_found = True
                        break
            except Exception:
                continue

        return 1.0 if validation_found else 0.0

    async def _check_security_headers(self, context: QAContext) -> float:
        """Check for security headers implementation"""
        security_headers = [
            "helmet",
            "cors",
            "x-frame-options",
            "content-security-policy",
            "x-xss-protection"
        ]

        headers_found = 0
        for file_path in context.test_config.get("project_files", []):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                    for header in security_headers:
                        if header in content:
                            headers_found += 1
                            break
            except Exception:
                continue

        return min(headers_found / len(security_headers), 1.0)

    async def _check_performance_practices(self, context: QAContext) -> float:
        """Check for performance best practices"""
        practices_score = 0.0
        total_practices = 0

        # Check for caching implementation
        if await self._has_caching_implementation(context):
            practices_score += 1.0
        total_practices += 1

        # Check for compression
        if await self._has_compression_enabled(context):
            practices_score += 1.0
        total_practices += 1

        # Check for lazy loading
        if await self._has_lazy_loading(context):
            practices_score += 1.0
        total_practices += 1

        return practices_score / total_practices if total_practices > 0 else 0.0

    async def _analyze_bundle_size(self, context: QAContext) -> float:
        """Analyze frontend bundle size"""
        # Check for webpack or build configuration
        build_files = [
            context.project_path / "webpack.config.js",
            context.project_path / "vite.config.js",
            context.project_path / "next.config.js"
        ]

        has_build_optimization = any(f.exists() for f in build_files)
        return 1.0 if has_build_optimization else 0.5

    async def _check_database_optimization(self, context: QAContext) -> float:
        """Check database optimization"""
        db_patterns = [
            r'index\s*\(',
            r'\.index\(',
            r'createIndex',
            r'ensureIndex',
            r'query\s*optimization'
        ]

        optimization_found = False
        for file_path in context.test_config.get("project_files", []):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if any(re.search(pattern, content, re.IGNORECASE) for pattern in db_patterns):
                        optimization_found = True
                        break
            except Exception:
                continue

        return 1.0 if optimization_found else 0.5

    # Helper methods for specific checks
    async def _has_performance_optimizations(self, context: QAContext) -> bool:
        """Check if performance optimizations are present"""
        perf_keywords = ["cache", "optimize", "performance", "lazy", "compression"]
        return await self._check_keywords_in_files(context, perf_keywords)

    async def _has_security_measures(self, context: QAContext) -> bool:
        """Check if security measures are implemented"""
        security_keywords = ["helmet", "cors", "auth", "jwt", "bcrypt", "hash"]
        return await self._check_keywords_in_files(context, security_keywords)

    async def _has_scalability_features(self, context: QAContext) -> bool:
        """Check if scalability features are present"""
        scalability_keywords = ["cluster", "worker", "pool", "queue", "cache", "redis"]
        return await self._check_keywords_in_files(context, scalability_keywords)

    async def _check_keywords_in_files(self, context: QAContext, keywords: List[str]) -> bool:
        """Check if any keywords are present in project files"""
        for file_path in context.test_config.get("project_files", []):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                    if any(keyword.lower() in content for keyword in keywords):
                        return True
            except Exception:
                continue
        return False

    # Additional security check methods
    async def _check_sql_injection_protection(self, context: QAContext) -> bool:
        """Check for SQL injection protection"""
        protection_patterns = ["parameterized", "prepared", "sanitize", "escape"]
        return await self._check_keywords_in_files(context, protection_patterns)

    async def _check_xss_protection(self, context: QAContext) -> bool:
        """Check for XSS protection"""
        xss_patterns = ["helmet", "xss", "sanitize", "escape", "content-security-policy"]
        return await self._check_keywords_in_files(context, xss_patterns)

    async def _check_csrf_protection(self, context: QAContext) -> bool:
        """Check for CSRF protection"""
        csrf_patterns = ["csrf", "csurf", "token", "anti-forgery"]
        return await self._check_keywords_in_files(context, csrf_patterns)

    async def _has_jwt_implementation(self, context: QAContext) -> bool:
        """Check for JWT implementation"""
        jwt_patterns = ["jwt", "jsonwebtoken", "bearer", "token"]
        return await self._check_keywords_in_files(context, jwt_patterns)

    async def _has_password_hashing(self, context: QAContext) -> bool:
        """Check for password hashing"""
        hash_patterns = ["bcrypt", "hash", "salt", "crypto"]
        return await self._check_keywords_in_files(context, hash_patterns)

    async def _has_session_management(self, context: QAContext) -> bool:
        """Check for session management"""
        session_patterns = ["session", "cookie", "express-session"]
        return await self._check_keywords_in_files(context, session_patterns)

    async def _has_caching_implementation(self, context: QAContext) -> bool:
        """Check for caching implementation"""
        cache_patterns = ["cache", "redis", "memcached", "lru"]
        return await self._check_keywords_in_files(context, cache_patterns)

    async def _has_compression_enabled(self, context: QAContext) -> bool:
        """Check for compression"""
        compression_patterns = ["compression", "gzip", "deflate"]
        return await self._check_keywords_in_files(context, compression_patterns)

    async def _has_lazy_loading(self, context: QAContext) -> bool:
        """Check for lazy loading"""
        lazy_patterns = ["lazy", "dynamic import", "code splitting"]
        return await self._check_keywords_in_files(context, lazy_patterns)

    async def _get_security_recommendations(self, context: QAContext) -> List[str]:
        """Get security-specific recommendations"""
        recommendations = []

        if not await self._has_security_measures(context):
            recommendations.append("Implement security headers using helmet.js")

        if not await self._has_jwt_implementation(context):
            recommendations.append("Add JWT-based authentication")

        if not await self._check_input_validation(context):
            recommendations.append("Implement input validation for all endpoints")

        return recommendations

    async def _get_performance_recommendations(self, context: QAContext) -> List[str]:
        """Get performance-specific recommendations"""
        recommendations = []

        if not await self._has_caching_implementation(context):
            recommendations.append("Implement caching strategy for improved performance")

        if not await self._has_compression_enabled(context):
            recommendations.append("Enable compression middleware")

        if not await self._has_lazy_loading(context):
            recommendations.append("Implement lazy loading for better initial load times")

        return recommendations

    async def _save_qa_artifacts(self, context: QAContext, report: QualityReport):
        """Save QA artifacts and reports"""
        qa_dir = context.project_path / "qa_reports"
        qa_dir.mkdir(exist_ok=True)

        # Save quality report
        report_file = qa_dir / f"quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump({
                "project_name": report.project_name,
                "overall_coverage": report.overall_coverage,
                "quality_score": report.quality_score,
                "requirements_compliance": report.requirements_compliance,
                "security_score": report.security_score,
                "performance_score": report.performance_score,
                "test_suites": [
                    {
                        "name": suite.name,
                        "test_type": suite.test_type.value,
                        "total_tests": suite.total_tests,
                        "passed_tests": suite.passed_tests,
                        "failed_tests": suite.failed_tests,
                        "coverage_percentage": suite.coverage_percentage
                    }
                    for suite in report.test_suites
                ],
                "recommendations": report.recommendations,
                "timestamp": report.timestamp
            }, f, indent=2)

        # Save detailed test results
        for suite in report.test_suites:
            suite_file = qa_dir / f"{suite.test_type.value}_results.json"
            with open(suite_file, 'w') as f:
                json.dump({
                    "suite_name": suite.name,
                    "test_type": suite.test_type.value,
                    "summary": {
                        "total_tests": suite.total_tests,
                        "passed_tests": suite.passed_tests,
                        "failed_tests": suite.failed_tests,
                        "skipped_tests": suite.skipped_tests,
                        "total_duration": suite.total_duration,
                        "coverage_percentage": suite.coverage_percentage
                    },
                    "tests": [
                        {
                            "test_name": test.test_name,
                            "status": test.status.value,
                            "duration": test.duration,
                            "error_message": test.error_message,
                            "file_path": test.file_path
                        }
                        for test in suite.tests
                    ]
                }, f, indent=2)

        logger.info(f"QA artifacts saved to {qa_dir}")

    async def _handle_qa_error(self, error: Exception, context: QAContext):
        """Handle QA process errors"""
        logger.error(f"QA process error: {error}")

        # Create error report
        error_report = {
            "timestamp": datetime.now().isoformat(),
            "project_path": str(context.project_path),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "quality_level": context.quality_level.value
        }

        # Save error report
        try:
            qa_dir = context.project_path / "qa_reports"
            qa_dir.mkdir(exist_ok=True)
            error_file = qa_dir / "qa_error.json"
            with open(error_file, 'w') as f:
                json.dump(error_report, f, indent=2)
            logger.info(f"Error report saved to {error_file}")
        except Exception as save_error:
            logger.error(f"Failed to save error report: {save_error}")


# QA Agent Executor for orchestrator integration
class QAAgentExecutor:
    """QA Agent executor for orchestrator integration"""

    def __init__(self):
        self.qa_agent = QAAgent()

    async def execute_qa_agent(self, project_id: str, project_path: str,
                             specification_data: Dict[str, Any] = None,
                             architecture_data: Dict[str, Any] = None,
                             quality_level: str = "standard") -> Dict[str, Any]:
        """Execute QA agent as part of orchestrator workflow"""
        try:
            logger.info(f"Executing QA agent for project {project_id}")

            # Create QA context
            context = QAContext(
                project_path=Path(project_path),
                quality_level=QualityLevel(quality_level)
            )

            # Add specification if available
            if specification_data:
                context.project_specification = self._convert_specification_data(specification_data)

            # Add architecture if available
            if architecture_data:
                context.architecture = self._convert_architecture_data(architecture_data)

            # Execute QA process
            report = await self.qa_agent.execute_qa_process(context)

            return {
                "success": True,
                "project_id": project_id,
                "quality_score": report.quality_score,
                "overall_coverage": report.overall_coverage,
                "requirements_compliance": report.requirements_compliance,
                "security_score": report.security_score,
                "performance_score": report.performance_score,
                "test_suites": len(report.test_suites),
                "recommendations": report.recommendations,
                "report_path": str(context.project_path / "qa_reports")
            }

        except Exception as e:
            logger.error(f"QA agent execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "project_id": project_id
            }

    def _convert_specification_data(self, spec_data: Dict[str, Any]) -> ProjectSpecification:
        """Convert specification data to ProjectSpecification object"""
        # This would need to match the actual ProjectSpecification structure
        return ProjectSpecification(
            project_name=spec_data.get("project_name", "Unknown Project"),
            description=spec_data.get("description", "Generated project"),
            requirements=spec_data.get("requirements", {
                "functional": spec_data.get("functional_requirements", [])
            }),
            user_stories=spec_data.get("user_stories", []),
            technical_stack=spec_data.get("technical_stack", {}),
            architecture=spec_data.get("architecture", {}),
            constraints=spec_data.get("constraints", []),
            success_metrics=spec_data.get("success_metrics", []),
            risks=spec_data.get("risks", [])
        )

    def _convert_architecture_data(self, arch_data: Dict[str, Any]) -> SystemArchitecture:
        """Convert architecture data to SystemArchitecture object"""
        # This would need to match the actual SystemArchitecture structure
        # For now, create a basic implementation
        from architect_agent import ArchitecturePattern, ScalabilityTier

        return SystemArchitecture(
            project_name=arch_data.get("project_name", "Unknown Project"),
            architecture_pattern=ArchitecturePattern(arch_data.get("architecture_pattern", "layered")),
            scalability_tier=ScalabilityTier(arch_data.get("scalability_tier", "medium")),
            components=arch_data.get("components", []),
            data_architecture=arch_data.get("data_architecture", {}),
            security_architecture=arch_data.get("security_architecture", {}),
            deployment_architecture=arch_data.get("deployment_architecture", {}),
            integration_patterns=arch_data.get("integration_patterns", []),
            technology_stack=arch_data.get("technology_stack", {}),
            quality_attributes=arch_data.get("quality_attributes", {}),
            constraints=arch_data.get("constraints", []),
            assumptions=arch_data.get("assumptions", []),
            risks=arch_data.get("risks", [])
        )
