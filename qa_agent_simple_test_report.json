{"test_summary": {"total_scenarios": 5, "passed": 5, "failed": 0, "success_rate": 100.0}, "scenario_results": [{"scenario": "test_enums", "success": true, "result": {"quality_levels_count": 4, "test_types_count": 10, "languages_count": 7}}, {"scenario": "test_configuration", "success": true, "result": {"config_valid": true, "coverage_levels": 4, "supported_frameworks": 7}}, {"scenario": "test_test_generation_logic", "success": true, "result": {"templates_valid": true, "languages_supported": 2, "template_types": 2}}, {"scenario": "test_quality_gates", "success": true, "result": {"quality_gates_valid": true, "quality_levels": 4, "strictest_coverage": 95.0}}, {"scenario": "test_framework_support", "success": true, "result": {"framework_support_valid": true, "languages_supported": 5, "total_frameworks": 19}}], "qa_agent_core_features": {"enum_definitions": true, "configuration_system": true, "test_generation_logic": true, "quality_gates": true, "framework_support": true}}