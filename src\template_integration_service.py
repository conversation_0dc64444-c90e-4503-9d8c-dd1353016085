#!/usr/bin/env python3
"""
Template Integration Service for Aetherforge
Integrates project templates with the orchestrator and agent system
"""

import asyncio
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import asdict

from project_templates import (
    ProjectTemplateManager, ProjectTemplate, ProjectDomain, 
    ProjectComplexity, TechnologyStack, create_template_manager
)

# Import orchestrator components (would be actual imports in real implementation)
try:
    from orchestrator import AetherforgeOrchestrator
    from analyst_agent import ProjectSpecification
    from architect_agent import SystemArchitecture
except ImportError:
    # Mock classes for demonstration
    class AetherforgeOrchestrator:
        pass
    
    class ProjectSpecification:
        pass
    
    class SystemArchitecture:
        pass

logger = logging.getLogger(__name__)

class TemplateIntegrationService:
    """Service that integrates project templates with the Aetherforge system"""
    
    def __init__(self, orchestrator: Optional[AetherforgeOrchestrator] = None):
        self.template_manager = create_template_manager()
        self.orchestrator = orchestrator
        
    async def get_available_templates(self, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Get available templates with optional filtering"""
        try:
            if not filters:
                return self.template_manager.list_all_templates()
            
            templates = list(self.template_manager.templates.values())
            
            # Apply filters
            if 'domain' in filters:
                domain = ProjectDomain(filters['domain'])
                templates = [t for t in templates if t.domain == domain]
            
            if 'complexity' in filters:
                complexity = ProjectComplexity(filters['complexity'])
                templates = [t for t in templates if t.complexity == complexity]
            
            if 'tech_stack' in filters:
                tech_stack = TechnologyStack(filters['tech_stack'])
                templates = [t for t in templates if t.tech_stack == tech_stack]
            
            if 'max_hours' in filters:
                max_hours = filters['max_hours']
                templates = [t for t in templates if t.estimated_hours <= max_hours]
            
            if 'search' in filters:
                search_term = filters['search']
                templates = [t for t in templates if 
                           search_term.lower() in t.name.lower() or 
                           search_term.lower() in t.description.lower()]
            
            # Convert to dict format
            return [
                {
                    "id": t.id,
                    "name": t.name,
                    "description": t.description,
                    "domain": t.domain.value,
                    "complexity": t.complexity.value,
                    "tech_stack": t.tech_stack.value,
                    "estimated_hours": t.estimated_hours,
                    "feature_count": len(t.features),
                    "tags": t.tags,
                    "prerequisites": t.prerequisites
                }
                for t in templates
            ]
            
        except Exception as e:
            logger.error(f"Failed to get available templates: {e}")
            return []
    
    async def recommend_templates(self, user_requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Recommend templates based on user requirements"""
        try:
            recommendations = self.template_manager.recommend_templates(user_requirements)
            
            return [
                {
                    "id": template.id,
                    "name": template.name,
                    "description": template.description,
                    "domain": template.domain.value,
                    "complexity": template.complexity.value,
                    "tech_stack": template.tech_stack.value,
                    "estimated_hours": template.estimated_hours,
                    "features": [f.name for f in template.features],
                    "tags": template.tags,
                    "match_score": self._calculate_match_score(template, user_requirements)
                }
                for template in recommendations
            ]
            
        except Exception as e:
            logger.error(f"Failed to recommend templates: {e}")
            return []
    
    def _calculate_match_score(self, template: ProjectTemplate, requirements: Dict[str, Any]) -> float:
        """Calculate how well a template matches user requirements"""
        score = 0.0
        max_score = 0.0
        
        # Domain match (weight: 30%)
        max_score += 30
        if requirements.get('domain') == template.domain.value:
            score += 30
        
        # Complexity match (weight: 20%)
        max_score += 20
        if requirements.get('complexity') == template.complexity.value:
            score += 20
        
        # Tech stack match (weight: 20%)
        max_score += 20
        if requirements.get('tech_stack') == template.tech_stack.value:
            score += 20
        
        # Feature match (weight: 20%)
        max_score += 20
        required_features = requirements.get('features', [])
        if required_features:
            template_features = [f.name for f in template.features]
            matches = sum(1 for feature in required_features if feature in template_features)
            score += (matches / len(required_features)) * 20
        else:
            score += 20  # No specific features required
        
        # Time constraint (weight: 10%)
        max_score += 10
        max_hours = requirements.get('max_hours')
        if max_hours:
            if template.estimated_hours <= max_hours:
                score += 10
            elif template.estimated_hours <= max_hours * 1.2:  # 20% tolerance
                score += 5
        else:
            score += 10  # No time constraint
        
        return (score / max_score) * 100 if max_score > 0 else 0
    
    async def create_project_from_template(
        self, 
        template_id: str, 
        project_config: Dict[str, Any],
        customizations: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a new project based on a template"""
        try:
            # Get the template
            template = self.template_manager.get_template(template_id)
            if not template:
                return {"success": False, "error": f"Template {template_id} not found"}
            
            # Apply customizations if provided
            if customizations:
                template = self.template_manager.customize_template(template_id, customizations)
                if not template:
                    return {"success": False, "error": "Failed to customize template"}
            
            # Validate template
            issues = self.template_manager.validate_template(template)
            if issues:
                return {"success": False, "error": f"Template validation failed: {', '.join(issues)}"}
            
            # Create project specification from template
            project_spec = await self._create_project_specification(template, project_config)
            
            # Create system architecture from template
            architecture = await self._create_system_architecture(template, project_config)
            
            # Generate project structure
            project_structure = await self._generate_project_structure(template, project_config)
            
            # If orchestrator is available, start project creation
            if self.orchestrator:
                project_id = await self._start_orchestrated_creation(
                    template, project_spec, architecture, project_structure, project_config
                )
            else:
                project_id = f"project_{template_id}_{asyncio.get_event_loop().time()}"
            
            return {
                "success": True,
                "project_id": project_id,
                "template_id": template.id,
                "template_name": template.name,
                "estimated_hours": template.estimated_hours,
                "features": [f.name for f in template.features],
                "project_structure": project_structure,
                "next_steps": self._generate_next_steps(template)
            }
            
        except Exception as e:
            logger.error(f"Failed to create project from template {template_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _create_project_specification(
        self, 
        template: ProjectTemplate, 
        project_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create project specification from template"""
        
        # Extract requirements from template features
        functional_requirements = []
        non_functional_requirements = []
        
        for feature in template.features:
            if feature.required:
                functional_requirements.append({
                    "id": f"req_{len(functional_requirements) + 1}",
                    "description": feature.description,
                    "priority": "high",
                    "feature": feature.name
                })
            else:
                functional_requirements.append({
                    "id": f"req_{len(functional_requirements) + 1}",
                    "description": feature.description,
                    "priority": "medium",
                    "feature": feature.name
                })
        
        # Add non-functional requirements based on domain
        if template.domain == ProjectDomain.FINTECH:
            non_functional_requirements.extend([
                {"type": "security", "description": "PCI DSS compliance required"},
                {"type": "performance", "description": "Sub-100ms response time for trading operations"},
                {"type": "availability", "description": "99.9% uptime requirement"}
            ])
        elif template.domain == ProjectDomain.HEALTHCARE:
            non_functional_requirements.extend([
                {"type": "security", "description": "HIPAA compliance required"},
                {"type": "privacy", "description": "End-to-end encryption for patient data"},
                {"type": "audit", "description": "Comprehensive audit logging"}
            ])
        elif template.domain == ProjectDomain.ECOMMERCE:
            non_functional_requirements.extend([
                {"type": "scalability", "description": "Handle 10,000 concurrent users"},
                {"type": "performance", "description": "Page load time under 3 seconds"},
                {"type": "security", "description": "PCI DSS compliance for payments"}
            ])
        
        return {
            "project_name": project_config.get("name", "Untitled Project"),
            "description": project_config.get("description", template.description),
            "domain": template.domain.value,
            "functional_requirements": functional_requirements,
            "non_functional_requirements": non_functional_requirements,
            "constraints": {
                "timeline": project_config.get("timeline", "standard"),
                "budget": project_config.get("budget"),
                "team_size": project_config.get("team_size", 1),
                "complexity": template.complexity.value
            },
            "success_criteria": self._generate_success_criteria(template),
            "assumptions": template.prerequisites
        }
    
    async def _create_system_architecture(
        self, 
        template: ProjectTemplate, 
        project_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create system architecture from template"""
        
        # Extract technology choices from template
        tech_choices = []
        
        # Frontend technology
        if template.tech_stack in [TechnologyStack.REACT_NODE, TechnologyStack.NEXT_FULLSTACK]:
            tech_choices.append({
                "category": "frontend",
                "technology": "React",
                "version": "18.x",
                "justification": "Modern, component-based UI framework"
            })
        elif template.tech_stack == TechnologyStack.VUE_EXPRESS:
            tech_choices.append({
                "category": "frontend",
                "technology": "Vue.js",
                "version": "3.x",
                "justification": "Progressive framework with excellent developer experience"
            })
        
        # Backend technology
        if template.tech_stack in [TechnologyStack.REACT_NODE, TechnologyStack.VUE_EXPRESS]:
            tech_choices.append({
                "category": "backend",
                "technology": "Node.js",
                "version": "18.x",
                "justification": "JavaScript runtime for scalable server applications"
            })
        elif template.tech_stack == TechnologyStack.DJANGO_REACT:
            tech_choices.append({
                "category": "backend",
                "technology": "Django",
                "version": "4.x",
                "justification": "High-level Python web framework"
            })
        
        # Database technology
        database_tech = template.configuration.get("database", "PostgreSQL")
        tech_choices.append({
            "category": "database",
            "technology": database_tech,
            "justification": f"Chosen based on {template.domain.value} domain requirements"
        })
        
        return {
            "architecture_pattern": self._get_architecture_pattern(template),
            "technology_choices": tech_choices,
            "system_components": self._extract_system_components(template),
            "data_flow": self._generate_data_flow(template),
            "deployment_strategy": self._get_deployment_strategy(template),
            "scalability_considerations": self._get_scalability_considerations(template),
            "security_measures": self._get_security_measures(template)
        }
