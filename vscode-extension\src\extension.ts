import * as vscode from 'vscode';
import { spawn, spawnSync, ChildProcess } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';
import * as os from 'os';
import axios from 'axios';
import { WorkspaceCommands } from './workspace-commands';
import { FileSystemService } from './file-system-service';
import { WorkspaceManager } from './workspace-manager';
import { AgentCommunicationPanel } from './agent-communication-panel';
import { EnhancedWebviewProvider } from './enhanced-webview';

// Global workspace services
let workspaceCommands: WorkspaceCommands;
let fileSystemService: FileSystemService;
let workspaceManager: WorkspaceManager;
let agentCommunicationPanel: AgentCommunicationPanel;
let enhancedWebviewProvider: EnhancedWebviewProvider;

// Extension activation function
export function activate(context: vscode.ExtensionContext) {
  console.log('Aetherforge extension is now active!');

  // Initialize workspace services
  workspaceManager = new WorkspaceManager(context);
  fileSystemService = new FileSystemService(context);
  workspaceCommands = new WorkspaceCommands(context);
  agentCommunicationPanel = new AgentCommunicationPanel(context, workspaceManager);
  enhancedWebviewProvider = new EnhancedWebviewProvider(context);

  // Register the main command
  let disposable = vscode.commands.registerCommand('aetherforge.start', () => {
    enhancedWebviewProvider.show();
  });

  context.subscriptions.push(disposable);

  // Register workspace integration commands
  registerWorkspaceCommands(context);
}

// Extension deactivation function
export function deactivate() {
  console.log('🔮 Aetherforge extension deactivated');

  // Dispose workspace services
  if (workspaceCommands) {
    workspaceCommands.dispose();
  }
  if (fileSystemService) {
    fileSystemService.dispose();
  }
  if (workspaceManager) {
    workspaceManager.dispose();
  }
  if (agentCommunicationPanel) {
    agentCommunicationPanel.dispose();
  }
  if (enhancedWebviewProvider) {
    enhancedWebviewProvider.dispose();
  }
}

// Main orchestrator for Aetherforge
async function startAetherforge(context: vscode.ExtensionContext) {
  // Create webview panel for Aetherforge UI
  const panel = vscode.window.createWebviewPanel(
    'aetherforgePanel',
    'Aetherforge',
    vscode.ViewColumn.One,
    {
      enableScripts: true,
      retainContextWhenHidden: true
    }
  );

  // Set up the UI
  panel.webview.html = getWebviewContent();

  // Set up enhanced message handling
  setupWebviewMessageHandling(panel, context);

  // Start real-time monitoring
  startRealTimeMonitoring(panel, context);

  // Setup file system watchers
  setupFileSystemWatchers(context);

  // Setup workspace integration
  setupWorkspaceIntegration(panel, context);

  // Show welcome message
  vscode.window.showInformationMessage('Aetherforge is ready! Use the panel to create autonomous software projects.');
}

// Create a new project based on user prompt
async function createProject(
  prompt: string,
  webview: vscode.Webview,
  projectName?: string,
  projectType?: string,
  workflow?: string
) {
  try {
    webview.postMessage({
      command: 'updateStatus',
      message: 'Starting project creation...'
    });

    // Call the orchestrator API to create the project
    const orchestratorUrl = vscode.workspace.getConfiguration('aetherforge').get('orchestratorUrl') || 'http://localhost:8000';

    const requestData: any = {
      prompt: prompt,
      project_type: projectType || 'fullstack'
    };

    if (projectName) {
      requestData.project_name = projectName;
    }

    if (workflow) {
      requestData.workflow = workflow;
    }

    try {
      const response = await axios.post(`${orchestratorUrl}/projects`, requestData, {
        timeout: 300000 // 5 minutes timeout for project creation
      });

      webview.postMessage({
        command: 'projectCreated',
        data: response.data
      });

      vscode.window.showInformationMessage(`Project "${response.data.project_slug}" created successfully!`);

      // Optionally open the project folder
      const openProject = await vscode.window.showInformationMessage(
        'Would you like to open the generated project?',
        'Yes', 'No'
      );

      if (openProject === 'Yes') {
        const projectPath = response.data.project_path || path.join('projects', response.data.project_slug);
        const projectUri = vscode.Uri.file(projectPath);
        await vscode.commands.executeCommand('vscode.openFolder', projectUri, true);
      }

    } catch (apiError: any) {
      if (apiError.code === 'ECONNREFUSED') {
        webview.postMessage({
          command: 'error',
          message: 'Orchestrator service not running. Please start it first or check the URL in settings.'
        });
        vscode.window.showErrorMessage('Aetherforge orchestrator is not running. Please start the service first.');
      } else if (apiError.code === 'ENOTFOUND') {
        webview.postMessage({
          command: 'error',
          message: 'Cannot reach orchestrator. Please check the URL in settings.'
        });
        vscode.window.showErrorMessage('Cannot reach Aetherforge orchestrator. Please check the URL in settings.');
      } else {
        webview.postMessage({
          command: 'error',
          message: `API Error: ${apiError.message}`
        });
        vscode.window.showErrorMessage(`Failed to create project: ${apiError.message}`);
      }
    }

  } catch (error) {
    webview.postMessage({
      command: 'error',
      message: `Error: ${error}`
    });
    vscode.window.showErrorMessage(`Failed to create project: ${error}`);
  }
}

// Generate webview HTML content
function getWebviewContent() {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aetherforge</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            padding: 20px;
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        h1 {
            color: var(--vscode-textLink-foreground);
            border-bottom: 2px solid var(--vscode-textSeparator-foreground);
            padding-bottom: 15px;
            margin-bottom: 10px;
        }
        .subtitle {
            color: var(--vscode-descriptionForeground);
            font-size: 14px;
            margin-bottom: 20px;
        }
        .input-group {
            margin: 20px 0;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: var(--vscode-foreground);
        }
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
            font-family: var(--vscode-font-family);
            box-sizing: border-box;
        }
        textarea {
            height: 120px;
            resize: vertical;
            font-family: var(--vscode-editor-font-family);
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 8px 8px 8px 0;
            font-weight: bold;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        .primary-button {
            background-color: var(--vscode-textLink-foreground);
            color: var(--vscode-editor-background);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 4px;
            background-color: var(--vscode-textCodeBlock-background);
            border-left: 4px solid var(--vscode-textLink-foreground);
        }
        .error {
            color: var(--vscode-errorForeground);
            background-color: var(--vscode-inputValidation-errorBackground);
            border-left-color: var(--vscode-errorForeground);
        }
        .success {
            color: var(--vscode-terminal-ansiGreen);
            border-left-color: var(--vscode-terminal-ansiGreen);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔮 Aetherforge</h1>
            <div class="subtitle">Autonomous AI Software Creation System</div>
        </div>

        <div class="input-group">
            <label for="prompt">Project Description:</label>
            <textarea id="prompt" placeholder="Describe the software project you want to create in detail. Include features, technologies, and any specific requirements..."></textarea>
        </div>

        <div class="input-group">
            <label for="projectName">Project Name (optional):</label>
            <input type="text" id="projectName" placeholder="MyAwesomeProject">
        </div>

        <div class="input-group">
            <label for="projectType">Project Type:</label>
            <select id="projectType">
                <option value="fullstack">Full Stack Web Application</option>
                <option value="frontend">Frontend Application</option>
                <option value="backend">Backend API Service</option>
                <option value="mobile">Mobile Application</option>
                <option value="desktop">Desktop Application</option>
                <option value="game">Game</option>
                <option value="api">REST API</option>
            </select>
        </div>

        <button class="primary-button" onclick="createProject()">
            Create Project
        </button>
        <button onclick="clearForm()">Clear Form</button>

        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';

            if (type !== 'error') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }

        function createProject() {
            const prompt = document.getElementById('prompt').value;
            const projectName = document.getElementById('projectName').value;
            const projectType = document.getElementById('projectType').value;

            if (!prompt.trim()) {
                showStatus('Please enter a project description', 'error');
                return;
            }

            showStatus('Creating project... This may take a few minutes.', 'info');
            vscode.postMessage({
                command: 'createProject',
                prompt: prompt,
                projectName: projectName,
                projectType: projectType
            });
        }

        function clearForm() {
            document.getElementById('prompt').value = '';
            document.getElementById('projectName').value = '';
            document.getElementById('projectType').value = 'fullstack';
        }

        // Listen for messages from the extension
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.command) {
                case 'updateStatus':
                    showStatus(message.message, 'info');
                    break;
                case 'error':
                    showStatus(message.message, 'error');
                    break;
                case 'projectCreated':
                    showStatus('Project created successfully! Check the projects folder.', 'success');
                    clearForm();
                    break;
            }
        });
    </script>
</body>
</html>`;
}

// Enhanced message handling for the webview
function setupWebviewMessageHandling(panel: vscode.WebviewPanel, context: vscode.ExtensionContext) {
  panel.webview.onDidReceiveMessage(
    async message => {
      switch (message.command) {
        case 'createProject':
          await createProject(message.prompt, panel.webview, message.projectName, message.projectType, message.workflow);
          return;
      }
    },
    undefined,
    context.subscriptions
  );
}

// Start real-time monitoring of project generation
function startRealTimeMonitoring(panel: vscode.WebviewPanel, context: vscode.ExtensionContext) {
  // Monitor pheromone activity every 5 seconds
  const monitoringInterval = setInterval(async () => {
    try {
      const orchestratorUrl = 'http://localhost:8000';

      // Check for active projects and pheromone activity
      const [projectsResponse, pheromoneResponse] = await Promise.allSettled([
        axios.get(`${orchestratorUrl}/projects`, { timeout: 2000 }),
        axios.get(`${orchestratorUrl}/pheromones/statistics`, { timeout: 2000 })
      ]);

      // Update webview with real-time data
      if (projectsResponse.status === 'fulfilled') {
        const activeProjects = projectsResponse.value.data.filter((p: any) =>
          p.status === 'in_progress' || p.status === 'generating'
        );

        if (activeProjects.length > 0) {
          panel.webview.postMessage({
            command: 'activeProjectsUpdate',
            data: {
              count: activeProjects.length,
              projects: activeProjects
            }
          });
        }
      }

    } catch (error) {
      // Silently handle monitoring errors to avoid spam
      console.log('Monitoring error:', error);
    }
  }, 5000);

  // Store interval for cleanup
  context.subscriptions.push({
    dispose: () => clearInterval(monitoringInterval)
  });
}

// Setup file system watchers for project changes
function setupFileSystemWatchers(context: vscode.ExtensionContext) {
  const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
  if (!workspaceRoot) return;

  const projectsPath = path.join(workspaceRoot, 'projects');

  // Watch for new projects being created
  const projectWatcher = vscode.workspace.createFileSystemWatcher(
    new vscode.RelativePattern(projectsPath, '**/README.md')
  );

  projectWatcher.onDidCreate(async (uri) => {
    const projectDir = path.dirname(uri.fsPath);
    const projectName = path.basename(projectDir);

    // Check if this is a new Aetherforge project
    const metadataFile = path.join(projectDir, '.aetherforge.json');
    if (fs.existsSync(metadataFile)) {
      vscode.window.showInformationMessage(
        `✨ New Aetherforge project detected: ${projectName}`,
        'Open Project'
      ).then(selection => {
        if (selection === 'Open Project') {
          vscode.commands.executeCommand('vscode.openFolder', vscode.Uri.file(projectDir), true);
        }
      });
    }
  });

  context.subscriptions.push(projectWatcher);
}

// Register workspace integration commands
function registerWorkspaceCommands(context: vscode.ExtensionContext) {
  // File management commands
  context.subscriptions.push(
    vscode.commands.registerCommand('aetherforge.workspace.createFile', async () => {
      await workspaceCommands.createFile();
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('aetherforge.workspace.createProject', async () => {
      await workspaceCommands.createProject();
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('aetherforge.workspace.createFromTemplate', async () => {
      await workspaceCommands.createProjectFromTemplate();
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('aetherforge.workspace.searchFiles', async () => {
      await workspaceCommands.searchInFiles();
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('aetherforge.workspace.backupFiles', async () => {
      await workspaceCommands.backupFiles();
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('aetherforge.workspace.showInfo', async () => {
      await workspaceCommands.showWorkspaceInfo();
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('aetherforge.workspace.listFiles', async () => {
      await workspaceCommands.listProjectFiles();
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('aetherforge.workspace.watchFiles', async () => {
      await workspaceCommands.setupFileWatcher();
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('aetherforge.workspace.showOperations', async () => {
      await workspaceCommands.showOperations();
    })
  );

  // Agent communication commands
  context.subscriptions.push(
    vscode.commands.registerCommand('aetherforge.showAgentCommunication', async () => {
      await agentCommunicationPanel.showPanel();
    })
  );
}

// Setup workspace integration with webview
function setupWorkspaceIntegration(panel: vscode.WebviewPanel, context: vscode.ExtensionContext) {
  // Enhanced message handling for workspace operations
  panel.webview.onDidReceiveMessage(
    async message => {
      try {
        switch (message.command) {
          case 'workspace.getInfo':
            const workspaceInfo = fileSystemService.getWorkspaceInfo();
            panel.webview.postMessage({
              command: 'workspace.info',
              data: workspaceInfo
            });
            break;

          case 'workspace.createFile':
            const fileUri = await workspaceManager.createFile(
              message.data.path,
              message.data.content || '',
              message.data.encoding || 'utf8'
            );
            panel.webview.postMessage({
              command: 'workspace.fileCreated',
              data: { path: message.data.path, uri: fileUri.toString() }
            });
            break;

          case 'workspace.readFile':
            const content = await workspaceManager.readFile(message.data.path);
            panel.webview.postMessage({
              command: 'workspace.fileContent',
              data: { path: message.data.path, content }
            });
            break;

          case 'workspace.modifyFile':
            await workspaceManager.modifyFile(
              message.data.path,
              message.data.content,
              message.data.encoding || 'utf8'
            );
            panel.webview.postMessage({
              command: 'workspace.fileModified',
              data: { path: message.data.path }
            });
            break;

          case 'workspace.deleteFile':
            await workspaceManager.deleteFile(message.data.path, message.data.recursive || false);
            panel.webview.postMessage({
              command: 'workspace.fileDeleted',
              data: { path: message.data.path }
            });
            break;

          case 'workspace.listFiles':
            const files = await fileSystemService.listFiles(
              message.data.path || '',
              message.data.recursive || false
            );
            panel.webview.postMessage({
              command: 'workspace.fileList',
              data: { path: message.data.path, files }
            });
            break;

          case 'workspace.searchFiles':
            const searchResults = await fileSystemService.searchInFiles(message.data.options);
            panel.webview.postMessage({
              command: 'workspace.searchResults',
              data: { results: searchResults }
            });
            break;

          case 'workspace.createProject':
            const operationId = await fileSystemService.createProject(
              message.data.config,
              message.data.templateId
            );
            panel.webview.postMessage({
              command: 'workspace.projectCreated',
              data: { operationId, config: message.data.config }
            });
            break;

          case 'workspace.getTemplates':
            const templates = fileSystemService.getProjectTemplates();
            panel.webview.postMessage({
              command: 'workspace.templates',
              data: { templates }
            });
            break;

          case 'workspace.getOperation':
            const operation = fileSystemService.getOperation(message.data.operationId);
            panel.webview.postMessage({
              command: 'workspace.operationStatus',
              data: { operation }
            });
            break;

          case 'workspace.batchOperations':
            const batchResults = await workspaceManager.executeBatchOperations(message.data.operations);
            panel.webview.postMessage({
              command: 'workspace.batchCompleted',
              data: { results: batchResults }
            });
            break;
        }
      } catch (error) {
        panel.webview.postMessage({
          command: 'workspace.error',
          data: {
            error: error instanceof Error ? error.message : String(error),
            originalCommand: message.command
          }
        });
      }
    },
    undefined,
    context.subscriptions
  );
}
