import * as vscode from 'vscode';
import * as path from 'path';
import { spawn } from 'child_process';

export interface APIKeyConfig {
    provider: string;
    hasKey: boolean;
    isActive: boolean;
    model: string;
}

export class APIKeyManager {
    private context: vscode.ExtensionContext;
    private outputChannel: vscode.OutputChannel;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.outputChannel = vscode.window.createOutputChannel('Aetherforge API Keys');
    }

    /**
     * Show the API key configuration panel
     */
    async showConfigurationPanel(): Promise<void> {
        const panel = vscode.window.createWebviewPanel(
            'aetherforge.apiKeyConfig',
            'Aetherforge API Key Configuration',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.file(path.join(this.context.extensionPath, 'webview'))
                ]
            }
        );

        panel.webview.html = this.getConfigurationHTML();

        // Handle messages from webview
        panel.webview.onDidReceiveMessage(async (message) => {
            switch (message.command) {
                case 'setApiKey':
                    await this.setApiKey(message.provider, message.apiKey, message.validate);
                    break;
                case 'testApiKey':
                    await this.testApiKey(message.provider);
                    break;
                case 'removeApiKey':
                    await this.removeApiKey(message.provider);
                    break;
                case 'listApiKeys':
                    await this.sendApiKeyList(panel.webview);
                    break;
                case 'runSetupWizard':
                    await this.runSetupWizard();
                    break;
            }
        });

        // Initial load
        await this.sendApiKeyList(panel.webview);
    }

    /**
     * Set an API key for a provider
     */
    private async setApiKey(provider: string, apiKey: string, validate: boolean = true): Promise<void> {
        try {
            const result = await this.executeCliCommand(['set', provider, '--key', apiKey, validate ? '' : '--no-validate']);
            
            if (result.success) {
                vscode.window.showInformationMessage(`✅ API key set successfully for ${provider}`);
            } else {
                vscode.window.showErrorMessage(`❌ Failed to set API key: ${result.error}`);
            }
        } catch (error) {
            vscode.window.showErrorMessage(`❌ Error setting API key: ${error}`);
        }
    }

    /**
     * Test an API key for a provider
     */
    private async testApiKey(provider: string): Promise<void> {
        try {
            const result = await this.executeCliCommand(['test', provider]);
            
            if (result.success) {
                vscode.window.showInformationMessage(`✅ API key is valid for ${provider}`);
            } else {
                vscode.window.showWarningMessage(`⚠️ API key validation failed: ${result.error}`);
            }
        } catch (error) {
            vscode.window.showErrorMessage(`❌ Error testing API key: ${error}`);
        }
    }

    /**
     * Remove an API key for a provider
     */
    private async removeApiKey(provider: string): Promise<void> {
        const confirm = await vscode.window.showWarningMessage(
            `Are you sure you want to remove the API key for ${provider}?`,
            'Yes', 'No'
        );

        if (confirm === 'Yes') {
            try {
                const result = await this.executeCliCommand(['remove', provider]);
                
                if (result.success) {
                    vscode.window.showInformationMessage(`✅ API key removed for ${provider}`);
                } else {
                    vscode.window.showErrorMessage(`❌ Failed to remove API key: ${result.error}`);
                }
            } catch (error) {
                vscode.window.showErrorMessage(`❌ Error removing API key: ${error}`);
            }
        }
    }

    /**
     * Run the setup wizard
     */
    private async runSetupWizard(): Promise<void> {
        try {
            const result = await this.executeCliCommand(['setup']);
            
            if (result.success) {
                vscode.window.showInformationMessage('🎉 Setup wizard completed successfully!');
            } else {
                vscode.window.showErrorMessage(`❌ Setup wizard failed: ${result.error}`);
            }
        } catch (error) {
            vscode.window.showErrorMessage(`❌ Error running setup wizard: ${error}`);
        }
    }

    /**
     * Send API key list to webview
     */
    private async sendApiKeyList(webview: vscode.Webview): Promise<void> {
        try {
            const result = await this.executeCliCommand(['list']);
            
            if (result.success) {
                webview.postMessage({
                    command: 'updateApiKeyList',
                    data: result.data
                });
            }
        } catch (error) {
            this.outputChannel.appendLine(`Error getting API key list: ${error}`);
        }
    }

    /**
     * Execute CLI command
     */
    private async executeCliCommand(args: string[]): Promise<{success: boolean, data?: any, error?: string}> {
        return new Promise((resolve) => {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                resolve({success: false, error: 'No workspace folder found'});
                return;
            }

            const cliPath = path.join(workspaceRoot, 'aetherforge-keys');
            const process = spawn('python', [cliPath, ...args], {
                cwd: workspaceRoot,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            let stdout = '';
            let stderr = '';

            process.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            process.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            process.on('close', (code) => {
                if (code === 0) {
                    try {
                        const data = JSON.parse(stdout);
                        resolve({success: true, data});
                    } catch {
                        resolve({success: true, data: stdout});
                    }
                } else {
                    resolve({success: false, error: stderr || stdout});
                }
            });

            process.on('error', (error) => {
                resolve({success: false, error: error.message});
            });
        });
    }

    /**
     * Get the HTML content for the configuration panel
     */
    private getConfigurationHTML(): string {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aetherforge API Key Configuration</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
            margin: 0;
        }
        
        .header {
            margin-bottom: 30px;
            border-bottom: 1px solid var(--vscode-panel-border);
            padding-bottom: 20px;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            color: var(--vscode-foreground);
        }
        
        .header p {
            margin: 0;
            color: var(--vscode-descriptionForeground);
        }
        
        .provider-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            background-color: var(--vscode-editor-background);
        }
        
        .provider-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .provider-name {
            font-size: 18px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-active {
            background-color: var(--vscode-testing-iconPassed);
            color: white;
        }
        
        .status-configured {
            background-color: var(--vscode-testing-iconQueued);
            color: white;
        }
        
        .status-not-configured {
            background-color: var(--vscode-testing-iconFailed);
            color: white;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
        }
        
        .btn-secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }
        
        .btn-danger {
            background-color: var(--vscode-testing-iconFailed);
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
        
        .setup-section {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed var(--vscode-panel-border);
            border-radius: 6px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: var(--vscode-descriptionForeground);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔑 API Key Configuration</h1>
        <p>Manage your AI provider API keys securely</p>
    </div>
    
    <div class="setup-section">
        <h3>Quick Setup</h3>
        <p>New to Aetherforge? Run the setup wizard to configure your API keys interactively.</p>
        <button class="btn btn-primary" onclick="runSetupWizard()">🚀 Run Setup Wizard</button>
    </div>
    
    <div id="loading" class="loading">
        Loading API key configuration...
    </div>
    
    <div id="providers" style="display: none;">
        <!-- Provider sections will be populated here -->
    </div>
    
    <script>
        const vscode = acquireVsCodeApi();
        
        // Request initial data
        vscode.postMessage({ command: 'listApiKeys' });
        
        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.command) {
                case 'updateApiKeyList':
                    updateProviderList(message.data);
                    break;
            }
        });
        
        function updateProviderList(providers) {
            const loadingDiv = document.getElementById('loading');
            const providersDiv = document.getElementById('providers');
            
            loadingDiv.style.display = 'none';
            providersDiv.style.display = 'block';
            
            providersDiv.innerHTML = providers.map(provider => createProviderSection(provider)).join('');
        }
        
        function createProviderSection(provider) {
            const statusClass = provider.isActive ? 'status-active' : 
                               provider.hasKey ? 'status-configured' : 'status-not-configured';
            const statusText = provider.isActive ? 'Active' : 
                              provider.hasKey ? 'Configured' : 'Not Configured';
            
            return \`
                <div class="provider-section">
                    <div class="provider-header">
                        <span class="provider-name">\${provider.provider}</span>
                        <span class="status-badge \${statusClass}">\${statusText}</span>
                    </div>
                    
                    <div class="form-group">
                        <label for="\${provider.provider}-key">API Key:</label>
                        <input type="password" id="\${provider.provider}-key" placeholder="Enter your \${provider.provider} API key">
                    </div>
                    
                    <div class="button-group">
                        <button class="btn btn-primary" onclick="setApiKey('\${provider.provider}')">Set Key</button>
                        <button class="btn btn-secondary" onclick="testApiKey('\${provider.provider}')">Test Key</button>
                        \${provider.hasKey ? \`<button class="btn btn-danger" onclick="removeApiKey('\${provider.provider}')">Remove Key</button>\` : ''}
                    </div>
                    
                    <p><strong>Model:</strong> \${provider.model}</p>
                </div>
            \`;
        }
        
        function setApiKey(provider) {
            const input = document.getElementById(\`\${provider}-key\`);
            const apiKey = input.value.trim();
            
            if (!apiKey) {
                alert('Please enter an API key');
                return;
            }
            
            vscode.postMessage({
                command: 'setApiKey',
                provider: provider,
                apiKey: apiKey,
                validate: true
            });
            
            input.value = '';
        }
        
        function testApiKey(provider) {
            vscode.postMessage({
                command: 'testApiKey',
                provider: provider
            });
        }
        
        function removeApiKey(provider) {
            vscode.postMessage({
                command: 'removeApiKey',
                provider: provider
            });
        }
        
        function runSetupWizard() {
            vscode.postMessage({
                command: 'runSetupWizard'
            });
        }
    </script>
</body>
</html>
        `;
    }
}
