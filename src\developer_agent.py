"""
Comprehensive Developer Agent for Aetherforge
Generates production-quality code following the architect's specifications,
implementing proper error handling, tests, and documentation.
"""

import asyncio
import json
import logging
import os
import re
from typing import Dict, Any, List, Optional, Tuple, Union
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

# Import architect components for integration
try:
    from src.architect_agent import SystemArchitecture, ArchitecturePattern, TechnologyChoice, ComponentSpecification
    from src.analyst_agent import MCPRAGClient
except ImportError:
    from architect_agent import SystemArchitecture, ArchitecturePattern, TechnologyChoice, ComponentSpecification
    from analyst_agent import MCPRAGClient

logger = logging.getLogger(__name__)

class DeveloperAgentError(Exception):
    """Base exception for Developer Agent errors"""
    pass

class ValidationError(DeveloperAgentError):
    """Raised when validation fails"""
    pass

class CodeGenerationError(DeveloperAgentError):
    """Raised when code generation fails"""
    pass

class FileWriteError(DeveloperAgentError):
    """Raised when file writing fails"""
    pass

class QualityGateError(DeveloperAgentError):
    """Raised when quality gates are not met"""
    pass

class CodeQuality(Enum):
    """Code quality levels"""
    PROTOTYPE = "prototype"
    DEVELOPMENT = "development"
    PRODUCTION = "production"
    ENTERPRISE = "enterprise"

class ProjectType(Enum):
    """Supported project types"""
    FRONTEND = "frontend"
    BACKEND = "backend"
    FULLSTACK = "fullstack"
    API = "api"
    MICROSERVICE = "microservice"
    MOBILE = "mobile"

@dataclass
class CodeFile:
    """Represents a generated code file"""
    path: str
    content: str
    language: str
    file_type: str  # "source", "test", "config", "documentation"
    dependencies: List[str] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)
    exports: List[str] = field(default_factory=list)
    tests_required: bool = True
    documentation_required: bool = True

@dataclass
class TestSuite:
    """Represents a test suite for generated code"""
    name: str
    test_type: str  # "unit", "integration", "e2e", "api"
    files: List[CodeFile]
    coverage_target: float = 80.0
    framework: str = "jest"
    setup_required: bool = True

@dataclass
class ProjectStructure:
    """Represents the complete project structure"""
    name: str
    type: ProjectType
    root_path: str
    source_files: List[CodeFile] = field(default_factory=list)
    test_files: List[CodeFile] = field(default_factory=list)
    config_files: List[CodeFile] = field(default_factory=list)
    documentation_files: List[CodeFile] = field(default_factory=list)
    dependencies: Dict[str, List[str]] = field(default_factory=dict)
    scripts: Dict[str, str] = field(default_factory=dict)
    environment_variables: Dict[str, str] = field(default_factory=dict)

@dataclass
class CodeGenerationContext:
    """Context for code generation"""
    architecture: SystemArchitecture
    project_structure: ProjectStructure
    quality_level: CodeQuality
    target_technologies: Dict[str, List[TechnologyChoice]]
    requirements: Dict[str, Any]
    constraints: List[str]
    coding_standards: Dict[str, Any] = field(default_factory=dict)
    security_requirements: List[str] = field(default_factory=list)
    performance_requirements: Dict[str, Any] = field(default_factory=dict)

class DeveloperAgent:
    """Comprehensive developer agent for production-quality code generation"""

    def __init__(self, mcp_url: str = None, api_manager=None):
        self.mcp_client = MCPRAGClient(mcp_url)

        # Use provided API manager or create new one
        if api_manager:
            self.api_manager = api_manager
        else:
            try:
                from .api_manager import APIManager
            except ImportError:
                from api_manager import APIManager
            self.api_manager = APIManager()
        
        # Code generation templates and patterns
        self.code_templates = self._initialize_code_templates()
        self.coding_standards = self._initialize_coding_standards()
        self.test_patterns = self._initialize_test_patterns()
        
        # Quality gates and validation rules
        self.quality_gates = {
            CodeQuality.PROTOTYPE: {"test_coverage": 0, "documentation": False, "linting": False},
            CodeQuality.DEVELOPMENT: {"test_coverage": 60, "documentation": True, "linting": True},
            CodeQuality.PRODUCTION: {"test_coverage": 80, "documentation": True, "linting": True, "security_scan": True},
            CodeQuality.ENTERPRISE: {"test_coverage": 90, "documentation": True, "linting": True, "security_scan": True, "performance_test": True}
        }
    
    async def generate_project(self, architecture: SystemArchitecture,
                             output_path: Path,
                             quality_level: CodeQuality = CodeQuality.PRODUCTION) -> ProjectStructure:
        """Main method to generate complete project from architecture specifications"""
        logger.info(f"Starting code generation for: {architecture.project_name}")

        try:
            # Step 0: Validate inputs
            await self._validate_inputs(architecture, output_path, quality_level)

            async with self.mcp_client:
                # Step 1: Analyze architecture and create generation context
                context = await self._create_generation_context(architecture, output_path, quality_level)

                # Step 2: Generate project structure
                project_structure = await self._generate_project_structure(context)

                # Step 3: Generate source code files
                await self._generate_source_code(context, project_structure)

                # Step 4: Generate test files
                await self._generate_test_files(context, project_structure)

                # Step 5: Generate configuration files
                await self._generate_configuration_files(context, project_structure)

                # Step 6: Generate documentation
                await self._generate_documentation(context, project_structure)

                # Step 7: Validate code quality
                await self._validate_code_quality(context, project_structure)

                # Step 8: Write files to disk
                await self._write_project_files(project_structure, output_path)

                return project_structure

        except Exception as e:
            logger.error(f"Code generation failed for {architecture.project_name}: {e}")
            await self._handle_generation_error(e, architecture, output_path)
            raise CodeGenerationError(f"Failed to generate project: {e}") from e

    async def _validate_inputs(self, architecture: SystemArchitecture,
                             output_path: Path, quality_level: CodeQuality):
        """Validate input parameters"""
        if not architecture:
            raise ValidationError("Architecture specification is required")

        if not architecture.project_name or not architecture.project_name.strip():
            raise ValidationError("Project name is required")

        if not architecture.technology_stack:
            raise ValidationError("Technology stack is required")

        if not output_path:
            raise ValidationError("Output path is required")

        # Validate project name format
        if not re.match(r'^[a-zA-Z0-9\s\-_]+$', architecture.project_name):
            raise ValidationError("Project name contains invalid characters")

        # Validate technology stack has required categories
        required_categories = self._get_required_tech_categories(architecture)
        missing_categories = []

        for category in required_categories:
            if category not in architecture.technology_stack or not architecture.technology_stack[category]:
                missing_categories.append(category)

        if missing_categories:
            raise ValidationError(f"Missing required technology categories: {', '.join(missing_categories)}")

        # Validate output path is writable
        try:
            output_path.mkdir(parents=True, exist_ok=True)
            test_file = output_path / ".test_write"
            test_file.write_text("test")
            test_file.unlink()
        except Exception as e:
            raise ValidationError(f"Output path is not writable: {e}")

        logger.info("Input validation passed")

    def _get_required_tech_categories(self, architecture: SystemArchitecture) -> List[str]:
        """Get required technology categories based on architecture pattern"""
        base_categories = ["testing"]

        if architecture.architecture_pattern in [ArchitecturePattern.MICROSERVICES]:
            base_categories.extend(["backend", "infrastructure"])
        elif architecture.architecture_pattern == ArchitecturePattern.EVENT_DRIVEN:
            # Event-driven can work with just backend for simple cases
            base_categories.append("backend")
        elif architecture.architecture_pattern == ArchitecturePattern.LAYERED:
            # Check if it's fullstack based on components
            components = [comp.name.lower() for comp in architecture.components]
            has_frontend = any("presentation" in comp or "ui" in comp for comp in components)
            has_backend = any("business" in comp or "api" in comp for comp in components)

            if has_frontend:
                base_categories.append("frontend")
            if has_backend:
                base_categories.append("backend")

            # If no components defined, assume backend for API projects
            if not components:
                base_categories.append("backend")

        return base_categories

    async def _handle_generation_error(self, error: Exception, architecture: SystemArchitecture, output_path: Path):
        """Handle generation errors with recovery mechanisms"""
        logger.error(f"Handling generation error: {error}")

        # Create error report
        error_report = {
            "timestamp": datetime.now().isoformat(),
            "project_name": architecture.project_name,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "architecture_pattern": architecture.architecture_pattern.value,
            "technology_stack": {
                category: [tech.name for tech in technologies]
                for category, technologies in architecture.technology_stack.items()
            }
        }

        # Write error report
        try:
            error_file = output_path / "generation_error.json"
            error_file.parent.mkdir(parents=True, exist_ok=True)
            error_file.write_text(json.dumps(error_report, indent=2))
            logger.info(f"Error report written to: {error_file}")
        except Exception as write_error:
            logger.error(f"Failed to write error report: {write_error}")

        # Attempt partial recovery if possible
        if isinstance(error, FileWriteError):
            logger.info("Attempting to recover from file write error...")
            await self._attempt_file_recovery(output_path)
        elif isinstance(error, QualityGateError):
            logger.info("Quality gate failure - project may still be usable with warnings")

    async def _attempt_file_recovery(self, output_path: Path):
        """Attempt to recover from file write errors"""
        try:
            # Check disk space
            stat = os.statvfs(output_path)
            free_space = stat.f_frsize * stat.f_bavail

            if free_space < 100 * 1024 * 1024:  # Less than 100MB
                logger.warning(f"Low disk space: {free_space / (1024*1024):.1f}MB available")

            # Check permissions
            if not os.access(output_path, os.W_OK):
                logger.error(f"No write permission for {output_path}")

        except Exception as e:
            logger.error(f"Recovery attempt failed: {e}")
    
    def _initialize_code_templates(self) -> Dict[str, Dict[str, str]]:
        """Initialize code templates for different technologies"""
        return {
            "react": {
                "component": """import React from 'react';
import { {interface_name} } from '../types';

interface {component_name}Props {{
  {props}
}}

export const {component_name}: React.FC<{component_name}Props> = ({{
  {prop_destructuring}
}}) => {{
  return (
    <div className="{css_classes}">
      {component_content}
    </div>
  );
}};

export default {component_name};""",
                "hook": """import {{ useState, useEffect }} from 'react';
import {{ {types} }} from '../types';

export const use{hook_name} = ({parameters}) => {{
  {state_declarations}
  
  {effects}
  
  return {{
    {return_object}
  }};
}};""",
                "context": """import React, {{ createContext, useContext, useReducer }} from 'react';
import {{ {types} }} from '../types';

{state_interface}

{action_types}

{reducer_function}

const {context_name}Context = createContext<{{
  state: {state_type};
  dispatch: React.Dispatch<{action_type}>;
}} | undefined>(undefined);

export const {context_name}Provider: React.FC<{{ children: React.ReactNode }}> = ({{ children }}) => {{
  const [state, dispatch] = useReducer({reducer_name}, {initial_state});
  
  return (
    <{context_name}Context.Provider value={{ state, dispatch }}>
      {{children}}
    </{context_name}Context.Provider>
  );
}};

export const use{context_name} = () => {{
  const context = useContext({context_name}Context);
  if (!context) {{
    throw new Error('use{context_name} must be used within a {context_name}Provider');
  }}
  return context;
}};"""
            },
            "express": {
                "controller": """import {{ Request, Response, NextFunction }} from 'express';
import {{ {service_imports} }} from '../services';
import {{ {type_imports} }} from '../types';
import {{ {validation_imports} }} from '../validation';
import {{ ApiError, catchAsync }} from '../utils';

export class {controller_name}Controller {{
  {methods}
}}""",
                "service": """import {{ {model_imports} }} from '../models';
import {{ {type_imports} }} from '../types';
import {{ ApiError }} from '../utils';

export class {service_name}Service {{
  {methods}
}}""",
                "model": """import {{ Schema, model, Document }} from 'mongoose';
import {{ {type_imports} }} from '../types';

{interface_definition}

const {model_name}Schema = new Schema<{interface_name}>{{
  {schema_fields}
}}, {{
  timestamps: true,
  toJSON: {{ virtuals: true }},
  toObject: {{ virtuals: true }}
}});

{schema_methods}

{schema_statics}

export const {model_name} = model<{interface_name}>('{model_name}', {model_name}Schema);""",
                "middleware": """import {{ Request, Response, NextFunction }} from 'express';
import {{ {imports} }} from '../utils';

export const {middleware_name} = ({parameters}) => {{
  return (req: Request, res: Response, next: NextFunction) => {{
    {middleware_logic}
  }};
}};"""
            }
        }
    
    def _initialize_coding_standards(self) -> Dict[str, Any]:
        """Initialize coding standards and best practices"""
        return {
            "typescript": {
                "strict_mode": True,
                "no_any": True,
                "explicit_return_types": True,
                "prefer_interfaces": True,
                "naming_conventions": {
                    "interfaces": "PascalCase",
                    "types": "PascalCase", 
                    "variables": "camelCase",
                    "functions": "camelCase",
                    "classes": "PascalCase",
                    "constants": "UPPER_SNAKE_CASE"
                }
            },
            "react": {
                "functional_components": True,
                "hooks_over_classes": True,
                "prop_types_required": True,
                "default_exports": False,
                "component_file_naming": "PascalCase"
            },
            "node": {
                "async_await_over_promises": True,
                "error_first_callbacks": False,
                "explicit_error_handling": True,
                "environment_variables": True,
                "logging_required": True
            },
            "testing": {
                "test_file_suffix": ".test.ts",
                "describe_blocks": True,
                "setup_teardown": True,
                "mocking_required": True,
                "coverage_threshold": 80
            }
        }
    
    def _initialize_test_patterns(self) -> Dict[str, Dict[str, str]]:
        """Initialize test patterns and templates"""
        return {
            "jest": {
                "unit_test": """import {{ {imports} }} from '{import_path}';
{mock_imports}

{mock_setup}

describe('{test_suite_name}', () => {{
  {setup_teardown}
  
  {test_cases}
}});""",
                "integration_test": """import request from 'supertest';
import {{ app }} from '{app_path}';
import {{ {imports} }} from '{import_path}';

{test_setup}

describe('{endpoint_name} Integration Tests', () => {{
  {setup_teardown}
  
  {test_cases}
}});""",
                "component_test": """import {{ render, screen, fireEvent, waitFor }} from '@testing-library/react';
import {{ {component_name} }} from '{component_path}';
import {{ {context_providers} }} from '{context_path}';

{mock_setup}

const renderComponent = (props = {{}}) => {{
  return render(
    <{context_providers}>
      <{component_name} {{...props}} />
    </{context_providers}>
  );
}};

describe('{component_name}', () => {{
  {test_cases}
}});"""
            }
        }

    async def _create_generation_context(self, architecture: SystemArchitecture,
                                       output_path: Path,
                                       quality_level: CodeQuality) -> CodeGenerationContext:
        """Create generation context from architecture specifications"""

        # Determine project type from architecture pattern
        project_type = self._determine_project_type(architecture)

        # Create project structure
        project_structure = ProjectStructure(
            name=architecture.project_name,
            type=project_type,
            root_path=str(output_path)
        )

        # Extract requirements from architecture
        requirements = {
            "functional": self._extract_functional_requirements(architecture),
            "non_functional": architecture.quality_attributes,
            "security": architecture.security_architecture,
            "performance": architecture.quality_attributes.get("performance", {}),
            "scalability": architecture.scalability_tier.value
        }

        return CodeGenerationContext(
            architecture=architecture,
            project_structure=project_structure,
            quality_level=quality_level,
            target_technologies=architecture.technology_stack,
            requirements=requirements,
            constraints=architecture.constraints,
            coding_standards=self.coding_standards,
            security_requirements=self._extract_security_requirements(architecture),
            performance_requirements=architecture.quality_attributes.get("performance", {})
        )

    def _determine_project_type(self, architecture: SystemArchitecture) -> ProjectType:
        """Determine project type from architecture pattern and components"""
        components = [comp.name.lower() for comp in architecture.components]

        has_frontend = any("presentation" in comp or "ui" in comp or "frontend" in comp for comp in components)
        has_backend = any("business" in comp or "api" in comp or "backend" in comp or "service" in comp for comp in components)

        if architecture.architecture_pattern == ArchitecturePattern.MICROSERVICES:
            return ProjectType.MICROSERVICE
        elif has_frontend and has_backend:
            return ProjectType.FULLSTACK
        elif has_frontend:
            return ProjectType.FRONTEND
        elif has_backend:
            return ProjectType.BACKEND
        else:
            return ProjectType.API

    def _extract_functional_requirements(self, architecture: SystemArchitecture) -> List[str]:
        """Extract functional requirements from architecture components"""
        requirements = []
        for component in architecture.components:
            requirements.extend(component.responsibilities)
        return list(set(requirements))

    def _extract_security_requirements(self, architecture: SystemArchitecture) -> List[str]:
        """Extract security requirements from architecture"""
        security_reqs = []

        # From security architecture
        if architecture.security_architecture:
            auth_strategy = architecture.security_architecture.get("authentication_strategy", {})
            if auth_strategy:
                security_reqs.append(f"Authentication: {auth_strategy.get('method', 'JWT')}")
                security_reqs.append(f"Token lifecycle: {auth_strategy.get('token_lifecycle', 'Standard')}")

            auth_model = architecture.security_architecture.get("authorization_model", {})
            if auth_model:
                security_reqs.append(f"Authorization: {auth_model.get('approach', 'RBAC')}")

        # From components
        for component in architecture.components:
            security_reqs.extend(component.security_requirements)

        return list(set(security_reqs))

    async def _generate_project_structure(self, context: CodeGenerationContext) -> ProjectStructure:
        """Generate the complete project directory structure"""
        structure = context.project_structure

        # Create base directories based on project type and architecture pattern
        if structure.type in [ProjectType.FULLSTACK, ProjectType.FRONTEND]:
            await self._create_frontend_structure(context, structure)

        if structure.type in [ProjectType.FULLSTACK, ProjectType.BACKEND, ProjectType.API, ProjectType.MICROSERVICE]:
            await self._create_backend_structure(context, structure)

        # Add common project files
        await self._create_common_structure(context, structure)

        return structure

    async def _create_frontend_structure(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Create frontend project structure"""
        frontend_tech = context.target_technologies.get("frontend", [])

        # Determine if using React
        is_react = any(tech.name.lower() == "react" for tech in frontend_tech)
        is_typescript = any(tech.name.lower() == "typescript" for tech in frontend_tech)

        if is_react:
            # React project structure
            structure.source_files.extend([
                CodeFile("src/App.tsx" if is_typescript else "src/App.jsx", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile("src/index.tsx" if is_typescript else "src/index.jsx", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile("src/components/index.ts" if is_typescript else "src/components/index.js", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile("src/hooks/index.ts" if is_typescript else "src/hooks/index.js", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile("src/services/index.ts" if is_typescript else "src/services/index.js", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile("src/types/index.ts", "", "typescript", "source") if is_typescript else None,
                CodeFile("src/utils/index.ts" if is_typescript else "src/utils/index.js", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile("src/styles/index.css", "", "css", "source"),
                CodeFile("public/index.html", "", "html", "source")
            ])

            # Remove None values
            structure.source_files = [f for f in structure.source_files if f is not None]

    async def _create_backend_structure(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Create backend project structure"""
        backend_tech = context.target_technologies.get("backend", [])

        # Determine backend technology
        is_node = any(tech.name.lower() in ["node.js", "nodejs"] for tech in backend_tech)
        is_express = any(tech.name.lower() == "express.js" for tech in backend_tech)
        is_typescript = any(tech.name.lower() == "typescript" for tech in backend_tech)

        if is_node:
            # Node.js project structure
            ext = ".ts" if is_typescript else ".js"
            structure.source_files.extend([
                CodeFile(f"src/app{ext}", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile(f"src/server{ext}", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile(f"src/config/index{ext}", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile(f"src/controllers/index{ext}", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile(f"src/services/index{ext}", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile(f"src/models/index{ext}", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile(f"src/middleware/index{ext}", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile(f"src/routes/index{ext}", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile(f"src/utils/index{ext}", "", "typescript" if is_typescript else "javascript", "source"),
                CodeFile("src/types/index.ts", "", "typescript", "source") if is_typescript else None,
                CodeFile(f"src/validation/index{ext}", "", "typescript" if is_typescript else "javascript", "source")
            ])

            # Remove None values
            structure.source_files = [f for f in structure.source_files if f is not None]

    async def _create_common_structure(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Create common project files and directories"""

        # Configuration files
        structure.config_files.extend([
            CodeFile("package.json", "", "json", "config"),
            CodeFile(".gitignore", "", "text", "config"),
            CodeFile(".env.example", "", "text", "config"),
            CodeFile("tsconfig.json", "", "json", "config") if self._uses_typescript(context) else None,
            CodeFile("jest.config.js", "", "javascript", "config"),
            CodeFile("docker-compose.yml", "", "yaml", "config") if self._uses_docker(context) else None,
            CodeFile("Dockerfile", "", "text", "config") if self._uses_docker(context) else None
        ])

        # Documentation files
        structure.documentation_files.extend([
            CodeFile("README.md", "", "markdown", "documentation"),
            CodeFile("docs/API.md", "", "markdown", "documentation"),
            CodeFile("docs/DEVELOPMENT.md", "", "markdown", "documentation"),
            CodeFile("docs/DEPLOYMENT.md", "", "markdown", "documentation")
        ])

        # Remove None values
        structure.config_files = [f for f in structure.config_files if f is not None]

    def _uses_typescript(self, context: CodeGenerationContext) -> bool:
        """Check if project uses TypeScript"""
        all_tech = []
        for tech_list in context.target_technologies.values():
            all_tech.extend(tech_list)
        return any(tech.name.lower() == "typescript" for tech in all_tech)

    def _uses_docker(self, context: CodeGenerationContext) -> bool:
        """Check if project uses Docker"""
        infra_tech = context.target_technologies.get("infrastructure", [])
        return any(tech.name.lower() == "docker" for tech in infra_tech)

    async def _generate_source_code(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate all source code files"""

        # Generate frontend code
        if structure.type in [ProjectType.FULLSTACK, ProjectType.FRONTEND]:
            await self._generate_frontend_code(context, structure)

        # Generate backend code
        if structure.type in [ProjectType.FULLSTACK, ProjectType.BACKEND, ProjectType.API, ProjectType.MICROSERVICE]:
            await self._generate_backend_code(context, structure)

    async def _generate_frontend_code(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate frontend source code"""
        frontend_tech = context.target_technologies.get("frontend", [])
        is_react = any(tech.name.lower() == "react" for tech in frontend_tech)
        is_typescript = any(tech.name.lower() == "typescript" for tech in frontend_tech)

        if is_react:
            # Generate React components
            await self._generate_react_app(context, structure, is_typescript)
            await self._generate_react_components(context, structure, is_typescript)
            await self._generate_react_hooks(context, structure, is_typescript)
            await self._generate_react_services(context, structure, is_typescript)

    async def _generate_backend_code(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate backend source code"""
        backend_tech = context.target_technologies.get("backend", [])
        is_node = any(tech.name.lower() in ["node.js", "nodejs"] for tech in backend_tech)
        is_express = any(tech.name.lower() == "express.js" for tech in backend_tech)
        is_typescript = any(tech.name.lower() == "typescript" for tech in backend_tech)

        if is_node and is_express:
            # Generate Express.js application
            await self._generate_express_app(context, structure, is_typescript)
            await self._generate_express_routes(context, structure, is_typescript)
            await self._generate_express_controllers(context, structure, is_typescript)
            await self._generate_express_services(context, structure, is_typescript)
            await self._generate_express_models(context, structure, is_typescript)
            await self._generate_express_middleware(context, structure, is_typescript)

    async def _generate_react_app(self, context: CodeGenerationContext, structure: ProjectStructure, is_typescript: bool):
        """Generate main React App component"""
        ext = "tsx" if is_typescript else "jsx"

        # Find the App file
        app_file = next((f for f in structure.source_files if f.path.endswith(f"App.{ext}")), None)
        if not app_file:
            return

        # Generate App component content
        app_content = f"""import React from 'react';
import {{ BrowserRouter as Router, Routes, Route }} from 'react-router-dom';
import {{ {context.architecture.project_name.replace(' ', '')}Provider }} from './context/{context.architecture.project_name.replace(' ', '')}Context';
import {{ Header, Footer, LoadingSpinner, ErrorBoundary }} from './components';
import {{ Home, Dashboard, NotFound }} from './pages';
import './styles/App.css';

{f"interface AppProps {{}}" if is_typescript else ""}

const App{f": React.FC<AppProps>" if is_typescript else ""} = () => {{
  return (
    <ErrorBoundary>
      <{context.architecture.project_name.replace(' ', '')}Provider>
        <Router>
          <div className="app">
            <Header />
            <main className="main-content">
              <Routes>
                <Route path="/" element={{<Home />}} />
                <Route path="/dashboard" element={{<Dashboard />}} />
                <Route path="*" element={{<NotFound />}} />
              </Routes>
            </main>
            <Footer />
          </div>
        </Router>
      </{context.architecture.project_name.replace(' ', '')}Provider>
    </ErrorBoundary>
  );
}};

export default App;"""

        app_file.content = app_content
        app_file.imports = ["react", "react-router-dom"]
        app_file.exports = ["App"]

    async def _generate_express_app(self, context: CodeGenerationContext, structure: ProjectStructure, is_typescript: bool):
        """Generate main Express application"""
        ext = "ts" if is_typescript else "js"

        # Find the app file
        app_file = next((f for f in structure.source_files if f.path.endswith(f"app.{ext}")), None)
        if not app_file:
            return

        # Generate Express app content
        type_import = ", { Application }" if is_typescript else ""
        app_declaration = "const app: Application = express();" if is_typescript else "const app = express();"

        app_content = f"""import express{type_import} from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';

{app_declaration}

// Security middleware
app.use(helmet());
app.use(cors({{
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true
}}));

// Rate limiting
const limiter = rateLimit({{
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
}});
app.use(limiter);

// Body parsing middleware
app.use(express.json({{ limit: '10mb' }}));
app.use(express.urlencoded({{ extended: true }}));
app.use(compression());

// Health check endpoint
app.get('/health', (req, res) => {{
  res.status(200).json({{
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  }});
}});

// API routes
app.use('/api', (req, res) => {{
  res.json({{ message: 'API routes will be implemented here' }});
}});

// Error handling middleware
app.use((req, res) => {{
  res.status(404).json({{ error: 'Route not found' }});
}});

app.use((err: any, req: any, res: any, next: any) => {{
  console.error(err.stack);
  res.status(500).json({{ error: 'Something went wrong!' }});
}});

export {{ app }};"""

        app_file.content = app_content
        app_file.imports = ["express", "cors", "helmet", "compression", "express-rate-limit"]
        app_file.exports = ["app"]

    async def _generate_test_files(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate comprehensive test files"""

        # Generate unit tests for source files
        for source_file in structure.source_files:
            if source_file.tests_required:
                test_file = await self._generate_unit_test(context, source_file)
                if test_file:
                    structure.test_files.append(test_file)

        # Generate integration tests
        if structure.type in [ProjectType.FULLSTACK, ProjectType.BACKEND, ProjectType.API]:
            integration_tests = await self._generate_integration_tests(context, structure)
            structure.test_files.extend(integration_tests)

        # Generate E2E tests for frontend
        if structure.type in [ProjectType.FULLSTACK, ProjectType.FRONTEND]:
            e2e_tests = await self._generate_e2e_tests(context, structure)
            structure.test_files.extend(e2e_tests)

    async def _generate_unit_test(self, context: CodeGenerationContext, source_file: CodeFile) -> Optional[CodeFile]:
        """Generate unit test for a source file"""
        if source_file.file_type != "source":
            return None

        # Determine test file path
        test_path = source_file.path.replace("src/", "tests/").replace(f".{source_file.language.split('script')[0]}", f".test.{source_file.language.split('script')[0]}")

        # Generate test content based on file type
        if "component" in source_file.path.lower() or source_file.path.endswith(('.tsx', '.jsx')):
            test_content = await self._generate_component_test(context, source_file)
        elif "service" in source_file.path.lower():
            test_content = await self._generate_service_test(context, source_file)
        elif "controller" in source_file.path.lower():
            test_content = await self._generate_controller_test(context, source_file)
        else:
            test_content = await self._generate_generic_test(context, source_file)

        return CodeFile(
            path=test_path,
            content=test_content,
            language=source_file.language,
            file_type="test",
            tests_required=False,
            documentation_required=False
        )

    async def _generate_component_test(self, context: CodeGenerationContext, source_file: CodeFile) -> str:
        """Generate React component test"""
        component_name = Path(source_file.path).stem

        return f"""import {{ render, screen, fireEvent, waitFor }} from '@testing-library/react';
import {{ {component_name} }} from '../{source_file.path.replace('src/', '').replace('.tsx', '').replace('.jsx', '')}';

describe('{component_name}', () => {{
  it('renders without crashing', () => {{
    render(<{component_name} />);
    expect(screen.getByRole('main')).toBeInTheDocument();
  }});

  it('handles user interactions correctly', async () => {{
    render(<{component_name} />);

    // Add specific interaction tests based on component functionality
    const button = screen.getByRole('button');
    fireEvent.click(button);

    await waitFor(() => {{
      expect(screen.getByText(/success/i)).toBeInTheDocument();
    }});
  }});

  it('displays error states appropriately', () => {{
    render(<{component_name} error="Test error" />);
    expect(screen.getByText(/test error/i)).toBeInTheDocument();
  }});
}});"""

    async def _generate_service_test(self, context: CodeGenerationContext, source_file: CodeFile) -> str:
        """Generate service test"""
        service_name = Path(source_file.path).stem.replace('Service', '')

        return f"""import {{ {service_name}Service }} from '../{source_file.path.replace('src/', '').replace('.ts', '').replace('.js', '')}';

describe('{service_name}Service', () => {{
  let service: {service_name}Service;

  beforeEach(() => {{
    service = new {service_name}Service();
  }});

  afterEach(() => {{
    jest.clearAllMocks();
  }});

  describe('core functionality', () => {{
    it('should initialize correctly', () => {{
      expect(service).toBeDefined();
    }});

    it('should handle successful operations', async () => {{
      // Mock successful operation
      const result = await service.performOperation();
      expect(result).toBeDefined();
    }});

    it('should handle errors gracefully', async () => {{
      // Mock error scenario
      await expect(service.performOperation()).rejects.toThrow();
    }});
  }});
}});"""

    async def _generate_configuration_files(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate all configuration files"""

        # Generate package.json
        await self._generate_package_json(context, structure)

        # Generate TypeScript config if needed
        if self._uses_typescript(context):
            await self._generate_tsconfig(context, structure)

        # Generate Jest config
        await self._generate_jest_config(context, structure)

        # Generate Docker files if needed
        if self._uses_docker(context):
            await self._generate_docker_files(context, structure)

        # Generate environment files
        await self._generate_env_files(context, structure)

        # Generate .gitignore
        await self._generate_gitignore(context, structure)

    async def _generate_package_json(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate package.json file"""
        package_file = next((f for f in structure.config_files if f.path == "package.json"), None)
        if not package_file:
            return

        # Extract dependencies from technology stack
        dependencies = {}
        dev_dependencies = {}

        # Frontend dependencies
        frontend_tech = context.target_technologies.get("frontend", [])
        for tech in frontend_tech:
            if tech.name.lower() == "react":
                dependencies["react"] = f"^{tech.version.replace('.x', '.0')}"
                dependencies["react-dom"] = f"^{tech.version.replace('.x', '.0')}"
            elif tech.name.lower() == "typescript":
                dev_dependencies["typescript"] = f"^{tech.version.replace('.x', '.0')}"
                dev_dependencies["@types/react"] = "^18.0.0"
                dev_dependencies["@types/react-dom"] = "^18.0.0"

        # Backend dependencies
        backend_tech = context.target_technologies.get("backend", [])
        for tech in backend_tech:
            if tech.name.lower() in ["node.js", "nodejs"]:
                continue  # Node.js is runtime, not dependency
            elif tech.name.lower() == "express.js":
                dependencies["express"] = f"^{tech.version.replace('.x', '.0')}"
                dependencies["cors"] = "^2.8.5"
                dependencies["helmet"] = "^7.0.0"
                dependencies["compression"] = "^1.7.4"
                dependencies["express-rate-limit"] = "^6.7.0"

        # Testing dependencies
        testing_tech = context.target_technologies.get("testing", [])
        for tech in testing_tech:
            if tech.name.lower() == "jest":
                dev_dependencies["jest"] = f"^{tech.version.replace('.x', '.0')}"
                dev_dependencies["@testing-library/react"] = "^13.4.0"
                dev_dependencies["@testing-library/jest-dom"] = "^5.16.5"
            elif tech.name.lower() == "supertest":
                dev_dependencies["supertest"] = f"^{tech.version.replace('.x', '.0')}"

        # Scripts
        scripts = {
            "start": "node dist/server.js" if structure.type in [ProjectType.BACKEND, ProjectType.API] else "react-scripts start",
            "build": "tsc" if structure.type in [ProjectType.BACKEND, ProjectType.API] else "react-scripts build",
            "test": "jest",
            "test:watch": "jest --watch",
            "test:coverage": "jest --coverage",
            "lint": "eslint src/**/*.{js,jsx,ts,tsx}",
            "lint:fix": "eslint src/**/*.{js,jsx,ts,tsx} --fix",
            "dev": "nodemon src/server.ts" if structure.type in [ProjectType.BACKEND, ProjectType.API] else "npm start"
        }

        package_content = {
            "name": context.architecture.project_name.lower().replace(" ", "-"),
            "version": "1.0.0",
            "description": f"Generated by Aetherforge - {context.architecture.project_name}",
            "main": "dist/server.js" if structure.type in [ProjectType.BACKEND, ProjectType.API] else "src/index.js",
            "scripts": scripts,
            "dependencies": dependencies,
            "devDependencies": dev_dependencies,
            "keywords": ["aetherforge", "generated", context.architecture.architecture_pattern.value],
            "author": "Aetherforge Developer Agent",
            "license": "MIT",
            "engines": {
                "node": ">=18.0.0",
                "npm": ">=8.0.0"
            }
        }

        package_file.content = json.dumps(package_content, indent=2)

    async def _validate_code_quality(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Validate generated code meets quality standards"""
        quality_gates = self.quality_gates[context.quality_level]

        # Check test coverage requirement
        if quality_gates.get("test_coverage", 0) > 0:
            test_coverage = len(structure.test_files) / max(len(structure.source_files), 1) * 100
            if test_coverage < quality_gates["test_coverage"]:
                logger.warning(f"Test coverage {test_coverage:.1f}% below required {quality_gates['test_coverage']}%")

        # Check documentation requirement
        if quality_gates.get("documentation", False):
            if not structure.documentation_files:
                logger.warning("Documentation required but no documentation files generated")

        # Validate file structure
        await self._validate_file_structure(structure)

        # Validate dependencies
        await self._validate_dependencies(context, structure)

    async def _validate_file_structure(self, structure: ProjectStructure):
        """Validate project file structure"""
        required_files = ["package.json", "README.md"]

        all_files = [f.path for f in structure.source_files + structure.config_files + structure.documentation_files]

        for required_file in required_files:
            if not any(f.endswith(required_file) for f in all_files):
                logger.warning(f"Required file {required_file} not found in project structure")

    async def _validate_dependencies(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Validate project dependencies are consistent"""
        # Check for circular dependencies
        for source_file in structure.source_files:
            if source_file.path in source_file.dependencies:
                logger.warning(f"Circular dependency detected in {source_file.path}")

    async def _write_project_files(self, structure: ProjectStructure, output_path: Path):
        """Write all generated files to disk"""
        base_path = Path(output_path)
        base_path.mkdir(parents=True, exist_ok=True)

        # Write all file types
        all_files = (structure.source_files + structure.test_files +
                    structure.config_files + structure.documentation_files)

        for file in all_files:
            file_path = base_path / file.path
            file_path.parent.mkdir(parents=True, exist_ok=True)

            try:
                file_path.write_text(file.content, encoding='utf-8')
                logger.info(f"Generated: {file.path}")
            except Exception as e:
                logger.error(f"Failed to write {file.path}: {e}")

    # Additional component generators
    async def _generate_react_components(self, context: CodeGenerationContext, structure: ProjectStructure, is_typescript: bool):
        """Generate React components based on architecture"""
        try:
            components_dir = context.project_path / "src" / "components"
            components_dir.mkdir(parents=True, exist_ok=True)

            # Generate common components based on project requirements
            components = [
                ("Header", "Navigation header component"),
                ("Footer", "Page footer component"),
                ("Layout", "Main layout wrapper component"),
                ("LoadingSpinner", "Loading indicator component"),
                ("ErrorBoundary", "Error handling boundary component")
            ]

            for component_name, description in components:
                await self._generate_single_react_component(
                    components_dir, component_name, description, is_typescript
                )

            logger.info(f"Generated {len(components)} React components")

        except Exception as e:
            logger.error(f"Failed to generate React components: {e}")

    async def _generate_single_react_component(self, components_dir: Path, name: str, description: str, is_typescript: bool):
        """Generate a single React component"""
        ext = "tsx" if is_typescript else "jsx"

        component_content = f"""import React from 'react';
import './{name}.css';

interface {name}Props {{
  className?: string;
  children?: React.ReactNode;
}}

const {name}: React.FC<{name}Props> = ({{ className = '', children }}) => {{
  return (
    <div className={{`{name.lower()} ${{className}}`.trim()}}>
      {{children || <span>{description}</span>}}
    </div>
  );
}};

export default {name};
"""

        component_file = components_dir / f"{name}.{ext}"
        component_file.write_text(component_content, encoding='utf-8')

        # Generate corresponding CSS file
        css_content = f""".{name.lower()} {{
  /* {description} styles */
  display: block;
  padding: 1rem;
  margin: 0;
}}

.{name.lower()}--loading {{
  opacity: 0.6;
  pointer-events: none;
}}

.{name.lower()}--error {{
  border: 1px solid #dc3545;
  background-color: #f8d7da;
  color: #721c24;
}}
"""

        css_file = components_dir / f"{name}.css"
        css_file.write_text(css_content, encoding='utf-8')

    async def _generate_react_hooks(self, context: CodeGenerationContext, structure: ProjectStructure, is_typescript: bool):
        """Generate custom React hooks"""
        hooks_dir = context.output_path / "src" / "hooks"
        hooks_dir.mkdir(parents=True, exist_ok=True)

        # Generate useApi hook for API calls
        api_hook_content = f"""import {{ useState, useEffect, useCallback }} from 'react';
import {{ apiClient }} from '../services/api';

interface UseApiOptions {{
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}}

interface UseApiReturn<T> {{
  data: T | null;
  loading: boolean;
  error: Error | null;
  execute: (...args: any[]) => Promise<void>;
  reset: () => void;
}}

export function useApi<T = any>(
  apiFunction: (...args: any[]) => Promise<T>,
  options: UseApiOptions = {{}}
): UseApiReturn<T> {{
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const execute = useCallback(async (...args: any[]) => {{
    try {{
      setLoading(true);
      setError(null);
      const result = await apiFunction(...args);
      setData(result);
      options.onSuccess?.(result);
    }} catch (err) {{
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      options.onError?.(error);
    }} finally {{
      setLoading(false);
    }}
  }}, [apiFunction, options]);

  const reset = useCallback(() => {{
    setData(null);
    setError(null);
    setLoading(false);
  }}, []);

  useEffect(() => {{
    if (options.immediate) {{
      execute();
    }}
  }}, [execute, options.immediate]);

  return {{ data, loading, error, execute, reset }};
}}

// Hook for local storage
export function useLocalStorage<T>(key: string, initialValue: T) {{
  const [storedValue, setStoredValue] = useState<T>(() => {{
    try {{
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    }} catch (error) {{
      console.error(`Error reading localStorage key "${{key}}":`, error);
      return initialValue;
    }}
  }});

  const setValue = useCallback((value: T | ((val: T) => T)) => {{
    try {{
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    }} catch (error) {{
      console.error(`Error setting localStorage key "${{key}}":`, error);
    }}
  }}, [key, storedValue]);

  return [storedValue, setValue] as const;
}}

// Hook for debounced values
export function useDebounce<T>(value: T, delay: number): T {{
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {{
    const handler = setTimeout(() => {{
      setDebouncedValue(value);
    }}, delay);

    return () => {{
      clearTimeout(handler);
    }};
  }}, [value, delay]);

  return debouncedValue;
}}

// Hook for form handling
export function useForm<T extends Record<string, any>>(
  initialValues: T,
  validationSchema?: (values: T) => Record<string, string>
) {{
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({{}});
  const [touched, setTouched] = useState<Record<string, boolean>>({{}});

  const handleChange = useCallback((name: keyof T, value: any) => {{
    setValues(prev => ({{ ...prev, [name]: value }}));
    if (touched[name as string]) {{
      const newErrors = validationSchema?.({{ ...values, [name]: value }}) || {{}};
      setErrors(prev => ({{ ...prev, [name]: newErrors[name as string] || '' }}));
    }}
  }}, [values, touched, validationSchema]);

  const handleBlur = useCallback((name: keyof T) => {{
    setTouched(prev => ({{ ...prev, [name]: true }}));
    const newErrors = validationSchema?.(values) || {{}};
    setErrors(prev => ({{ ...prev, [name]: newErrors[name as string] || '' }}));
  }}, [values, validationSchema]);

  const validate = useCallback(() => {{
    const newErrors = validationSchema?.(values) || {{}};
    setErrors(newErrors);
    setTouched(Object.keys(values).reduce((acc, key) => ({{ ...acc, [key]: true }}), {{}}));
    return Object.keys(newErrors).length === 0;
  }}, [values, validationSchema]);

  const reset = useCallback(() => {{
    setValues(initialValues);
    setErrors({{}});
    setTouched({{}});
  }}, [initialValues]);

  return {{
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    validate,
    reset,
    isValid: Object.keys(errors).length === 0
  }};
}}
"""

        api_hook_file = hooks_dir / f"useApi.{'ts' if is_typescript else 'js'}"
        api_hook_file.write_text(api_hook_content, encoding='utf-8')

        # Generate index file for hooks
        index_content = f"""export {{ useApi, useLocalStorage, useDebounce, useForm }} from './useApi';
"""

        index_file = hooks_dir / f"index.{'ts' if is_typescript else 'js'}"
        index_file.write_text(index_content, encoding='utf-8')

    async def _generate_react_services(self, context: CodeGenerationContext, structure: ProjectStructure, is_typescript: bool):
        """Generate service layer for React app"""
        services_dir = context.output_path / "src" / "services"
        services_dir.mkdir(parents=True, exist_ok=True)

        # Generate API client service
        api_service_content = f"""import axios, {{ AxiosInstance, AxiosRequestConfig, AxiosResponse }} from 'axios';

interface ApiConfig {{
  baseURL: string;
  timeout: number;
  headers?: Record<string, string>;
}}

interface ApiResponse<T = any> {{
  data: T;
  message?: string;
  success: boolean;
}}

class ApiClient {{
  private client: AxiosInstance;
  private token: string | null = null;

  constructor(config: ApiConfig) {{
    this.client = axios.create(config);
    this.setupInterceptors();
  }}

  private setupInterceptors() {{
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {{
        if (this.token) {{
          config.headers.Authorization = `Bearer ${{this.token}}`;
        }}
        return config;
      }},
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error) => {{
        if (error.response?.status === 401) {{
          this.clearToken();
          window.location.href = '/login';
        }}
        return Promise.reject(error);
      }}
    );
  }}

  setToken(token: string) {{
    this.token = token;
    localStorage.setItem('authToken', token);
  }}

  clearToken() {{
    this.token = null;
    localStorage.removeItem('authToken');
  }}

  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {{
    const response = await this.client.get<ApiResponse<T>>(url, config);
    return response.data.data;
  }}

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {{
    const response = await this.client.post<ApiResponse<T>>(url, data, config);
    return response.data.data;
  }}

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {{
    const response = await this.client.put<ApiResponse<T>>(url, data, config);
    return response.data.data;
  }}

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {{
    const response = await this.client.delete<ApiResponse<T>>(url, config);
    return response.data.data;
  }}
}}

// Create API client instance
export const apiClient = new ApiClient({{
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',
  timeout: 10000,
  headers: {{
    'Content-Type': 'application/json',
  }},
}});

// Initialize token from localStorage
const savedToken = localStorage.getItem('authToken');
if (savedToken) {{
  apiClient.setToken(savedToken);
}}

export default apiClient;
"""

        api_service_file = services_dir / f"api.{'ts' if is_typescript else 'js'}"
        api_service_file.write_text(api_service_content, encoding='utf-8')

        # Generate authentication service
        auth_service_content = f"""import {{ apiClient }} from './api';

interface LoginCredentials {{
  email: string;
  password: string;
}}

interface RegisterData {{
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}}

interface User {{
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  createdAt: string;
}}

interface AuthResponse {{
  user: User;
  token: string;
}}

export class AuthService {{
  async login(credentials: LoginCredentials): Promise<AuthResponse> {{
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials);
    apiClient.setToken(response.token);
    return response;
  }}

  async register(data: RegisterData): Promise<AuthResponse> {{
    const response = await apiClient.post<AuthResponse>('/auth/register', data);
    apiClient.setToken(response.token);
    return response;
  }}

  async logout(): Promise<void> {{
    try {{
      await apiClient.post('/auth/logout');
    }} finally {{
      apiClient.clearToken();
    }}
  }}

  async getCurrentUser(): Promise<User> {{
    return apiClient.get<User>('/auth/me');
  }}

  async refreshToken(): Promise<AuthResponse> {{
    const response = await apiClient.post<AuthResponse>('/auth/refresh');
    apiClient.setToken(response.token);
    return response;
  }}

  async forgotPassword(email: string): Promise<void> {{
    await apiClient.post('/auth/forgot-password', {{ email }});
  }}

  async resetPassword(token: string, password: string): Promise<void> {{
    await apiClient.post('/auth/reset-password', {{ token, password }});
  }}

  isAuthenticated(): boolean {{
    return !!localStorage.getItem('authToken');
  }}
}}

export const authService = new AuthService();
"""

        auth_service_file = services_dir / f"auth.{'ts' if is_typescript else 'js'}"
        auth_service_file.write_text(auth_service_content, encoding='utf-8')

        # Generate services index
        services_index_content = f"""export {{ default as apiClient }} from './api';
export {{ authService, AuthService }} from './auth';
export type {{ User, LoginCredentials, RegisterData, AuthResponse }} from './auth';
"""

        services_index_file = services_dir / f"index.{'ts' if is_typescript else 'js'}"
        services_index_file.write_text(services_index_content, encoding='utf-8')

    async def _generate_express_routes(self, context: CodeGenerationContext, structure: ProjectStructure, is_typescript: bool):
        """Generate Express routes"""
        routes_dir = context.output_path / "src" / "routes"
        routes_dir.mkdir(parents=True, exist_ok=True)

        # Generate authentication routes
        auth_routes_content = f"""import {{ Router }} from 'express';
import {{ AuthController }} from '../controllers/AuthController';
import {{ authMiddleware, validateRequest }} from '../middleware';
import {{ body }} from 'express-validator';

const router = Router();
const authController = new AuthController();

// Validation rules
const loginValidation = [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({{ min: 6 }}).withMessage('Password must be at least 6 characters'),
];

const registerValidation = [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({{ min: 6 }}).withMessage('Password must be at least 6 characters'),
  body('firstName').trim().isLength({{ min: 1 }}).withMessage('First name is required'),
  body('lastName').trim().isLength({{ min: 1 }}).withMessage('Last name is required'),
];

// Public routes
router.post('/login', loginValidation, validateRequest, authController.login);
router.post('/register', registerValidation, validateRequest, authController.register);
router.post('/forgot-password',
  body('email').isEmail().normalizeEmail(),
  validateRequest,
  authController.forgotPassword
);
router.post('/reset-password',
  body('token').notEmpty(),
  body('password').isLength({{ min: 6 }}),
  validateRequest,
  authController.resetPassword
);

// Protected routes
router.post('/logout', authMiddleware, authController.logout);
router.get('/me', authMiddleware, authController.getCurrentUser);
router.post('/refresh', authMiddleware, authController.refreshToken);

export default router;
"""

        auth_routes_file = routes_dir / f"auth.{'ts' if is_typescript else 'js'}"
        auth_routes_file.write_text(auth_routes_content, encoding='utf-8')

        # Generate main API routes
        api_routes_content = f"""import {{ Router }} from 'express';
import {{ authMiddleware }} from '../middleware';
import authRoutes from './auth';

const router = Router();

// Health check
router.get('/health', (req, res) => {{
  res.json({{
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  }});
}});

// Authentication routes
router.use('/auth', authRoutes);

// Protected API routes
router.use('/api', authMiddleware);

// Example protected routes
router.get('/api/profile', (req, res) => {{
  res.json({{
    success: true,
    data: req.user,
    message: 'Profile retrieved successfully'
  }});
}});

// Error handling for undefined routes
router.use('*', (req, res) => {{
  res.status(404).json({{
    success: false,
    message: 'Route not found',
    path: req.originalUrl
  }});
}});

export default router;
"""

        api_routes_file = routes_dir / f"index.{'ts' if is_typescript else 'js'}"
        api_routes_file.write_text(api_routes_content, encoding='utf-8')

    async def _generate_express_controllers(self, context: CodeGenerationContext, structure: ProjectStructure, is_typescript: bool):
        """Generate Express controllers"""
        controllers_dir = context.output_path / "src" / "controllers"
        controllers_dir.mkdir(parents=True, exist_ok=True)

        # Generate base controller
        base_controller_content = f"""import {{ Request, Response, NextFunction }} from 'express';
import {{ validationResult }} from 'express-validator';

export interface ApiResponse<T = any> {{
  success: boolean;
  data?: T;
  message?: string;
  errors?: any[];
}}

export abstract class BaseController {{
  protected sendSuccess<T>(res: Response, data: T, message?: string, statusCode: number = 200): void {{
    const response: ApiResponse<T> = {{
      success: true,
      data,
      message
    }};
    res.status(statusCode).json(response);
  }}

  protected sendError(res: Response, message: string, statusCode: number = 400, errors?: any[]): void {{
    const response: ApiResponse = {{
      success: false,
      message,
      errors
    }};
    res.status(statusCode).json(response);
  }}

  protected handleValidationErrors(req: Request, res: Response): boolean {{
    const errors = validationResult(req);
    if (!errors.isEmpty()) {{
      this.sendError(res, 'Validation failed', 400, errors.array());
      return true;
    }}
    return false;
  }}

  protected asyncHandler(fn: (req: Request, res: Response, next: NextFunction) => Promise<void>) {{
    return (req: Request, res: Response, next: NextFunction) => {{
      Promise.resolve(fn(req, res, next)).catch(next);
    }};
  }}
}}
"""

        base_controller_file = controllers_dir / f"BaseController.{'ts' if is_typescript else 'js'}"
        base_controller_file.write_text(base_controller_content, encoding='utf-8')

        # Generate authentication controller
        auth_controller_content = f"""import {{ Request, Response }} from 'express';
import {{ BaseController }} from './BaseController';
import {{ AuthService }} from '../services/AuthService';
import {{ UserService }} from '../services/UserService';

interface AuthenticatedRequest extends Request {{
  user?: any;
}}

export class AuthController extends BaseController {{
  private authService: AuthService;
  private userService: UserService;

  constructor() {{
    super();
    this.authService = new AuthService();
    this.userService = new UserService();
  }}

  login = this.asyncHandler(async (req: Request, res: Response) => {{
    if (this.handleValidationErrors(req, res)) return;

    const {{ email, password }} = req.body;

    try {{
      const result = await this.authService.login(email, password);
      this.sendSuccess(res, result, 'Login successful');
    }} catch (error) {{
      this.sendError(res, error.message || 'Login failed', 401);
    }}
  }});

  register = this.asyncHandler(async (req: Request, res: Response) => {{
    if (this.handleValidationErrors(req, res)) return;

    const {{ email, password, firstName, lastName }} = req.body;

    try {{
      const result = await this.authService.register({{
        email,
        password,
        firstName,
        lastName
      }});
      this.sendSuccess(res, result, 'Registration successful', 201);
    }} catch (error) {{
      this.sendError(res, error.message || 'Registration failed', 400);
    }}
  }});

  logout = this.asyncHandler(async (req: AuthenticatedRequest, res: Response) => {{
    try {{
      await this.authService.logout(req.user.id);
      this.sendSuccess(res, null, 'Logout successful');
    }} catch (error) {{
      this.sendError(res, error.message || 'Logout failed', 400);
    }}
  }});

  getCurrentUser = this.asyncHandler(async (req: AuthenticatedRequest, res: Response) => {{
    try {{
      const user = await this.userService.findById(req.user.id);
      this.sendSuccess(res, user, 'User retrieved successfully');
    }} catch (error) {{
      this.sendError(res, error.message || 'Failed to get user', 400);
    }}
  }});

  refreshToken = this.asyncHandler(async (req: AuthenticatedRequest, res: Response) => {{
    try {{
      const result = await this.authService.refreshToken(req.user.id);
      this.sendSuccess(res, result, 'Token refreshed successfully');
    }} catch (error) {{
      this.sendError(res, error.message || 'Token refresh failed', 401);
    }}
  }});

  forgotPassword = this.asyncHandler(async (req: Request, res: Response) => {{
    if (this.handleValidationErrors(req, res)) return;

    const {{ email }} = req.body;

    try {{
      await this.authService.forgotPassword(email);
      this.sendSuccess(res, null, 'Password reset email sent');
    }} catch (error) {{
      this.sendError(res, error.message || 'Failed to send reset email', 400);
    }}
  }});

  resetPassword = this.asyncHandler(async (req: Request, res: Response) => {{
    if (this.handleValidationErrors(req, res)) return;

    const {{ token, password }} = req.body;

    try {{
      await this.authService.resetPassword(token, password);
      this.sendSuccess(res, null, 'Password reset successful');
    }} catch (error) {{
      this.sendError(res, error.message || 'Password reset failed', 400);
    }}
  }});
}}
"""

        auth_controller_file = controllers_dir / f"AuthController.{'ts' if is_typescript else 'js'}"
        auth_controller_file.write_text(auth_controller_content, encoding='utf-8')

        # Generate controllers index
        controllers_index_content = f"""export {{ BaseController }} from './BaseController';
export {{ AuthController }} from './AuthController';
export type {{ ApiResponse }} from './BaseController';
"""

        controllers_index_file = controllers_dir / f"index.{'ts' if is_typescript else 'js'}"
        controllers_index_file.write_text(controllers_index_content, encoding='utf-8')

    async def _generate_express_services(self, context: CodeGenerationContext, structure: ProjectStructure, is_typescript: bool):
        """Generate Express services"""
        services_dir = context.output_path / "src" / "services"
        services_dir.mkdir(parents=True, exist_ok=True)

        # Generate base service
        base_service_content = f"""import {{ logger }} from '../utils/logger';

export abstract class BaseService {{
  protected logger = logger;

  protected async handleServiceError(error: any, operation: string): Promise<never> {{
    this.logger.error(`Error in ${{operation}}:`, error);
    throw new Error(`${{operation}} failed: ${{error.message}}`);
  }}

  protected validateRequired(data: any, fields: string[]): void {{
    const missing = fields.filter(field => !data[field]);
    if (missing.length > 0) {{
      throw new Error(`Missing required fields: ${{missing.join(', ')}}`);
    }}
  }}
}}
"""

        base_service_file = services_dir / f"BaseService.{'ts' if is_typescript else 'js'}"
        base_service_file.write_text(base_service_content, encoding='utf-8')

        # Generate authentication service
        auth_service_content = f"""import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import {{ BaseService }} from './BaseService';
import {{ UserService }} from './UserService';
import {{ EmailService }} from './EmailService';

interface LoginResult {{
  user: any;
  token: string;
}}

interface RegisterData {{
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}}

export class AuthService extends BaseService {{
  private userService: UserService;
  private emailService: EmailService;
  private jwtSecret: string;

  constructor() {{
    super();
    this.userService = new UserService();
    this.emailService = new EmailService();
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
  }}

  async login(email: string, password: string): Promise<LoginResult> {{
    try {{
      this.validateRequired({{ email, password }}, ['email', 'password']);

      const user = await this.userService.findByEmail(email);
      if (!user) {{
        throw new Error('Invalid credentials');
      }}

      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {{
        throw new Error('Invalid credentials');
      }}

      const token = this.generateToken(user.id);

      // Update last login
      await this.userService.updateLastLogin(user.id);

      return {{
        user: this.sanitizeUser(user),
        token
      }};
    }} catch (error) {{
      await this.handleServiceError(error, 'login');
    }}
  }}

  async register(data: RegisterData): Promise<LoginResult> {{
    try {{
      this.validateRequired(data, ['email', 'password', 'firstName', 'lastName']);

      // Check if user already exists
      const existingUser = await this.userService.findByEmail(data.email);
      if (existingUser) {{
        throw new Error('User already exists with this email');
      }}

      // Hash password
      const hashedPassword = await bcrypt.hash(data.password, 12);

      // Create user
      const user = await this.userService.create({{
        ...data,
        password: hashedPassword
      }});

      const token = this.generateToken(user.id);

      // Send welcome email
      await this.emailService.sendWelcomeEmail(user.email, user.firstName);

      return {{
        user: this.sanitizeUser(user),
        token
      }};
    }} catch (error) {{
      await this.handleServiceError(error, 'register');
    }}
  }}

  async logout(userId: string): Promise<void> {{
    try {{
      // In a real app, you might want to blacklist the token
      // or update user's last logout time
      await this.userService.updateLastLogout(userId);
    }} catch (error) {{
      await this.handleServiceError(error, 'logout');
    }}
  }}

  async refreshToken(userId: string): Promise<LoginResult> {{
    try {{
      const user = await this.userService.findById(userId);
      if (!user) {{
        throw new Error('User not found');
      }}

      const token = this.generateToken(user.id);

      return {{
        user: this.sanitizeUser(user),
        token
      }};
    }} catch (error) {{
      await this.handleServiceError(error, 'refresh token');
    }}
  }}

  async forgotPassword(email: string): Promise<void> {{
    try {{
      const user = await this.userService.findByEmail(email);
      if (!user) {{
        // Don't reveal if email exists
        return;
      }}

      const resetToken = this.generateResetToken();
      await this.userService.setPasswordResetToken(user.id, resetToken);
      await this.emailService.sendPasswordResetEmail(email, resetToken);
    }} catch (error) {{
      await this.handleServiceError(error, 'forgot password');
    }}
  }}

  async resetPassword(token: string, newPassword: string): Promise<void> {{
    try {{
      const user = await this.userService.findByResetToken(token);
      if (!user) {{
        throw new Error('Invalid or expired reset token');
      }}

      const hashedPassword = await bcrypt.hash(newPassword, 12);
      await this.userService.updatePassword(user.id, hashedPassword);
      await this.userService.clearPasswordResetToken(user.id);
    }} catch (error) {{
      await this.handleServiceError(error, 'reset password');
    }}
  }}

  private generateToken(userId: string): string {{
    return jwt.sign({{ userId }}, this.jwtSecret, {{ expiresIn: '7d' }});
  }}

  private generateResetToken(): string {{
    return jwt.sign({{ type: 'reset' }}, this.jwtSecret, {{ expiresIn: '1h' }});
  }}

  private sanitizeUser(user: any): any {{
    const {{ password, passwordResetToken, ...sanitized }} = user;
    return sanitized;
  }}

  verifyToken(token: string): any {{
    try {{
      return jwt.verify(token, this.jwtSecret);
    }} catch (error) {{
      throw new Error('Invalid token');
    }}
  }}
}}
"""

        auth_service_file = services_dir / f"AuthService.{'ts' if is_typescript else 'js'}"
        auth_service_file.write_text(auth_service_content, encoding='utf-8')

    async def _generate_express_models(self, context: CodeGenerationContext, structure: ProjectStructure, is_typescript: bool):
        """Generate data models"""
        models_dir = context.output_path / "src" / "models"
        models_dir.mkdir(parents=True, exist_ok=True)

        # Generate base model
        base_model_content = f"""import {{ Model, DataTypes, Sequelize }} from 'sequelize';

export interface BaseModelAttributes {{
  id: string;
  createdAt: Date;
  updatedAt: Date;
}}

export abstract class BaseModel extends Model<BaseModelAttributes> {{
  public id!: string;
  public createdAt!: Date;
  public updatedAt!: Date;

  static initModel(sequelize: Sequelize): void {{
    // Override in subclasses
  }}

  static associate(models: any): void {{
    // Override in subclasses for associations
  }}
}}
"""

        base_model_file = models_dir / f"BaseModel.{'ts' if is_typescript else 'js'}"
        base_model_file.write_text(base_model_content, encoding='utf-8')

        # Generate User model
        user_model_content = f"""import {{ DataTypes, Sequelize }} from 'sequelize';
import {{ BaseModel, BaseModelAttributes }} from './BaseModel';

export interface UserAttributes extends BaseModelAttributes {{
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: string;
  isActive: boolean;
  lastLoginAt?: Date;
  lastLogoutAt?: Date;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
}}

export class User extends BaseModel implements UserAttributes {{
  public email!: string;
  public password!: string;
  public firstName!: string;
  public lastName!: string;
  public role!: string;
  public isActive!: boolean;
  public lastLoginAt?: Date;
  public lastLogoutAt?: Date;
  public passwordResetToken?: string;
  public passwordResetExpires?: Date;

  static initModel(sequelize: Sequelize): void {{
    User.init(
      {{
        id: {{
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        }},
        email: {{
          type: DataTypes.STRING,
          allowNull: false,
          unique: true,
          validate: {{
            isEmail: true,
          }},
        }},
        password: {{
          type: DataTypes.STRING,
          allowNull: false,
          validate: {{
            len: [6, 255],
          }},
        }},
        firstName: {{
          type: DataTypes.STRING,
          allowNull: false,
          validate: {{
            len: [1, 50],
          }},
        }},
        lastName: {{
          type: DataTypes.STRING,
          allowNull: false,
          validate: {{
            len: [1, 50],
          }},
        }},
        role: {{
          type: DataTypes.ENUM('user', 'admin', 'moderator'),
          defaultValue: 'user',
        }},
        isActive: {{
          type: DataTypes.BOOLEAN,
          defaultValue: true,
        }},
        lastLoginAt: {{
          type: DataTypes.DATE,
          allowNull: true,
        }},
        lastLogoutAt: {{
          type: DataTypes.DATE,
          allowNull: true,
        }},
        passwordResetToken: {{
          type: DataTypes.STRING,
          allowNull: true,
        }},
        passwordResetExpires: {{
          type: DataTypes.DATE,
          allowNull: true,
        }},
        createdAt: {{
          type: DataTypes.DATE,
          allowNull: false,
        }},
        updatedAt: {{
          type: DataTypes.DATE,
          allowNull: false,
        }},
      }},
      {{
        sequelize,
        modelName: 'User',
        tableName: 'users',
        timestamps: true,
        indexes: [
          {{
            unique: true,
            fields: ['email'],
          }},
          {{
            fields: ['role'],
          }},
          {{
            fields: ['isActive'],
          }},
        ],
        hooks: {{
          beforeCreate: (user: User) => {{
            user.email = user.email.toLowerCase();
          }},
          beforeUpdate: (user: User) => {{
            if (user.changed('email')) {{
              user.email = user.email.toLowerCase();
            }}
          }},
        }},
      }}
    );
  }}

  static associate(models: any): void {{
    // Define associations here
    // Example: User.hasMany(models.Post, {{{{ foreignKey: 'userId' }}}});
  }}

  // Instance methods
  public getFullName(): string {{
    return `${{this.firstName}} ${{this.lastName}}`;
  }}

  public toJSON(): any {{
    const values = Object.assign({{}}, this.get());
    delete values.password;
    delete values.passwordResetToken;
    return values;
  }}
}}
"""

        user_model_file = models_dir / f"User.{'ts' if is_typescript else 'js'}"
        user_model_file.write_text(user_model_content, encoding='utf-8')

        # Generate models index
        models_index_content = f"""import {{ Sequelize }} from 'sequelize';
import {{ BaseModel }} from './BaseModel';
import {{ User }} from './User';

const models = {{
  BaseModel,
  User,
}};

export function initializeModels(sequelize: Sequelize): void {{
  // Initialize all models
  Object.values(models).forEach((model: any) => {{
    if (model.initModel) {{
      model.initModel(sequelize);
    }}
  }});

  // Set up associations
  Object.values(models).forEach((model: any) => {{
    if (model.associate) {{
      model.associate(models);
    }}
  }});
}}

export {{ BaseModel, User }};
export default models;
"""

        models_index_file = models_dir / f"index.{'ts' if is_typescript else 'js'}"
        models_index_file.write_text(models_index_content, encoding='utf-8')

    async def _generate_express_middleware(self, context: CodeGenerationContext, structure: ProjectStructure, is_typescript: bool):
        """Generate Express middleware"""
        middleware_dir = context.output_path / "src" / "middleware"
        middleware_dir.mkdir(parents=True, exist_ok=True)

        # Generate authentication middleware
        auth_middleware_content = f"""import {{ Request, Response, NextFunction }} from 'express';
import {{ AuthService }} from '../services/AuthService';

interface AuthenticatedRequest extends Request {{
  user?: any;
}}

const authService = new AuthService();

export const authMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {{
  try {{
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {{
      return res.status(401).json({{
        success: false,
        message: 'Access token required'
      }});
    }}

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    try {{
      const decoded = authService.verifyToken(token);
      req.user = decoded;
      next();
    }} catch (error) {{
      return res.status(401).json({{
        success: false,
        message: 'Invalid or expired token'
      }});
    }}
  }} catch (error) {{
    return res.status(500).json({{
      success: false,
      message: 'Authentication error'
    }});
  }}
}};

export const optionalAuthMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {{
  try {{
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {{
      const token = authHeader.substring(7);
      try {{
        const decoded = authService.verifyToken(token);
        req.user = decoded;
      }} catch (error) {{
        // Token is invalid, but we continue without user
      }}
    }}

    next();
  }} catch (error) {{
    next();
  }}
}};
"""

        auth_middleware_file = middleware_dir / f"auth.{'ts' if is_typescript else 'js'}"
        auth_middleware_file.write_text(auth_middleware_content, encoding='utf-8')

        # Generate validation middleware
        validation_middleware_content = f"""import {{ Request, Response, NextFunction }} from 'express';
import {{ validationResult }} from 'express-validator';

export const validateRequest = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {{
  const errors = validationResult(req);

  if (!errors.isEmpty()) {{
    return res.status(400).json({{
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    }});
  }}

  next();
}};

export const sanitizeInput = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {{
  // Basic input sanitization
  const sanitizeObject = (obj: any): any => {{
    if (typeof obj === 'string') {{
      return obj.trim();
    }}
    if (Array.isArray(obj)) {{
      return obj.map(sanitizeObject);
    }}
    if (obj && typeof obj === 'object') {{
      const sanitized: any = {{}};
      for (const [key, value] of Object.entries(obj)) {{
        sanitized[key] = sanitizeObject(value);
      }}
      return sanitized;
    }}
    return obj;
  }};

  if (req.body) {{
    req.body = sanitizeObject(req.body);
  }}
  if (req.query) {{
    req.query = sanitizeObject(req.query);
  }}
  if (req.params) {{
    req.params = sanitizeObject(req.params);
  }}

  next();
}};
"""

        validation_middleware_file = middleware_dir / f"validation.{'ts' if is_typescript else 'js'}"
        validation_middleware_file.write_text(validation_middleware_content, encoding='utf-8')

        # Generate error handling middleware
        error_middleware_content = f"""import {{ Request, Response, NextFunction }} from 'express';
import {{ logger }} from '../utils/logger';

interface ErrorWithStatus extends Error {{
  status?: number;
  statusCode?: number;
}}

export const errorHandler = (
  error: ErrorWithStatus,
  req: Request,
  res: Response,
  next: NextFunction
): void => {{
  logger.error('Error occurred:', {{
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  }});

  const status = error.status || error.statusCode || 500;
  const message = error.message || 'Internal Server Error';

  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';

  res.status(status).json({{
    success: false,
    message: isDevelopment ? message : 'Something went wrong',
    ...(isDevelopment && {{ stack: error.stack }})
  }});
}};

export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {{
  res.status(404).json({{
    success: false,
    message: 'Route not found',
    path: req.originalUrl
  }});
}};

export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) => {{
  return (req: Request, res: Response, next: NextFunction) => {{
    Promise.resolve(fn(req, res, next)).catch(next);
  }};
}};
"""

        error_middleware_file = middleware_dir / f"error.{'ts' if is_typescript else 'js'}"
        error_middleware_file.write_text(error_middleware_content, encoding='utf-8')

        # Generate middleware index
        middleware_index_content = f"""export {{ authMiddleware, optionalAuthMiddleware }} from './auth';
export {{ validateRequest, sanitizeInput }} from './validation';
export {{ errorHandler, notFoundHandler, asyncHandler }} from './error';
"""

        middleware_index_file = middleware_dir / f"index.{'ts' if is_typescript else 'js'}"
        middleware_index_file.write_text(middleware_index_content, encoding='utf-8')

    async def _generate_tsconfig(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate TypeScript configuration"""
        tsconfig_file = next((f for f in structure.config_files if f.path == "tsconfig.json"), None)
        if not tsconfig_file:
            return

        # Determine configuration based on project type
        if structure.type in [ProjectType.FRONTEND, ProjectType.FULLSTACK]:
            # React TypeScript configuration
            tsconfig_content = {
                "compilerOptions": {
                    "target": "ES5",
                    "lib": ["DOM", "DOM.Iterable", "ES6"],
                    "allowJs": True,
                    "skipLibCheck": True,
                    "esModuleInterop": True,
                    "allowSyntheticDefaultImports": True,
                    "strict": True,
                    "forceConsistentCasingInFileNames": True,
                    "noFallthroughCasesInSwitch": True,
                    "module": "esnext",
                    "moduleResolution": "bundler",
                    "resolveJsonModule": True,
                    "isolatedModules": True,
                    "noEmit": True,
                    "jsx": "react-jsx"
                },
                "include": ["src"],
                "exclude": ["node_modules"]
            }
        else:
            # Node.js TypeScript configuration
            tsconfig_content = {
                "compilerOptions": {
                    "target": "ES2020",
                    "module": "commonjs",
                    "lib": ["ES2020"],
                    "outDir": "./dist",
                    "rootDir": "./src",
                    "strict": True,
                    "esModuleInterop": True,
                    "skipLibCheck": True,
                    "forceConsistentCasingInFileNames": True,
                    "resolveJsonModule": True,
                    "declaration": True,
                    "declarationMap": True,
                    "sourceMap": True,
                    "removeComments": True,
                    "noImplicitAny": True,
                    "noImplicitReturns": True,
                    "noImplicitThis": True,
                    "noUnusedLocals": True,
                    "noUnusedParameters": True
                },
                "include": ["src/**/*"],
                "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]
            }

        tsconfig_file.content = json.dumps(tsconfig_content, indent=2)

    async def _generate_jest_config(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate Jest configuration"""
        jest_config_file = next((f for f in structure.config_files if f.path == "jest.config.js"), None)
        if not jest_config_file:
            return

        if structure.type in [ProjectType.FRONTEND, ProjectType.FULLSTACK]:
            # React Jest configuration
            jest_content = """module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '\\\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': 'jest-transform-stub'
  },
  transform: {
    '^.+\\\\.(ts|tsx)$': 'ts-jest',
    '^.+\\\\.(js|jsx)$': 'babel-jest'
  },
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.(ts|tsx|js)',
    '<rootDir>/src/**/?(*.)(spec|test).(ts|tsx|js)'
  ],
  collectCoverageFrom: [
    'src/**/*.(ts|tsx)',
    '!src/**/*.d.ts',
    '!src/index.tsx',
    '!src/serviceWorker.ts'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};"""
        else:
            # Node.js Jest configuration
            jest_content = """module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  transform: {
    '^.+\\\\.ts$': 'ts-jest'
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/types/**/*'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  testTimeout: 10000
};"""

        jest_config_file.content = jest_content

    async def _generate_docker_files(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate Docker configuration"""
        dockerfile = next((f for f in structure.config_files if f.path == "Dockerfile"), None)
        docker_compose = next((f for f in structure.config_files if f.path == "docker-compose.yml"), None)

        if dockerfile:
            if structure.type in [ProjectType.FRONTEND, ProjectType.FULLSTACK]:
                # Multi-stage Dockerfile for React app
                dockerfile_content = f"""# Build stage
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built app to nginx
COPY --from=builder /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]"""
            else:
                # Node.js Dockerfile
                dockerfile_content = f"""FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build TypeScript
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership
RUN chown -R nodejs:nodejs /app
USER nodejs

EXPOSE 3000

CMD ["npm", "start"]"""

            dockerfile.content = dockerfile_content

        if docker_compose:
            # Docker Compose configuration
            compose_content = f"""version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: {context.architecture.project_name.lower().replace(' ', '_')}
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:"""

            docker_compose.content = compose_content

    async def _generate_env_files(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate environment files"""
        env_example_file = next((f for f in structure.config_files if f.path == ".env.example"), None)
        if not env_example_file:
            return

        env_content = f"""# {context.architecture.project_name} Environment Configuration

# Application
NODE_ENV=development
PORT=3000
APP_NAME={context.architecture.project_name}

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/{context.architecture.project_name.lower().replace(' ', '_')}

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Redis (for caching and sessions)
REDIS_URL=redis://localhost:6379

# Email (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# External APIs
OPENAI_API_KEY=your-openai-api-key

# Security
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Monitoring
SENTRY_DSN=your-sentry-dsn

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads/

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_CACHING=true
ENABLE_RATE_LIMITING=true
"""

        env_example_file.content = env_content

    async def _generate_gitignore(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate .gitignore file"""
        gitignore_file = next((f for f in structure.config_files if f.path == ".gitignore"), None)
        if not gitignore_file:
            return

        gitignore_content = """# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
/build
/dist
*.tgz

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Database
*.sqlite
*.db

# Docker
docker-compose.override.yml

# Testing
test-results/
playwright-report/
"""

        gitignore_file.content = gitignore_content

    async def _generate_documentation(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate comprehensive documentation"""

        # Generate README.md
        await self._generate_readme(context, structure)

        # Generate API documentation
        if structure.type in [ProjectType.FULLSTACK, ProjectType.BACKEND, ProjectType.API]:
            await self._generate_api_docs(context, structure)

        # Generate development guide
        await self._generate_dev_guide(context, structure)

        # Generate deployment guide
        await self._generate_deployment_guide(context, structure)

        # Generate code comments and inline documentation
        await self._add_code_documentation(context, structure)

    async def _generate_integration_tests(self, context: CodeGenerationContext, structure: ProjectStructure) -> List[CodeFile]:
        """Generate comprehensive integration tests"""
        integration_tests = []

        # Generate API integration tests for backend projects
        if structure.type in [ProjectType.FULLSTACK, ProjectType.BACKEND, ProjectType.API, ProjectType.MICROSERVICE]:
            api_test = await self._generate_api_integration_test(context, structure)
            if api_test:
                integration_tests.append(api_test)

            # Generate database integration tests
            db_test = await self._generate_database_integration_test(context, structure)
            if db_test:
                integration_tests.append(db_test)

        # Generate component integration tests for frontend projects
        if structure.type in [ProjectType.FULLSTACK, ProjectType.FRONTEND]:
            component_integration_test = await self._generate_component_integration_test(context, structure)
            if component_integration_test:
                integration_tests.append(component_integration_test)

        return integration_tests

    async def _generate_api_integration_test(self, context: CodeGenerationContext, structure: ProjectStructure) -> Optional[CodeFile]:
        """Generate API integration tests"""
        test_content = f"""import request from 'supertest';
import {{ app }} from '../src/app';
import {{ connectDatabase, disconnectDatabase }} from '../src/config/database';

describe('API Integration Tests', () => {{
  beforeAll(async () => {{
    await connectDatabase();
  }});

  afterAll(async () => {{
    await disconnectDatabase();
  }});

  describe('Health Check', () => {{
    it('should return 200 for health endpoint', async () => {{
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'OK');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('uptime');
    }});
  }});

  describe('Authentication', () => {{
    it('should handle user registration', async () => {{
      const userData = {{
        email: '<EMAIL>',
        password: 'securePassword123',
        name: 'Test User'
      }};

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('token');
      expect(response.body.user.email).toBe(userData.email);
    }});

    it('should handle user login', async () => {{
      const loginData = {{
        email: '<EMAIL>',
        password: 'securePassword123'
      }};

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
    }});

    it('should reject invalid credentials', async () => {{
      const invalidData = {{
        email: '<EMAIL>',
        password: 'wrongPassword'
      }};

      await request(app)
        .post('/api/auth/login')
        .send(invalidData)
        .expect(401);
    }});
  }});

  describe('Protected Routes', () => {{
    let authToken: string;

    beforeEach(async () => {{
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({{
          email: '<EMAIL>',
          password: 'securePassword123'
        }});

      authToken = loginResponse.body.token;
    }});

    it('should require authentication for protected routes', async () => {{
      await request(app)
        .get('/api/protected-resource')
        .expect(401);
    }});

    it('should allow access with valid token', async () => {{
      await request(app)
        .get('/api/protected-resource')
        .set('Authorization', `Bearer ${{authToken}}`)
        .expect(200);
    }});
  }});

  describe('Error Handling', () => {{
    it('should handle 404 for non-existent routes', async () => {{
      await request(app)
        .get('/api/non-existent')
        .expect(404);
    }});

    it('should handle malformed JSON', async () => {{
      await request(app)
        .post('/api/auth/login')
        .set('Content-Type', 'application/json')
        .send('{{invalid json}}')
        .expect(400);
    }});
  }});
}});"""

        return CodeFile(
            path="tests/integration/api.integration.test.ts",
            content=test_content,
            language="typescript",
            file_type="test",
            tests_required=False,
            documentation_required=False
        )

    async def _generate_database_integration_test(self, context: CodeGenerationContext, structure: ProjectStructure) -> Optional[CodeFile]:
        """Generate database integration tests"""
        test_content = f"""import {{ connectDatabase, disconnectDatabase }} from '../src/config/database';

describe('Database Integration Tests', () => {{
  beforeAll(async () => {{
    await connectDatabase();
  }});

  afterAll(async () => {{
    await disconnectDatabase();
  }});

  describe('Connection', () => {{
    it('should connect to database successfully', async () => {{
      // Test database connection
      const connection = await db.connect();
      expect(connection).toBeDefined();
      expect(connection.readyState).toBe(1); // Connected state
      await db.disconnect();
    }});

    it('should handle connection errors gracefully', async () => {{
      // Test connection error handling
      const invalidConnectionString = 'mongodb://invalid:27017/test';
      await expect(db.connect(invalidConnectionString)).rejects.toThrow();
    }});
  }});

  describe('CRUD Operations', () => {{
    it('should create records', async () => {{
      // Test record creation
      const testData = {{ name: 'Test Record', value: 123 }};
      const result = await db.create('testCollection', testData);
      expect(result).toBeDefined();
      expect(result._id).toBeDefined();
      expect(result.name).toBe(testData.name);
    }});

    it('should read records', async () => {{
      // Test record reading
      const testData = {{ name: 'Read Test', value: 456 }};
      const created = await db.create('testCollection', testData);
      const found = await db.findById('testCollection', created._id);
      expect(found).toBeDefined();
      expect(found.name).toBe(testData.name);
    }});

    it('should update records', async () => {{
      // Test record updating
      const testData = {{ name: 'Update Test', value: 789 }};
      const created = await db.create('testCollection', testData);
      const updateData = {{ name: 'Updated Name' }};
      const updated = await db.update('testCollection', created._id, updateData);
      expect(updated.name).toBe(updateData.name);
    }});

    it('should delete records', async () => {{
      // Test record deletion
      const testData = {{ name: 'Delete Test', value: 999 }};
      const created = await db.create('testCollection', testData);
      const deleted = await db.delete('testCollection', created._id);
      expect(deleted).toBe(true);
      const found = await db.findById('testCollection', created._id);
      expect(found).toBeNull();
    }});
  }});
}});"""

        return CodeFile(
            path="tests/integration/database.integration.test.ts",
            content=test_content,
            language="typescript",
            file_type="test",
            tests_required=False,
            documentation_required=False
        )

    async def _generate_component_integration_test(self, context: CodeGenerationContext, structure: ProjectStructure) -> Optional[CodeFile]:
        """Generate component integration tests"""
        test_content = f"""import {{ render, screen, fireEvent, waitFor }} from '@testing-library/react';
import {{ BrowserRouter }} from 'react-router-dom';
import {{ App }} from '../src/App';

const renderWithRouter = (component: React.ReactElement) => {{
  return render(
    <BrowserRouter>
      {{component}}
    </BrowserRouter>
  );
}};

describe('Component Integration Tests', () => {{
  describe('App Integration', () => {{
    it('should render main application', () => {{
      renderWithRouter(<App />);
      expect(screen.getByRole('main')).toBeInTheDocument();
    }});

    it('should handle navigation', async () => {{
      renderWithRouter(<App />);

      // Test navigation functionality
      const navLink = screen.getByText(/dashboard/i);
      fireEvent.click(navLink);

      await waitFor(() => {{
        expect(window.location.pathname).toBe('/dashboard');
      }});
    }});

    it('should handle user interactions', async () => {{
      renderWithRouter(<App />);

      // Test user interaction flows
      const button = screen.getByRole('button', {{ name: /submit/i }});
      fireEvent.click(button);
      await waitFor(() => {{
        expect(screen.getByText(/success/i)).toBeInTheDocument();
      }});
    }});
  }});

  describe('Form Integration', () => {{
    it('should handle form submissions', async () => {{
      renderWithRouter(<App />);

      // Test form submission flows
      const form = screen.getByRole('form');
      const input = screen.getByLabelText(/name/i);
      fireEvent.change(input, {{ target: {{ value: 'Test Name' }} }});
      fireEvent.submit(form);
      await waitFor(() => {{
        expect(screen.getByText(/submitted/i)).toBeInTheDocument();
      }});
    }});

    it('should validate form inputs', async () => {{
      renderWithRouter(<App />);

      // Test form validation
      const submitButton = screen.getByRole('button', {{ name: /submit/i }});
      fireEvent.click(submitButton);
      await waitFor(() => {{
        expect(screen.getByText(/required/i)).toBeInTheDocument();
      }});
    }});
  }});
}});"""

        return CodeFile(
            path="tests/integration/components.integration.test.tsx",
            content=test_content,
            language="typescript",
            file_type="test",
            tests_required=False,
            documentation_required=False
        )

    async def _generate_e2e_config(self, context: CodeGenerationContext, structure: ProjectStructure) -> Optional[CodeFile]:
        """Generate E2E test configuration"""
        config_content = f"""import {{ defineConfig, devices }} from '@playwright/test';

export default defineConfig({{
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {{
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  }},

  projects: [
    {{
      name: 'chromium',
      use: {{ ...devices['Desktop Chrome'] }},
    }},
    {{
      name: 'firefox',
      use: {{ ...devices['Desktop Firefox'] }},
    }},
    {{
      name: 'webkit',
      use: {{ ...devices['Desktop Safari'] }},
    }},
    {{
      name: 'Mobile Chrome',
      use: {{ ...devices['Pixel 5'] }},
    }},
  ],

  webServer: {{
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  }},
}});"""

        return CodeFile(
            path="playwright.config.ts",
            content=config_content,
            language="typescript",
            file_type="config",
            tests_required=False,
            documentation_required=False
        )

    async def _generate_e2e_tests(self, context: CodeGenerationContext, structure: ProjectStructure) -> List[CodeFile]:
        """Generate comprehensive end-to-end tests"""
        e2e_tests = []

        if structure.type in [ProjectType.FULLSTACK, ProjectType.FRONTEND]:
            # Generate Playwright E2E tests
            e2e_test = await self._generate_playwright_e2e_test(context, structure)
            if e2e_test:
                e2e_tests.append(e2e_test)

            # Generate E2E configuration
            e2e_config = await self._generate_e2e_config(context, structure)
            if e2e_config:
                e2e_tests.append(e2e_config)

        return e2e_tests

    async def _generate_playwright_e2e_test(self, context: CodeGenerationContext, structure: ProjectStructure) -> Optional[CodeFile]:
        """Generate Playwright E2E tests"""
        test_content = f"""import {{ test, expect, Page }} from '@playwright/test';

test.describe('{context.architecture.project_name} E2E Tests', () => {{
  test.beforeEach(async ({{ page }}) => {{
    // Navigate to the application
    await page.goto('/');
  }});

  test('should load the homepage', async ({{ page }}) => {{
    // Check if the page loads correctly
    await expect(page).toHaveTitle(/{context.architecture.project_name}/);

    // Check for main navigation elements
    await expect(page.locator('nav')).toBeVisible();
    await expect(page.locator('main')).toBeVisible();
  }});

  test('should handle user authentication flow', async ({{ page }}) => {{
    // Navigate to login page
    await page.click('text=Login');
    await expect(page).toHaveURL(/.*login/);

    // Fill login form
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');

    // Submit form
    await page.click('[data-testid="login-button"]');

    // Check successful login
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    await expect(page).toHaveURL(/.*dashboard/);
  }});

  test('should handle form validation', async ({{ page }}) => {{
    await page.click('text=Login');

    // Try to submit empty form
    await page.click('[data-testid="login-button"]');

    // Check validation messages
    await expect(page.locator('[data-testid="email-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="password-error"]')).toBeVisible();
  }});

  test('should be responsive on mobile', async ({{ page }}) => {{
    // Set mobile viewport
    await page.setViewportSize({{ width: 375, height: 667 }});

    // Check mobile navigation
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible();

    // Test mobile menu functionality
    await page.click('[data-testid="mobile-menu-button"]');
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
  }});

  test('should handle error states gracefully', async ({{ page }}) => {{
    // Mock network error
    await page.route('**/api/**', route => route.abort());

    // Try to perform an action that requires API call
    await page.click('[data-testid="load-data-button"]');

    // Check error message is displayed
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('text=Something went wrong')).toBeVisible();
  }});

  test('should maintain accessibility standards', async ({{ page }}) => {{
    // Check for proper heading hierarchy
    const h1Count = await page.locator('h1').count();
    expect(h1Count).toBe(1);

    // Check for alt text on images
    const images = page.locator('img');
    const imageCount = await images.count();

    for (let i = 0; i < imageCount; i++) {{
      const alt = await images.nth(i).getAttribute('alt');
      expect(alt).toBeTruthy();
    }}

    // Check for proper form labels
    const inputs = page.locator('input');
    const inputCount = await inputs.count();

    for (let i = 0; i < inputCount; i++) {{
      const input = inputs.nth(i);
      const id = await input.getAttribute('id');
      if (id) {{
        await expect(page.locator(`label[for="${{id}}"]`)).toBeVisible();
      }}
    }}
  }});
}});"""

        return CodeFile(
            path="tests/e2e/app.e2e.test.ts",
            content=test_content,
            language="typescript",
            file_type="test",
            tests_required=False,
            documentation_required=False
        )

    async def _generate_controller_test(self, context: CodeGenerationContext, source_file: CodeFile) -> str:
        """Generate comprehensive controller test"""
        controller_name = Path(source_file.path).stem.replace('Controller', '')

        return f"""import {{ Request, Response, NextFunction }} from 'express';
import {{ {controller_name}Controller }} from '../{source_file.path.replace('src/', '').replace('.ts', '').replace('.js', '')}';
import {{ {controller_name}Service }} from '../services/{controller_name}Service';

// Mock the service
jest.mock('../services/{controller_name}Service');

describe('{controller_name}Controller', () => {{
  let controller: {controller_name}Controller;
  let mockService: jest.Mocked<{controller_name}Service>;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {{
    controller = new {controller_name}Controller();
    mockService = new {controller_name}Service() as jest.Mocked<{controller_name}Service>;

    mockRequest = {{
      body: {{}},
      params: {{}},
      query: {{}},
      user: {{ id: 'user123' }}
    }};

    mockResponse = {{
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis()
    }};

    mockNext = jest.fn();

    // Reset mocks
    jest.clearAllMocks();
  }});

  describe('create{controller_name}', () => {{
    it('should create a new {controller_name.lower()} successfully', async () => {{
      const mockData = {{ name: 'Test {controller_name}', description: 'Test description' }};
      const mockResult = {{ id: '123', ...mockData }};

      mockRequest.body = mockData;
      mockService.create.mockResolvedValue(mockResult);

      await controller.create{controller_name}(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockService.create).toHaveBeenCalledWith(mockData);
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({{
        success: true,
        data: mockResult
      }});
    }});

    it('should handle validation errors', async () => {{
      mockRequest.body = {{ /* invalid data */ }};
      const validationError = new Error('Validation failed');
      validationError.name = 'ValidationError';

      mockService.create.mockRejectedValue(validationError);

      await controller.create{controller_name}(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(validationError);
    }});
  }});

  describe('get{controller_name}ById', () => {{
    it('should return {controller_name.lower()} by id', async () => {{
      const mockId = '123';
      const mockResult = {{ id: mockId, name: 'Test {controller_name}' }};

      mockRequest.params = {{ id: mockId }};
      mockService.findById.mockResolvedValue(mockResult);

      await controller.get{controller_name}ById(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockService.findById).toHaveBeenCalledWith(mockId);
      expect(mockResponse.json).toHaveBeenCalledWith({{
        success: true,
        data: mockResult
      }});
    }});

    it('should handle not found error', async () => {{
      const mockId = 'nonexistent';
      mockRequest.params = {{ id: mockId }};

      mockService.findById.mockResolvedValue(null);

      await controller.get{controller_name}ById(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({{
        success: false,
        message: '{controller_name} not found'
      }});
    }});
  }});

  describe('update{controller_name}', () => {{
    it('should update {controller_name.lower()} successfully', async () => {{
      const mockId = '123';
      const updateData = {{ name: 'Updated {controller_name}' }};
      const mockResult = {{ id: mockId, ...updateData }};

      mockRequest.params = {{ id: mockId }};
      mockRequest.body = updateData;
      mockService.update.mockResolvedValue(mockResult);

      await controller.update{controller_name}(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockService.update).toHaveBeenCalledWith(mockId, updateData);
      expect(mockResponse.json).toHaveBeenCalledWith({{
        success: true,
        data: mockResult
      }});
    }});
  }});

  describe('delete{controller_name}', () => {{
    it('should delete {controller_name.lower()} successfully', async () => {{
      const mockId = '123';
      mockRequest.params = {{ id: mockId }};
      mockService.delete.mockResolvedValue(true);

      await controller.delete{controller_name}(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockService.delete).toHaveBeenCalledWith(mockId);
      expect(mockResponse.status).toHaveBeenCalledWith(204);
      expect(mockResponse.send).toHaveBeenCalled();
    }});
  }});
}});"""

    async def _generate_generic_test(self, context: CodeGenerationContext, source_file: CodeFile) -> str:
        """Generate generic test for utility functions and modules"""
        module_name = Path(source_file.path).stem

        return f"""import {{ {module_name} }} from '../{source_file.path.replace('src/', '').replace('.ts', '').replace('.js', '')}';

describe('{module_name}', () => {{
  describe('initialization', () => {{
    it('should be defined', () => {{
      expect({module_name}).toBeDefined();
    }});
  }});

  describe('core functionality', () => {{
    it('should perform expected operations', () => {{
      // Add specific tests based on module functionality
      const result = {module_name}.performOperation('test input');
      expect(result).toBeDefined();
      expect(typeof result).toBe('object');
    }});

    it('should handle edge cases', () => {{
      // Test edge cases and boundary conditions
      expect(() => {module_name}.performOperation(null)).toThrow();
      expect(() => {module_name}.performOperation(undefined)).toThrow();
      expect({module_name}.performOperation('')).toBeDefined();
    }});
  }});

  describe('error handling', () => {{
    it('should handle invalid inputs gracefully', () => {{
      // Test error scenarios
      expect(() => {{
        // Add error test cases
      }}).not.toThrow();
    }});
  }});
}});"""

    async def _generate_api_docs(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate comprehensive API documentation"""
        api_doc_file = next((f for f in structure.documentation_files if f.path == "docs/API.md"), None)
        if not api_doc_file:
            return

        api_content = f"""# API Documentation

## {context.architecture.project_name} API

### Overview

This document describes the REST API for {context.architecture.project_name}.

**Base URL:** `http://localhost:3000/api`

**Authentication:** Bearer Token (JWT)

### Authentication

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "name": "John Doe"
}}
```

**Response:**
```json
{{
  "success": true,
  "data": {{
    "user": {{
      "id": "user123",
      "email": "<EMAIL>",
      "name": "John Doe"
    }},
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }}
}}
```

### Error Responses

All API endpoints return errors in the following format:

```json
{{
  "success": false,
  "error": {{
    "code": "ERROR_CODE",
    "message": "Human readable error message"
  }}
}}
```

### Rate Limiting

API requests are limited to 100 requests per 15 minutes per IP address.

### Support

For API support, contact <EMAIL>
"""

        api_doc_file.content = api_content

    async def _generate_readme(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate comprehensive README.md"""
        readme_file = next((f for f in structure.documentation_files if f.path == "README.md"), None)
        if not readme_file:
            return

        # Extract technology names for display
        tech_stack = []
        for category, technologies in context.target_technologies.items():
            for tech in technologies:
                tech_stack.append(f"- **{tech.name}** {tech.version} - {tech.justification}")

        readme_content = f"""# {context.architecture.project_name}

Generated by Aetherforge Developer Agent

## 📋 Overview

{context.architecture.project_name} is a {context.architecture.architecture_pattern.value.replace('_', ' ').title()} application built with modern technologies and best practices.

## 🏗️ Architecture

- **Pattern**: {context.architecture.architecture_pattern.value.replace('_', ' ').title()}
- **Scalability Tier**: {context.architecture.scalability_tier.value.title()}
- **Components**: {len(context.architecture.components)} main components

## 🛠️ Technology Stack

{chr(10).join(tech_stack)}

## 🚀 Quick Start

### Prerequisites

- Node.js >= 18.0.0
- npm >= 8.0.0
{f"- Docker (optional)" if self._uses_docker(context) else ""}

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd {context.architecture.project_name.lower().replace(' ', '-')}

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Start development server
npm run dev
```

### Development

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix
```

### Production

```bash
# Build for production
npm run build

# Start production server
npm start
```

## 📚 Documentation

- [API Documentation](docs/API.md)
- [Development Guide](docs/DEVELOPMENT.md)
- [Deployment Guide](docs/DEPLOYMENT.md)

## 🧪 Testing

This project maintains {context.quality_level.value} quality standards with:

- Unit tests for all components and services
- Integration tests for API endpoints
- End-to-end tests for critical user flows
- Minimum {self.quality_gates[context.quality_level]['test_coverage']}% test coverage

## 🔒 Security

Security features implemented:

{chr(10).join(f"- {req}" for req in context.security_requirements)}

## 📊 Performance

Performance targets:

{chr(10).join(f"- {key}: {value}" for key, value in context.performance_requirements.items())}

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

---

*Generated by Aetherforge Developer Agent on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        readme_file.content = readme_content

    async def _generate_dev_guide(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate development guide"""
        dev_guide_file = next((f for f in structure.documentation_files if f.path == "docs/DEVELOPMENT.md"), None)
        if not dev_guide_file:
            return

        dev_content = f"""# Development Guide

## {context.architecture.project_name} Development

### Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Copy environment variables: `cp .env.example .env`
4. Start development server: `npm run dev`

### Project Structure

```
{context.architecture.project_name.lower().replace(' ', '-')}/
├── src/                 # Source code
├── tests/              # Test files
├── docs/               # Documentation
├── package.json        # Dependencies
└── README.md          # Project overview
```

### Development Workflow

1. Create feature branch
2. Make changes
3. Add tests
4. Run tests: `npm test`
5. Submit pull request

### Code Standards

- Use TypeScript for type safety
- Follow ESLint rules
- Maintain test coverage above 80%
- Write meaningful commit messages

### Testing

- Unit tests: `npm test`
- Integration tests: `npm run test:integration`
- E2E tests: `npm run test:e2e`
- Coverage: `npm run test:coverage`

### Debugging

Use VS Code debugger or:
```bash
npm run dev:debug
```

### Contributing

See CONTRIBUTING.md for detailed guidelines.
"""

        dev_guide_file.content = dev_content

    async def _generate_deployment_guide(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Generate deployment guide"""
        deploy_guide_file = next((f for f in structure.documentation_files if f.path == "docs/DEPLOYMENT.md"), None)
        if not deploy_guide_file:
            return

        deploy_content = f"""# Deployment Guide

## {context.architecture.project_name} Deployment

### Prerequisites

- Node.js 18+ runtime
- Database (PostgreSQL/MongoDB)
- Redis (for caching)
- Docker (optional)

### Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://user:pass@localhost:5432/db
JWT_SECRET=your-secret-key
```

### Production Build

```bash
npm run build
npm start
```

### Docker Deployment

```bash
docker build -t {context.architecture.project_name.lower().replace(' ', '-')} .
docker run -p 3000:3000 {context.architecture.project_name.lower().replace(' ', '-')}
```

### Health Checks

Monitor `/health` endpoint for application status.

### Scaling

- Use PM2 for process management
- Configure load balancer
- Set up database replicas
- Implement caching strategy

### Monitoring

- Application logs: `/logs`
- Metrics: Prometheus/Grafana
- Error tracking: Sentry
- Uptime monitoring: Pingdom

### Security

- Enable HTTPS
- Configure CORS
- Set security headers
- Regular security updates

### Backup

- Database backups: Daily
- File backups: Weekly
- Disaster recovery plan

### Troubleshooting

Common issues and solutions:

1. **Port already in use**: Change PORT in .env
2. **Database connection**: Check DATABASE_URL
3. **Memory issues**: Increase container limits
"""

        deploy_guide_file.content = deploy_content

    async def _add_code_documentation(self, context: CodeGenerationContext, structure: ProjectStructure):
        """Add inline documentation and comments to generated code"""
        for source_file in structure.source_files:
            if source_file.content and source_file.language in ["typescript", "javascript"]:
                # Add JSDoc comments to functions
                source_file.content = self._add_jsdoc_comments(source_file.content)

    def _add_jsdoc_comments(self, content: str) -> str:
        """Add JSDoc comments to functions"""
        # Simple implementation - in production, this would use AST parsing
        lines = content.split('\n')
        documented_lines = []

        for i, line in enumerate(lines):
            # Add JSDoc for function declarations
            if 'function ' in line or '=>' in line and 'const ' in line:
                documented_lines.append('/**')
                documented_lines.append(' * Function description')
                documented_lines.append(' * @param {any} param - Parameter description')
                documented_lines.append(' * @returns {any} Return description')
                documented_lines.append(' */')

            documented_lines.append(line)

        return '\n'.join(documented_lines)


# Orchestrator Integration Functions
class DeveloperAgentExecutor:
    """Executor for integrating Developer Agent with Aetherforge Orchestrator"""

    def __init__(self, mcp_url: str = None, api_manager=None):
        self.developer_agent = DeveloperAgent(mcp_url, api_manager)

    async def execute_developer_agent(self, project_id: str, project_path: str,
                                    architecture_data: Dict[str, Any],
                                    quality_level: str = "production") -> Dict[str, Any]:
        """Execute developer agent as part of orchestrator workflow"""
        try:
            logger.info(f"Executing developer agent for project {project_id}")

            # Convert architecture data to SystemArchitecture object
            architecture = self._convert_architecture_data(architecture_data)

            # Generate project code
            output_path = Path(project_path) / "generated_code"
            project_structure = await self.developer_agent.generate_project(
                architecture,
                output_path,
                CodeQuality(quality_level)
            )

            # Create orchestrator response
            result = {
                "success": True,
                "project_id": project_id,
                "agent": "developer",
                "phase": "development",
                "output_path": str(output_path),
                "project_structure": {
                    "name": project_structure.name,
                    "type": project_structure.type.value,
                    "files_generated": {
                        "source": len(project_structure.source_files),
                        "test": len(project_structure.test_files),
                        "config": len(project_structure.config_files),
                        "documentation": len(project_structure.documentation_files)
                    },
                    "dependencies": project_structure.dependencies,
                    "scripts": project_structure.scripts
                },
                "quality_metrics": {
                    "test_coverage_target": self.developer_agent.quality_gates[CodeQuality(quality_level)]["test_coverage"],
                    "documentation_generated": len(project_structure.documentation_files) > 0,
                    "linting_enabled": True,
                    "security_configured": True
                },
                "next_phase": "qa",
                "timestamp": datetime.now().isoformat()
            }

            logger.info(f"Developer agent completed successfully for project {project_id}")
            return result

        except Exception as e:
            logger.error(f"Developer agent execution failed for project {project_id}: {e}")
            return {
                "success": False,
                "project_id": project_id,
                "agent": "developer",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _convert_architecture_data(self, architecture_data: Dict[str, Any]) -> SystemArchitecture:
        """Convert architecture data dict to SystemArchitecture object"""
        from architect_agent import SystemArchitecture, ArchitecturePattern, ScalabilityTier, TechnologyChoice

        # Convert technology stack
        technology_stack = {}
        for category, techs in architecture_data.get('technology_stack', {}).items():
            technology_stack[category] = [
                TechnologyChoice(
                    name=tech.get('name', ''),
                    version=tech.get('version', '1.0.0'),
                    category=tech.get('category', category),
                    justification=tech.get('justification', ''),
                    alternatives=tech.get('alternatives', []),
                    pros=tech.get('pros', []),
                    cons=tech.get('cons', []),
                    learning_curve=tech.get('learning_curve', 'medium'),
                    community_support=tech.get('community_support', 'good')
                ) for tech in techs
            ]

        return SystemArchitecture(
            project_name=architecture_data.get('project_name', 'Generated Project'),
            architecture_pattern=ArchitecturePattern(architecture_data.get('architecture_pattern', 'layered')),
            scalability_tier=ScalabilityTier(architecture_data.get('scalability_tier', 'medium')),
            components=architecture_data.get('components', []),
            data_architecture=architecture_data.get('data_architecture', {}),
            security_architecture=architecture_data.get('security_architecture', {}),
            deployment_architecture=architecture_data.get('deployment_architecture', {}),
            integration_patterns=architecture_data.get('integration_patterns', []),
            technology_stack=technology_stack,
            quality_attributes=architecture_data.get('quality_attributes', {}),
            constraints=architecture_data.get('constraints', []),
            assumptions=architecture_data.get('assumptions', []),
            risks=architecture_data.get('risks', [])
        )


# Pheromone Bus Integration
async def send_developer_pheromone(project_id: str, phase: str, data: Dict[str, Any]):
    """Send pheromone signal for developer agent completion"""
    pheromone_data = {
        "project_id": project_id,
        "agent": "developer",
        "phase": phase,
        "status": "completed",
        "data": data,
        "timestamp": datetime.now().isoformat()
    }

    # In a real implementation, this would send to the pheromone bus
    logger.info(f"Sending developer pheromone for project {project_id}: {pheromone_data}")
    return pheromone_data


# Main execution function
async def main():
    """Main function for standalone execution"""
    if len(sys.argv) < 2:
        print("Usage: python developer_agent.py '<architecture_file>' [output_path] [quality_level]")
        print("Example: python developer_agent.py architecture.json ./generated_project production")
        return

    architecture_file = sys.argv[1]
    output_path = Path(sys.argv[2]) if len(sys.argv) > 2 else Path("./generated_project")
    quality_level = CodeQuality(sys.argv[3]) if len(sys.argv) > 3 else CodeQuality.PRODUCTION

    try:
        # Load architecture from file
        with open(architecture_file, 'r') as f:
            arch_data = json.load(f)

        print(f"🏗️ Starting code generation for: {arch_data.get('project_name', 'Unknown Project')}")
        print(f"📁 Output path: {output_path}")
        print(f"🎯 Quality level: {quality_level.value}")

        # Initialize developer agent
        developer = DeveloperAgent()

        # Convert JSON to SystemArchitecture object (simplified)
        # In real implementation, this would use proper deserialization
        from architect_agent import SystemArchitecture, ArchitecturePattern, ScalabilityTier

        architecture = SystemArchitecture(
            project_name=arch_data.get('project_name', 'Generated Project'),
            architecture_pattern=ArchitecturePattern(arch_data.get('architecture_pattern', 'layered')),
            scalability_tier=ScalabilityTier(arch_data.get('scalability_tier', 'medium')),
            components=[],  # Simplified for demo
            data_architecture=arch_data.get('data_architecture', {}),
            security_architecture=arch_data.get('security_architecture', {}),
            deployment_architecture=arch_data.get('deployment_architecture', {}),
            integration_patterns=arch_data.get('integration_patterns', []),
            technology_stack=arch_data.get('technology_stack', {}),
            quality_attributes=arch_data.get('quality_attributes', {}),
            constraints=arch_data.get('constraints', []),
            assumptions=arch_data.get('assumptions', []),
            risks=arch_data.get('risks', [])
        )

        # Generate project
        project_structure = await developer.generate_project(architecture, output_path, quality_level)

        print(f"✅ Code generation complete!")
        print(f"   Source files: {len(project_structure.source_files)}")
        print(f"   Test files: {len(project_structure.test_files)}")
        print(f"   Config files: {len(project_structure.config_files)}")
        print(f"   Documentation files: {len(project_structure.documentation_files)}")

        print(f"🎉 Project generated successfully! Check {output_path} for all files.")

    except FileNotFoundError:
        print(f"❌ Error: Architecture file '{architecture_file}' not found")
    except json.JSONDecodeError:
        print(f"❌ Error: Invalid JSON in architecture file")
    except Exception as e:
        print(f"❌ Error during code generation: {e}")
        logger.error(f"Code generation failed: {e}", exc_info=True)


if __name__ == "__main__":
    import sys
    asyncio.run(main())
