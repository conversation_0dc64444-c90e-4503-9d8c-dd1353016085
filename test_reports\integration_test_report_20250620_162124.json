{"timestamp": "2025-06-20T16:21:24.188366", "summary": {"total_tests": 0, "passed": 0, "failed": 0, "skipped": 0, "success_rate": 0, "total_duration": 3.38, "coverage_percent": 0}, "test_suites": {"Unit Tests": {"success": false, "total": 0, "passed": 0, "failed": 0, "skipped": 0, "duration": 0, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.12.3, pytest-8.4.1, pluggy-1.6.0 -- C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe\ncachedir: .pytest_cache\nbenchmark: 5.1.0 (defaults: timer=time.perf_counter disable_gc=False min_rounds=5 min_time=0.000005 max_time=1.0 calibration_precision=10 warmup=False warmup_iterations=100000)\nmetadata: {'Python': '3.12.3', 'Platform': 'Windows-10-10.0.19045-SP0', 'Packages': {'pytest': '8.4.1', 'pluggy': '1.6.0'}, 'Plugins': {'anyio': '3.7.1', 'asyncio': '1.0.0', 'benchmark': '5.1.0', 'cov': '6.2.1', 'json-report': '1.5.0', 'metadata': '3.1.1', 'timeout': '2.4.0'}}\nrootdir: E:\\Projects\\TaoForge Main\\TaoForge\\tests\nconfigfile: pytest.ini\nplugins: anyio-3.7.1, asyncio-1.0.0, benchmark-5.1.0, cov-6.2.1, json-report-1.5.0, metadata-3.1.1, timeout-2.4.0\nasyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ntimeout: 300.0s\ntimeout method: thread\ntimeout func_only: False\ncollecting ... collected 0 items\n\n--------------------------------- JSON report ---------------------------------\nreport saved to: test_reports/pytest_unit_tests.json\n============================ no tests ran in 0.00s ============================\n", "stderr": "ERROR: file or directory not found: test_*.py\n\n"}, "Integration Tests": {"success": false, "total": 0, "passed": 0, "failed": 0, "skipped": 0, "duration": 0, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.12.3, pytest-8.4.1, pluggy-1.6.0 -- C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe\ncachedir: .pytest_cache\nbenchmark: 5.1.0 (defaults: timer=time.perf_counter disable_gc=False min_rounds=5 min_time=0.000005 max_time=1.0 calibration_precision=10 warmup=False warmup_iterations=100000)\nmetadata: {'Python': '3.12.3', 'Platform': 'Windows-10-10.0.19045-SP0', 'Packages': {'pytest': '8.4.1', 'pluggy': '1.6.0'}, 'Plugins': {'anyio': '3.7.1', 'asyncio': '1.0.0', 'benchmark': '5.1.0', 'cov': '6.2.1', 'json-report': '1.5.0', 'metadata': '3.1.1', 'timeout': '2.4.0'}}\nrootdir: E:\\Projects\\TaoForge Main\\TaoForge\\tests\nconfigfile: pytest.ini\nplugins: anyio-3.7.1, asyncio-1.0.0, benchmark-5.1.0, cov-6.2.1, json-report-1.5.0, metadata-3.1.1, timeout-2.4.0\nasyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ntimeout: 600.0s\ntimeout method: thread\ntimeout func_only: False\ncollecting ... collected 0 items\n\n--------------------------------- JSON report ---------------------------------\nreport saved to: test_reports/pytest_integration_tests.json\n============================ no tests ran in 0.00s ============================\n", "stderr": "ERROR: file or directory not found: test_*integration*.py\n\n"}, "VS Code Extension Tests": {"success": false, "total": 0, "passed": 0, "failed": 0, "skipped": 0, "duration": 0, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.12.3, pytest-8.4.1, pluggy-1.6.0 -- C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe\ncachedir: .pytest_cache\nbenchmark: 5.1.0 (defaults: timer=time.perf_counter disable_gc=False min_rounds=5 min_time=0.000005 max_time=1.0 calibration_precision=10 warmup=False warmup_iterations=100000)\nmetadata: {'Python': '3.12.3', 'Platform': 'Windows-10-10.0.19045-SP0', 'Packages': {'pytest': '8.4.1', 'pluggy': '1.6.0'}, 'Plugins': {'anyio': '3.7.1', 'asyncio': '1.0.0', 'benchmark': '5.1.0', 'cov': '6.2.1', 'json-report': '1.5.0', 'metadata': '3.1.1', 'timeout': '2.4.0'}}\nrootdir: E:\\Projects\\TaoForge Main\\TaoForge\\tests\nconfigfile: pytest.ini\nplugins: anyio-3.7.1, asyncio-1.0.0, benchmark-5.1.0, cov-6.2.1, json-report-1.5.0, metadata-3.1.1, timeout-2.4.0\nasyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ntimeout: 300.0s\ntimeout method: thread\ntimeout func_only: False\ncollecting ... collected 0 items\n\n--------------------------------- JSON report ---------------------------------\nreport saved to: test_reports/pytest_vs_code_extension_tests.json\n============================ no tests ran in 0.00s ============================\n", "stderr": "ERROR: file or directory not found: test_vscode*.py\n\n"}, "Performance Tests": {"success": false, "total": 0, "passed": 0, "failed": 0, "skipped": 0, "duration": 0, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.12.3, pytest-8.4.1, pluggy-1.6.0 -- C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe\ncachedir: .pytest_cache\nbenchmark: 5.1.0 (defaults: timer=time.perf_counter disable_gc=False min_rounds=5 min_time=0.000005 max_time=1.0 calibration_precision=10 warmup=False warmup_iterations=100000)\nmetadata: {'Python': '3.12.3', 'Platform': 'Windows-10-10.0.19045-SP0', 'Packages': {'pytest': '8.4.1', 'pluggy': '1.6.0'}, 'Plugins': {'anyio': '3.7.1', 'asyncio': '1.0.0', 'benchmark': '5.1.0', 'cov': '6.2.1', 'json-report': '1.5.0', 'metadata': '3.1.1', 'timeout': '2.4.0'}}\nrootdir: E:\\Projects\\TaoForge Main\\TaoForge\\tests\nconfigfile: pytest.ini\nplugins: anyio-3.7.1, asyncio-1.0.0, benchmark-5.1.0, cov-6.2.1, json-report-1.5.0, metadata-3.1.1, timeout-2.4.0\nasyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ntimeout: 900.0s\ntimeout method: thread\ntimeout func_only: False\ncollecting ... collected 0 items\n\n--------------------------------- JSON report ---------------------------------\nreport saved to: test_reports/pytest_performance_tests.json\n============================ no tests ran in 0.00s ============================\n", "stderr": "ERROR: file or directory not found: test_*performance*.py\n\n"}, "End-to-End Tests": {"success": false, "total": 0, "passed": 0, "failed": 0, "skipped": 0, "duration": 0, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.12.3, pytest-8.4.1, pluggy-1.6.0 -- C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe\ncachedir: .pytest_cache\nbenchmark: 5.1.0 (defaults: timer=time.perf_counter disable_gc=False min_rounds=5 min_time=0.000005 max_time=1.0 calibration_precision=10 warmup=False warmup_iterations=100000)\nmetadata: {'Python': '3.12.3', 'Platform': 'Windows-10-10.0.19045-SP0', 'Packages': {'pytest': '8.4.1', 'pluggy': '1.6.0'}, 'Plugins': {'anyio': '3.7.1', 'asyncio': '1.0.0', 'benchmark': '5.1.0', 'cov': '6.2.1', 'json-report': '1.5.0', 'metadata': '3.1.1', 'timeout': '2.4.0'}}\nrootdir: E:\\Projects\\TaoForge Main\\TaoForge\\tests\nconfigfile: pytest.ini\nplugins: anyio-3.7.1, asyncio-1.0.0, benchmark-5.1.0, cov-6.2.1, json-report-1.5.0, metadata-3.1.1, timeout-2.4.0\nasyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ntimeout: 1200.0s\ntimeout method: thread\ntimeout func_only: False\ncollecting ... collected 0 items\n\n--------------------------------- JSON report ---------------------------------\nreport saved to: test_reports/pytest_end-to-end_tests.json\n============================ no tests ran in 0.00s ============================\n", "stderr": "ERROR: file or directory not found: test_end_to_end*.py\n\n"}}, "coverage": {"error": "Coverage data not generated"}, "performance": {"error": "Benchmark data not generated"}, "recommendations": ["🔴 Test success rate is below 90%. Review failed tests and fix issues.", "🔴 Code coverage is below 80%. Add more comprehensive tests.", "🔴 Unit Tests failed. Review errors and fix issues.", "🔴 Integration Tests failed. Review errors and fix issues.", "🔴 VS Code Extension Tests failed. Review errors and fix issues.", "🔴 Performance Tests failed. Review errors and fix issues.", "🔴 End-to-End Tests failed. Review errors and fix issues."]}