#!/usr/bin/env python3
"""
Test runner for API key management system
Runs comprehensive tests and generates detailed reports
"""

import sys
import os
import subprocess
import time
from pathlib import Path
import json

def main():
    """Run all API key management tests"""
    print("🧪 Aetherforge API Key Management Test Suite")
    print("=" * 50)
    
    # Ensure test dependencies are available
    try:
        import pytest
    except ImportError:
        print("❌ pytest is required but not installed")
        print("Install with: pip install pytest pytest-asyncio")
        return False
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    # Test configuration
    test_files = [
        "tests/test_api_key_management.py"
    ]
    
    # Check if test files exist
    missing_files = []
    for test_file in test_files:
        if not Path(test_file).exists():
            missing_files.append(test_file)
    
    if missing_files:
        print(f"❌ Missing test files: {', '.join(missing_files)}")
        return False
    
    print(f"📁 Found {len(test_files)} test files")
    
    # Run tests
    start_time = time.time()
    
    # Basic unit tests
    print("\n🔬 Running unit tests...")
    unit_result = run_pytest_tests([
        "tests/test_api_key_management.py::TestSecureKeyStorage",
        "tests/test_api_key_management.py::TestAPIKeyValidator",
        "tests/test_api_key_management.py::TestAPIManager",
        "tests/test_api_key_management.py::TestAPIKeyCLI"
    ])
    
    # Integration tests
    print("\n🔗 Running integration tests...")
    integration_result = run_pytest_tests([
        "tests/test_api_key_management.py::TestIntegration"
    ])
    
    # CLI tests
    print("\n💻 Running CLI tests...")
    cli_result = test_cli_functionality()
    
    # VS Code extension tests
    print("\n🎨 Running VS Code extension tests...")
    vscode_result = test_vscode_integration()
    
    # Generate report
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    results = {
        "Unit Tests": unit_result,
        "Integration Tests": integration_result,
        "CLI Tests": cli_result,
        "VS Code Tests": vscode_result
    }
    
    total_passed = sum(1 for result in results.values() if result)
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:20} | {status}")
    
    print(f"\nOverall: {total_passed}/{total_tests} test suites passed")
    print(f"Duration: {duration:.2f} seconds")
    
    # Save detailed report
    save_test_report(results, duration)
    
    if total_passed == total_tests:
        print("\n🎉 All tests passed! API key management system is working correctly.")
        return True
    else:
        print(f"\n⚠️ {total_tests - total_passed} test suite(s) failed. Check logs for details.")
        return False

def run_pytest_tests(test_patterns):
    """Run pytest with specific test patterns"""
    try:
        cmd = [
            sys.executable, '-m', 'pytest',
            '-v',
            '--tb=short',
            '--asyncio-mode=auto'
        ] + test_patterns
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        # Log output
        with open('logs/pytest_output.log', 'a') as f:
            f.write(f"\n{'='*50}\n")
            f.write(f"Command: {' '.join(cmd)}\n")
            f.write(f"Return code: {result.returncode}\n")
            f.write(f"STDOUT:\n{result.stdout}\n")
            f.write(f"STDERR:\n{result.stderr}\n")
        
        if result.returncode == 0:
            print(f"  ✅ Tests passed")
            return True
        else:
            print(f"  ❌ Tests failed (exit code: {result.returncode})")
            if result.stderr:
                print(f"  Error: {result.stderr[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print("  ❌ Tests timed out")
        return False
    except Exception as e:
        print(f"  ❌ Error running tests: {e}")
        return False

def test_cli_functionality():
    """Test CLI functionality"""
    try:
        # Test CLI import
        result = subprocess.run([
            sys.executable, '-c',
            'import sys; sys.path.insert(0, "src"); from api_key_cli import APIKeyCLI; print("CLI import successful")'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            print(f"  ❌ CLI import failed: {result.stderr}")
            return False
        
        # Test main.py import
        result = subprocess.run([
            sys.executable, '-c',
            'import main; print("Main module import successful")'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            print(f"  ❌ Main module import failed: {result.stderr}")
            return False
        
        # Test help command
        result = subprocess.run([
            sys.executable, 'main.py', '--help'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            print(f"  ❌ Help command failed: {result.stderr}")
            return False
        
        if "Aetherforge" not in result.stdout:
            print("  ❌ Help output doesn't contain expected content")
            return False
        
        print("  ✅ CLI functionality working")
        return True
        
    except Exception as e:
        print(f"  ❌ CLI test error: {e}")
        return False

def test_vscode_integration():
    """Test VS Code extension integration"""
    try:
        # Check if VS Code extension files exist
        extension_files = [
            "vscode-extension/package.json",
            "vscode-extension/src/extension.js"
        ]
        
        for file_path in extension_files:
            if not Path(file_path).exists():
                print(f"  ❌ Missing VS Code extension file: {file_path}")
                return False
        
        # Check package.json for API key commands
        with open("vscode-extension/package.json", 'r') as f:
            package_data = json.load(f)
        
        commands = package_data.get("contributes", {}).get("commands", [])
        api_key_command_found = any(
            cmd.get("command") == "aetherforge.configureApiKeys" 
            for cmd in commands
        )
        
        if not api_key_command_found:
            print("  ❌ API key configuration command not found in package.json")
            return False
        
        # Check for API key settings
        configuration = package_data.get("contributes", {}).get("configuration", {})
        properties = configuration.get("properties", {})
        
        api_key_settings = [
            key for key in properties.keys() 
            if "apiKeys" in key
        ]
        
        if not api_key_settings:
            print("  ❌ API key settings not found in package.json")
            return False
        
        print(f"  ✅ VS Code extension integration working ({len(api_key_settings)} API key settings found)")
        return True
        
    except Exception as e:
        print(f"  ❌ VS Code integration test error: {e}")
        return False

def save_test_report(results, duration):
    """Save detailed test report"""
    try:
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "duration_seconds": duration,
            "results": results,
            "summary": {
                "total_suites": len(results),
                "passed_suites": sum(1 for r in results.values() if r),
                "failed_suites": sum(1 for r in results.values() if not r),
                "success_rate": sum(1 for r in results.values() if r) / len(results) * 100
            }
        }
        
        with open('logs/api_key_test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: logs/api_key_test_report.json")
        
    except Exception as e:
        print(f"⚠️ Could not save test report: {e}")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
