# TaoForge Installation Guide

This comprehensive guide will help you install and configure TaoForge on your system. Choose the installation method that best fits your needs and environment.

## 🎯 System Requirements

### Minimum Requirements
- **Operating System:** Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **Python:** 3.9 or higher
- **Node.js:** 16.0 or higher (for web projects)
- **Memory:** 4GB RAM minimum, 8GB recommended
- **Storage:** 2GB free space
- **Internet:** Stable connection for AI API calls

### Recommended Requirements
- **Memory:** 16GB RAM for optimal performance
- **Storage:** 10GB free space for projects and dependencies
- **CPU:** Multi-core processor for faster project generation
- **GPU:** Optional, for local AI model inference

### Required External Services
- **AI API Key** - Choose one or more providers:
  - **OpenAI API Key** - Best performance and reliability
  - **OpenRouter API Key** - Access to multiple AI models
  - **Anthropic API Key** - Claude models
  - **Azure OpenAI** - Enterprise deployment
- **Git** - For version control (recommended)
- **Docker** - For containerized development (optional)

## 🚀 Quick Installation

### Option 1: Using pip (Recommended)

```bash
# Install TaoForge
pip install taoforge

# Verify installation
taoforge --version

# Initialize configuration
taoforge init

# Set up your API keys using the interactive wizard
taoforge keys setup

# Or set individual keys
taoforge keys set openai your_openai_key_here
taoforge keys set openrouter your_openrouter_key_here
```

### Option 2: Using pipx (Isolated Installation)

```bash
# Install pipx if not already installed
pip install pipx

# Install TaoForge in isolated environment
pipx install taoforge

# Verify installation
taoforge --version
```

### Option 3: Using Homebrew (macOS)

```bash
# Add TaoForge tap
brew tap taoforge/taoforge

# Install TaoForge
brew install taoforge

# Verify installation
taoforge --version
```

## 🔧 Detailed Installation

### Step 1: Install Python Dependencies

#### On Windows

```powershell
# Using Windows Package Manager
winget install Python.Python.3.11

# Or download from python.org
# https://www.python.org/downloads/windows/

# Verify Python installation
python --version
pip --version
```

#### On macOS

```bash
# Using Homebrew (recommended)
brew install python@3.11

# Or using pyenv
brew install pyenv
pyenv install 3.11.0
pyenv global 3.11.0

# Verify installation
python3 --version
pip3 --version
```

#### On Linux (Ubuntu/Debian)

```bash
# Update package list
sudo apt update

# Install Python and pip
sudo apt install python3.11 python3.11-pip python3.11-venv

# Verify installation
python3 --version
pip3 --version
```

### Step 2: Install Node.js (for web projects)

#### Using Node Version Manager (Recommended)

```bash
# Install nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Restart terminal or source profile
source ~/.bashrc

# Install latest LTS Node.js
nvm install --lts
nvm use --lts

# Verify installation
node --version
npm --version
```

#### Direct Installation

- **Windows:** Download from [nodejs.org](https://nodejs.org/)
- **macOS:** `brew install node`
- **Linux:** `sudo apt install nodejs npm`

### Step 3: Install TaoForge

#### Production Installation

```bash
# Create virtual environment (recommended)
python -m venv taoforge-env

# Activate virtual environment
# Windows:
taoforge-env\Scripts\activate
# macOS/Linux:
source taoforge-env/bin/activate

# Install TaoForge
pip install taoforge

# Verify installation
taoforge --version
```

#### Development Installation

```bash
# Clone repository
git clone https://github.com/taoforge/taoforge.git
cd taoforge

# Create virtual environment
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows

# Install in development mode
pip install -e .

# Install development dependencies
pip install -r requirements-dev.txt

# Verify installation
taoforge --version
```

## ⚙️ Configuration

### Step 1: Initialize TaoForge

```bash
# Initialize configuration
taoforge init

# This creates:
# ~/.taoforge/config.json
# ~/.taoforge/workflows/
# ~/.taoforge/templates/
# ~/.taoforge/logs/
```

### Step 2: Configure API Keys

#### Interactive Setup (Recommended)

```bash
# Run the interactive setup wizard
taoforge keys setup

# This will guide you through:
# - Choosing AI providers
# - Setting up API keys
# - Testing connections
# - Configuring preferences
```

#### Manual API Key Configuration

```bash
# Set OpenAI API key (recommended for best performance)
taoforge keys set openai sk-your-openai-api-key

# Set OpenRouter API key (access to multiple models)
taoforge keys set openrouter sk-or-your-openrouter-key

# Set Anthropic API key (Claude models)
taoforge keys set anthropic sk-ant-your-anthropic-key

# Set Azure OpenAI (enterprise deployment)
taoforge keys set azure your-azure-key

# Verify API connections
taoforge keys test openai
taoforge keys test openrouter
```

#### Environment Variable Configuration

```bash
# Alternative: Set environment variables
export OPENAI_API_KEY=sk-your-openai-api-key
export OPENROUTER_API_KEY=sk-or-your-openrouter-key
export ANTHROPIC_API_KEY=sk-ant-your-anthropic-key
export AZURE_OPENAI_API_KEY=your-azure-key
export AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
```

#### Additional API Keys

```bash
# GitHub API (for repository integration)
taoforge config set GITHUB_TOKEN ghp_your-github-token

# Google Cloud API (for additional services)
taoforge config set GOOGLE_CLOUD_API_KEY your-google-key
```

### Step 3: Configure System Settings

```bash
# Set default project directory
taoforge config set PROJECTS_DIR ~/taoforge-projects

# Set log level
taoforge config set LOG_LEVEL info

# Set default project type
taoforge config set DEFAULT_PROJECT_TYPE fullstack

# Set orchestrator port
taoforge config set ORCHESTRATOR_PORT 8000
```

### Step 4: Verify Configuration

```bash
# Check system status
taoforge status

# Expected output:
# ✅ TaoForge v1.0.0
# ✅ Python 3.11.0
# ✅ Node.js 18.17.0
# ✅ API Providers: 2 configured (OpenAI, OpenRouter)
# ✅ Configuration: Valid
# ✅ Projects Directory: ~/taoforge-projects

# Check API key status
taoforge keys list
# Expected output:
# 🔑 API Key Status:
# OPENAI       | ✅ Active        | Model: gpt-4
# OPENROUTER   | ✅ Active        | Model: openai/gpt-3.5-turbo
# ANTHROPIC    | ❌ Not configured | Model: claude-3-sonnet-20240229
```

## 🐳 Docker Installation

### Option 1: Using Docker Compose (Recommended)

```bash
# Create project directory
mkdir taoforge-docker
cd taoforge-docker

# Download docker-compose.yml
curl -O https://raw.githubusercontent.com/taoforge/taoforge/main/docker-compose.yml

# Set environment variables
echo "OPENAI_API_KEY=your_api_key_here" > .env

# Start TaoForge
docker-compose up -d

# Verify installation
docker-compose ps
```

### Option 2: Using Docker Run

```bash
# Pull the latest image
docker pull taoforge/taoforge:latest

# Run TaoForge
docker run -d \
  --name taoforge \
  -p 8000:8000 \
  -e OPENAI_API_KEY=your_api_key_here \
  -v $(pwd)/projects:/app/projects \
  taoforge/taoforge:latest

# Check status
docker ps
```

### Docker Configuration

Create a `docker-compose.yml` file:

```yaml
version: '3.8'

services:
  taoforge:
    image: taoforge/taoforge:latest
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - PROJECTS_DIR=/app/projects
      - LOG_LEVEL=info
    volumes:
      - ./projects:/app/projects
      - ./config:/app/config
    restart: unless-stopped

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=taoforge
      - POSTGRES_USER=taoforge
      - POSTGRES_PASSWORD=taoforge_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

volumes:
  postgres_data:
```

## 🎨 VS Code Extension Installation

### From VS Code Marketplace

1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "TaoForge"
4. Click "Install"
5. Reload VS Code

### From Command Line

```bash
# Install extension
code --install-extension taoforge.taoforge-vscode

# Verify installation
code --list-extensions | grep taoforge
```

### Manual Installation

```bash
# Download VSIX file
curl -L -o taoforge-vscode.vsix https://github.com/taoforge/vscode-extension/releases/latest/download/taoforge-vscode.vsix

# Install from VSIX
code --install-extension taoforge-vscode.vsix
```

## 🔧 Advanced Configuration

### Custom Configuration File

Create `~/.taoforge/config.json`:

```json
{
  "api": {
    "openai_api_key": "your-api-key",
    "base_url": "https://api.openai.com/v1",
    "timeout": 60,
    "max_retries": 3
  },
  "orchestrator": {
    "host": "localhost",
    "port": 8000,
    "workers": 4,
    "max_concurrent_projects": 10
  },
  "projects": {
    "default_directory": "~/taoforge-projects",
    "auto_git_init": true,
    "auto_npm_install": true,
    "backup_enabled": true
  },
  "agents": {
    "analyst": {
      "model": "gpt-4",
      "temperature": 0.3,
      "max_tokens": 4000
    },
    "architect": {
      "model": "gpt-4",
      "temperature": 0.2,
      "max_tokens": 6000
    },
    "developer": {
      "model": "gpt-4",
      "temperature": 0.1,
      "max_tokens": 8000
    },
    "qa": {
      "model": "gpt-3.5-turbo",
      "temperature": 0.2,
      "max_tokens": 4000
    }
  },
  "logging": {
    "level": "info",
    "file": "~/.taoforge/logs/taoforge.log",
    "max_size": "10MB",
    "backup_count": 5
  }
}
```

### Environment Variables

Create `.env` file:

```bash
# API Configuration
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_key
GITHUB_TOKEN=your_github_token

# System Configuration
TAOFORGE_ENV=production
TAOFORGE_DEBUG=false
TAOFORGE_LOG_LEVEL=info

# Orchestrator Configuration
ORCHESTRATOR_HOST=localhost
ORCHESTRATOR_PORT=8000
ORCHESTRATOR_WORKERS=4

# Project Configuration
PROJECTS_DIR=~/taoforge-projects
AUTO_GIT_INIT=true
AUTO_NPM_INSTALL=true

# Database Configuration (if using external DB)
DATABASE_URL=postgresql://user:password@localhost:5432/taoforge

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379
```

## 🧪 Testing Installation

### Basic Functionality Test

```bash
# Test system status
taoforge status

# Test API connection
taoforge test-api

# Create a simple test project
taoforge create "A simple hello world web page" \
  --name HelloWorld \
  --type frontend \
  --test-mode

# Check if project was created
ls ~/taoforge-projects/HelloWorld
```

### Performance Test

```bash
# Run performance benchmarks
taoforge benchmark

# Test concurrent project creation
taoforge stress-test --projects 5 --concurrent 2
```

## 🆘 Troubleshooting

### Common Issues

#### Python Version Issues

```bash
# Check Python version
python --version

# If using wrong version, use pyenv
pyenv install 3.11.0
pyenv global 3.11.0
```

#### Permission Issues (Linux/macOS)

```bash
# Fix pip permissions
pip install --user taoforge

# Or use virtual environment
python -m venv taoforge-env
source taoforge-env/bin/activate
pip install taoforge
```

#### API Connection Issues

```bash
# Test API key
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/models

# Check firewall settings
taoforge test-connection

# Verify proxy settings
taoforge config set HTTP_PROXY http://proxy:8080
```

#### Port Conflicts

```bash
# Check if port 8000 is in use
netstat -an | grep 8000

# Use different port
taoforge serve --port 8001

# Or kill conflicting process
sudo lsof -ti:8000 | xargs kill -9
```

### Getting Help

If you encounter issues:

1. **Check logs:** `taoforge logs --tail 50`
2. **Run diagnostics:** `taoforge diagnose`
3. **Reset configuration:** `taoforge reset --config`
4. **Reinstall:** `pip uninstall taoforge && pip install taoforge`
5. **Get support:** Visit our [support page](support/README.md)

## ✅ Next Steps

After successful installation:

1. **[Quick Start Tutorial](tutorials/quick-start-tutorial.md)** - Create your first project
2. **[User Guide](user-guides/beginners-guide.md)** - Learn the basics
3. **[Configuration Guide](configuration/README.md)** - Customize your setup
4. **[VS Code Extension Guide](vscode/README.md)** - Set up the extension

**Congratulations!** TaoForge is now installed and ready to transform your development workflow. Start creating amazing projects with the power of autonomous AI development!
