# Aetherforge API Key Management System

## Overview

The Aetherforge API Key Management System provides secure, centralized management of API keys for multiple AI providers. It features encrypted storage, key validation, multi-provider support, and a user-friendly CLI interface.

## Features

### 🔐 **Secure Storage**
- **Encrypted Storage**: API keys are encrypted using industry-standard cryptography
- **Master Key Protection**: Uses PBKDF2 with SHA-256 for key derivation
- **Local Storage**: Keys stored locally in `~/.aetherforge/keys.enc`
- **No Cloud Dependencies**: Complete offline operation

### 🌐 **Multi-Provider Support**
- **OpenAI**: GPT models (GPT-4, GPT-3.5-turbo)
- **Anthropic**: Claude models (Claude-3-<PERSON><PERSON>, Claude-3-<PERSON><PERSON>)
- **Azure OpenAI**: Enterprise-grade OpenAI models via Azure
- **OpenRouter**: Access to multiple AI models through one API (OpenAI, Anthropic, Meta, Google, and more)
- **Local Models**: Ollama and other local API endpoints
- **Extensible**: Easy to add new providers

### ✅ **Key Validation**
- **Real-time Testing**: Validate keys against actual API endpoints
- **Model Discovery**: Automatically detect available models
- **Error Reporting**: Detailed error messages for troubleshooting
- **Quota Checking**: Detect quota and rate limit issues

### 🛠️ **CLI Interface**
- **Interactive Setup**: Guided configuration wizard
- **Key Management**: Set, test, list, and remove API keys
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **Batch Operations**: Manage multiple providers efficiently

## Quick Start

### 1. Installation

The API key management system is included with Aetherforge. Ensure you have the required dependencies:

```bash
pip install cryptography anthropic openai aiohttp
```

### 2. Initial Setup

Run the interactive setup wizard:

```bash
python aetherforge-keys setup
```

Or on Windows:
```cmd
aetherforge-keys.bat setup
```

### 3. Manual Key Configuration

Set API keys manually:

```bash
# Set OpenAI API key
python aetherforge-keys set openai

# Set Anthropic API key with validation
python aetherforge-keys set anthropic sk-ant-your-key-here

# Set key without validation
python aetherforge-keys set openai --no-validate
```

### 4. View Configuration

List all configured providers:

```bash
python aetherforge-keys list
```

### 5. Test API Keys

Validate your API keys:

```bash
# Test specific provider
python aetherforge-keys test openai

# Test all providers
python aetherforge-keys test openai
python aetherforge-keys test anthropic
```

## CLI Reference

### Commands

#### `setup`
Run the interactive setup wizard for first-time configuration.

```bash
python aetherforge-keys setup
```

#### `set <provider> [--key KEY] [--no-validate]`
Set an API key for a provider.

**Parameters:**
- `provider`: Provider name (openai, anthropic, azure, openrouter, local, ollama)
- `--key`: API key (optional, will prompt if not provided)
- `--no-validate`: Skip key validation

**Examples:**
```bash
python aetherforge-keys set openai
python aetherforge-keys set anthropic sk-ant-123...
python aetherforge-keys set openrouter sk-or-123...
python aetherforge-keys set azure --no-validate
python aetherforge-keys set local --no-validate
```

#### `test <provider>`
Test an API key by making a validation request.

```bash
python aetherforge-keys test openai
```

#### `list`
List all configured providers and their status.

```bash
python aetherforge-keys list
```

#### `remove <provider>`
Remove an API key for a provider.

```bash
python aetherforge-keys remove anthropic
```

## Programming Interface

### Basic Usage

```python
from api_manager import APIManager, APIProvider

# Initialize API manager
api_manager = APIManager()

# Set an API key
result = await api_manager.set_api_key(
    APIProvider.OPENAI, 
    "sk-your-key-here",
    validate=True
)

# Get an API key
api_key = api_manager.get_api_key(APIProvider.OPENAI)

# Generate text
messages = [{"role": "user", "content": "Hello, world!"}]
response = await api_manager.generate_text(messages)
```

### Advanced Configuration

```python
from api_manager import APIManager, SecureKeyStorage, APIKeyValidator

# Custom storage location
storage = SecureKeyStorage("/custom/path/keys.enc")

# Manual key validation
validator = APIKeyValidator()
result = await validator.validate_openai_key("sk-test-key")

# API manager with custom storage
api_manager = APIManager(storage_path="/custom/path/keys.enc")
```

## Security Considerations

### Encryption Details

- **Algorithm**: AES-256 in Fernet mode (symmetric encryption)
- **Key Derivation**: PBKDF2-HMAC-SHA256 with 100,000 iterations
- **Salt**: Random 16-byte salt for each installation
- **Master Key**: Derived from environment variable or default passphrase

### Best Practices

1. **Environment Variables**: Set `AETHERFORGE_MASTER_KEY` for additional security
2. **File Permissions**: Ensure `~/.aetherforge/` has restricted permissions
3. **Key Rotation**: Regularly rotate API keys
4. **Monitoring**: Monitor API usage for unusual activity

### Storage Locations

- **Keys**: `~/.aetherforge/keys.enc` (encrypted)
- **Master Key**: `~/.aetherforge/key.key`
- **Salt**: `~/.aetherforge/salt`

## Provider Configuration

### OpenAI

```bash
# Get API key from: https://platform.openai.com/api-keys
python aetherforge-keys set openai
```

**Supported Models:**
- gpt-4
- gpt-4-turbo
- gpt-3.5-turbo

### Anthropic

```bash
# Get API key from: https://console.anthropic.com/
python aetherforge-keys set anthropic
```

**Supported Models:**
- claude-3-sonnet-********
- claude-3-haiku-********
- claude-3-opus-********

### OpenRouter

```bash
# Get API key from: https://openrouter.ai/keys
python aetherforge-keys set openrouter
```

**Supported Models:**
- openai/gpt-4
- openai/gpt-3.5-turbo
- anthropic/claude-3-sonnet
- anthropic/claude-3-haiku
- meta-llama/llama-2-70b-chat
- google/palm-2-chat-bison
- And many more models from various providers

**Features:**
- Access to multiple AI providers through one API
- Competitive pricing and model variety
- Automatic model routing and fallback
- Real-time model availability

### Azure OpenAI

```bash
# Get API key and endpoint from Azure Portal
python aetherforge-keys set azure
```

**Configuration:**
- Requires Azure endpoint URL
- Requires API version (default: 2024-02-01)
- Enterprise-grade security and compliance

### Local Models (Ollama)

```bash
# Ensure Ollama is running on http://localhost:11434
python aetherforge-keys set local
```

**Configuration:**
- Default URL: `http://localhost:11434`
- Custom URL: Set `LOCAL_API_URL` environment variable

## Troubleshooting

### Common Issues

#### "No module named 'cryptography'"
```bash
pip install cryptography
```

#### "No module named 'anthropic'"
```bash
pip install anthropic
```

#### "API key validation failed"
- Check if the API key is correct
- Verify internet connection
- Check API provider status
- Ensure sufficient quota/credits
- For OpenRouter: Verify key starts with `sk-or-`
- For Azure: Ensure endpoint URL and API version are correct

#### "Permission denied" on key storage
```bash
# Fix permissions (Unix/Linux/macOS)
chmod 700 ~/.aetherforge/
chmod 600 ~/.aetherforge/*
```

### Debug Mode

Enable verbose logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Reset Configuration

To completely reset the API key configuration:

```bash
# Remove all stored keys
rm -rf ~/.aetherforge/
```

## Integration Examples

### Project Generator Integration

```python
from api_manager import APIManager
from project_generator_standalone import ProjectGenerator

# Initialize with API manager
api_manager = APIManager()
generator = ProjectGenerator()
generator.api_manager = api_manager

# Generate project with authenticated APIs
result = await generator.generate_project(
    prompt="Create a task management app",
    project_name="TaskApp",
    project_type="fullstack"
)
```

### Custom Provider Integration

```python
from api_manager import APIProvider, APIConfig

# Add custom provider
class CustomProvider(APIProvider):
    CUSTOM = "custom"

# Configure custom provider
config = APIConfig(
    provider=CustomProvider.CUSTOM,
    api_key="custom-key",
    base_url="https://api.custom.com",
    model="custom-model"
)
```

## API Reference

See the inline documentation in `src/api_manager.py` for detailed API reference.

## Support

For issues and questions:
1. Check this documentation
2. Review the troubleshooting section
3. Check the test files for usage examples
4. Open an issue in the project repository
