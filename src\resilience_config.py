"""
Configuration system for API Resilience Layer

This module provides comprehensive configuration management for all resilience features
including retry behavior, fallback strategies, quota management, and monitoring.
"""

import json
import os
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass, field, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class ResilienceConfig:
    """Main configuration class for API resilience features"""
    
    # Feature toggles
    enable_retries: bool = True
    enable_fallbacks: bool = True
    enable_quota_management: bool = True
    enable_caching: bool = True
    enable_degraded_mode: bool = True
    enable_notifications: bool = True
    
    # Retry configuration
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    jitter_range: float = 0.1
    
    # Error-specific retry settings
    retry_on_rate_limit: bool = True
    retry_on_server_error: bool = True
    retry_on_timeout: bool = True
    retry_on_connection: bool = True
    retry_on_authentication: bool = False
    retry_on_quota: bool = False
    
    # Error-specific delays
    rate_limit_delay: float = 5.0
    server_error_delay: float = 2.0
    timeout_delay: float = 1.0
    connection_delay: float = 1.0
    
    # Fallback configuration
    provider_fallback_order: List[str] = field(default_factory=lambda: [
        "openai", "openrouter", "anthropic", "azure", "local"
    ])
    
    # Model fallback chains
    model_fallback_chains: Dict[str, List[str]] = field(default_factory=lambda: {
        "gpt-4": ["gpt-4", "gpt-3.5-turbo", "gpt-3.5-turbo-16k"],
        "gpt-3.5-turbo": ["gpt-3.5-turbo", "gpt-3.5-turbo-16k"],
        "claude-3-sonnet": ["claude-3-sonnet", "claude-3-haiku"],
        "claude-3-opus": ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku"],
        "openai/gpt-4": ["openai/gpt-4", "openai/gpt-3.5-turbo"],
        "openai/gpt-3.5-turbo": ["openai/gpt-3.5-turbo", "anthropic/claude-3-haiku"]
    })
    
    # Cache configuration
    cache_ttl: int = 3600  # 1 hour
    cache_max_size: int = 1000
    cache_enabled_for_agents: List[str] = field(default_factory=lambda: [
        "analyst", "architect", "developer", "qa"
    ])
    
    # Quota management
    quota_warning_threshold: float = 0.8  # 80%
    quota_critical_threshold: float = 0.95  # 95%
    cost_warning_threshold: float = 50.0  # $50
    cost_critical_threshold: float = 100.0  # $100
    
    # Quota limits (can be overridden per provider)
    default_daily_token_limit: Optional[int] = None
    default_monthly_token_limit: Optional[int] = None
    default_daily_cost_limit: Optional[float] = None
    default_monthly_cost_limit: Optional[float] = None
    
    # Provider-specific quota limits
    provider_quota_limits: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # Degraded mode configuration
    degraded_response_template: str = "I apologize, but I'm currently experiencing technical difficulties. Please try again later."
    degraded_mode_triggers: List[str] = field(default_factory=lambda: [
        "all_providers_failed", "quota_exceeded", "critical_errors"
    ])
    
    # Notification configuration
    notification_max_history: int = 1000
    notification_cleanup_interval: int = 3600  # 1 hour
    log_resilience_events: bool = True
    log_quota_warnings: bool = True
    log_fallback_usage: bool = True
    
    # Monitoring and statistics
    enable_performance_monitoring: bool = True
    enable_usage_projections: bool = True
    statistics_retention_days: int = 30
    
    # Agent-specific configurations
    agent_specific_configs: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        "analyst": {
            "preferred_provider": "openai",
            "max_tokens": 3000,
            "temperature": 0.7,
            "enable_caching": True
        },
        "architect": {
            "preferred_provider": "openai",
            "max_tokens": 4000,
            "temperature": 0.8,
            "enable_caching": True
        },
        "developer": {
            "preferred_provider": "openai",
            "max_tokens": 2000,
            "temperature": 0.6,
            "enable_caching": False  # Code generation should be fresh
        },
        "qa": {
            "preferred_provider": "anthropic",
            "max_tokens": 2500,
            "temperature": 0.5,
            "enable_caching": True
        }
    })

class ResilienceConfigManager:
    """Manages resilience configuration loading, saving, and updates"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = Path(config_path or "~/.aetherforge/resilience_config.json").expanduser()
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        self.config = ResilienceConfig()
        self._load_config()
    
    def _load_config(self):
        """Load configuration from file and environment variables"""
        # Load from file first
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    config_data = json.load(f)
                    self._update_config_from_dict(config_data)
                logger.info(f"Loaded resilience configuration from {self.config_path}")
            except Exception as e:
                logger.warning(f"Failed to load resilience config: {e}")
        
        # Override with environment variables
        self._load_from_environment()
    
    def _update_config_from_dict(self, config_data: Dict[str, Any]):
        """Update configuration from dictionary"""
        for key, value in config_data.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
    
    def _load_from_environment(self):
        """Load configuration overrides from environment variables"""
        env_mappings = {
            'AETHERFORGE_ENABLE_RETRIES': ('enable_retries', bool),
            'AETHERFORGE_ENABLE_FALLBACKS': ('enable_fallbacks', bool),
            'AETHERFORGE_ENABLE_QUOTA_MANAGEMENT': ('enable_quota_management', bool),
            'AETHERFORGE_ENABLE_CACHING': ('enable_caching', bool),
            'AETHERFORGE_ENABLE_DEGRADED_MODE': ('enable_degraded_mode', bool),
            'AETHERFORGE_MAX_RETRIES': ('max_retries', int),
            'AETHERFORGE_BASE_DELAY': ('base_delay', float),
            'AETHERFORGE_MAX_DELAY': ('max_delay', float),
            'AETHERFORGE_QUOTA_WARNING_THRESHOLD': ('quota_warning_threshold', float),
            'AETHERFORGE_COST_WARNING_THRESHOLD': ('cost_warning_threshold', float),
            'AETHERFORGE_CACHE_TTL': ('cache_ttl', int),
            'AETHERFORGE_CACHE_MAX_SIZE': ('cache_max_size', int),
        }
        
        for env_var, (config_key, config_type) in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                try:
                    if config_type == bool:
                        value = env_value.lower() in ('true', '1', 'yes', 'on')
                    else:
                        value = config_type(env_value)
                    setattr(self.config, config_key, value)
                    logger.debug(f"Set {config_key} = {value} from environment")
                except ValueError as e:
                    logger.warning(f"Invalid environment value for {env_var}: {e}")
    
    def save_config(self):
        """Save current configuration to file"""
        try:
            config_dict = asdict(self.config)
            with open(self.config_path, 'w') as f:
                json.dump(config_dict, f, indent=2)
            logger.info(f"Saved resilience configuration to {self.config_path}")
        except Exception as e:
            logger.error(f"Failed to save resilience config: {e}")
    
    def get_config(self) -> ResilienceConfig:
        """Get current configuration"""
        return self.config
    
    def update_config(self, **kwargs):
        """Update configuration with new values"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                logger.info(f"Updated resilience config: {key} = {value}")
            else:
                logger.warning(f"Unknown configuration key: {key}")
        
        # Save updated configuration
        self.save_config()
    
    def get_agent_config(self, agent_name: str) -> Dict[str, Any]:
        """Get agent-specific configuration"""
        return self.config.agent_specific_configs.get(agent_name, {})
    
    def update_agent_config(self, agent_name: str, **kwargs):
        """Update agent-specific configuration"""
        if agent_name not in self.config.agent_specific_configs:
            self.config.agent_specific_configs[agent_name] = {}
        
        self.config.agent_specific_configs[agent_name].update(kwargs)
        self.save_config()
        logger.info(f"Updated agent config for {agent_name}: {kwargs}")
    
    def get_provider_quota_limits(self, provider: str) -> Dict[str, Any]:
        """Get quota limits for a specific provider"""
        return self.config.provider_quota_limits.get(provider, {
            'daily_token_limit': self.config.default_daily_token_limit,
            'monthly_token_limit': self.config.default_monthly_token_limit,
            'daily_cost_limit': self.config.default_daily_cost_limit,
            'monthly_cost_limit': self.config.default_monthly_cost_limit
        })
    
    def set_provider_quota_limits(self, provider: str, **limits):
        """Set quota limits for a specific provider"""
        if provider not in self.config.provider_quota_limits:
            self.config.provider_quota_limits[provider] = {}
        
        self.config.provider_quota_limits[provider].update(limits)
        self.save_config()
        logger.info(f"Updated quota limits for {provider}: {limits}")
    
    def reset_to_defaults(self):
        """Reset configuration to defaults"""
        self.config = ResilienceConfig()
        self.save_config()
        logger.info("Reset resilience configuration to defaults")

# Global configuration manager instance
_config_manager: Optional[ResilienceConfigManager] = None

def get_config_manager() -> ResilienceConfigManager:
    """Get global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ResilienceConfigManager()
    return _config_manager

def get_resilience_config() -> ResilienceConfig:
    """Get current resilience configuration"""
    return get_config_manager().get_config()

def update_resilience_config(**kwargs):
    """Update resilience configuration"""
    get_config_manager().update_config(**kwargs)

def get_agent_config(agent_name: str) -> Dict[str, Any]:
    """Get agent-specific configuration"""
    return get_config_manager().get_agent_config(agent_name)
