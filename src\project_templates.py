#!/usr/bin/env python3
"""
Advanced Project Templates System for Aetherforge
Provides comprehensive, domain-specific project templates with intelligent configuration
"""

import json
import logging
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)

class ProjectDomain(Enum):
    """Project domain categories"""
    ECOMMERCE = "ecommerce"
    SAAS = "saas"
    MOBILE = "mobile"
    ENTERPRISE = "enterprise"
    FINTECH = "fintech"
    HEALTHCARE = "healthcare"
    EDUCATION = "education"
    GAMING = "gaming"
    IOT = "iot"
    BLOCKCHAIN = "blockchain"
    AI_ML = "ai_ml"
    SOCIAL = "social"
    MEDIA = "media"
    PRODUCTIVITY = "productivity"
    ANALYTICS = "analytics"

class ProjectComplexity(Enum):
    """Project complexity levels"""
    SIMPLE = "simple"
    STANDARD = "standard"
    ADVANCED = "advanced"
    ENTERPRISE = "enterprise"

class TechnologyStack(Enum):
    """Technology stack options"""
    REACT_NODE = "react_node"
    VUE_EXPRESS = "vue_express"
    ANGULAR_NESTJS = "angular_nestjs"
    NEXT_FULLSTACK = "next_fullstack"
    DJANGO_REACT = "django_react"
    FLASK_VUE = "flask_vue"
    SPRING_ANGULAR = "spring_angular"
    DOTNET_REACT = "dotnet_react"
    LARAVEL_VUE = "laravel_vue"
    RAILS_REACT = "rails_react"
    FLUTTER_FIREBASE = "flutter_firebase"
    REACT_NATIVE_NODE = "react_native_node"
    UNITY_CSHARP = "unity_csharp"
    ELECTRON_REACT = "electron_react"

@dataclass
class TemplateFeature:
    """Represents a feature that can be included in a project template"""
    name: str
    description: str
    required: bool = False
    dependencies: List[str] = field(default_factory=list)
    complexity_impact: int = 0  # 0-5 scale
    estimated_hours: int = 0
    technologies: List[str] = field(default_factory=list)
    files_to_generate: List[str] = field(default_factory=list)

@dataclass
class ProjectTemplate:
    """Comprehensive project template definition"""
    id: str
    name: str
    description: str
    domain: ProjectDomain
    complexity: ProjectComplexity
    tech_stack: TechnologyStack
    features: List[TemplateFeature]
    base_structure: Dict[str, Any]
    configuration: Dict[str, Any]
    estimated_hours: int
    prerequisites: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    version: str = "1.0.0"
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())

class ProjectTemplateManager:
    """Manages project templates and provides intelligent template selection"""
    
    def __init__(self):
        self.templates: Dict[str, ProjectTemplate] = {}
        self.features_catalog: Dict[str, TemplateFeature] = {}
        self._initialize_features_catalog()
        self._initialize_templates()
    
    def _initialize_features_catalog(self):
        """Initialize the catalog of available features"""
        
        # Authentication & Authorization Features
        self.features_catalog.update({
            "user_auth": TemplateFeature(
                name="User Authentication",
                description="Complete user authentication system with login, registration, password reset",
                required=True,
                complexity_impact=2,
                estimated_hours=8,
                technologies=["JWT", "bcrypt", "OAuth"],
                files_to_generate=["auth/login.js", "auth/register.js", "auth/middleware.js"]
            ),
            "oauth_integration": TemplateFeature(
                name="OAuth Integration",
                description="Social login with Google, Facebook, GitHub, etc.",
                dependencies=["user_auth"],
                complexity_impact=2,
                estimated_hours=6,
                technologies=["OAuth2", "Passport.js"],
                files_to_generate=["auth/oauth.js", "auth/strategies.js"]
            ),
            "rbac": TemplateFeature(
                name="Role-Based Access Control",
                description="Advanced permission system with roles and permissions",
                dependencies=["user_auth"],
                complexity_impact=3,
                estimated_hours=12,
                technologies=["RBAC", "ACL"],
                files_to_generate=["auth/rbac.js", "auth/permissions.js"]
            ),
            "mfa": TemplateFeature(
                name="Multi-Factor Authentication",
                description="Two-factor authentication with TOTP and SMS",
                dependencies=["user_auth"],
                complexity_impact=3,
                estimated_hours=10,
                technologies=["TOTP", "SMS", "QR Code"],
                files_to_generate=["auth/mfa.js", "auth/totp.js"]
            )
        })
        
        # E-commerce Features
        self.features_catalog.update({
            "product_catalog": TemplateFeature(
                name="Product Catalog",
                description="Complete product management with categories, variants, inventory",
                required=True,
                complexity_impact=3,
                estimated_hours=16,
                technologies=["Database", "Search", "Images"],
                files_to_generate=["models/product.js", "controllers/products.js", "views/catalog.js"]
            ),
            "shopping_cart": TemplateFeature(
                name="Shopping Cart",
                description="Persistent shopping cart with session management",
                dependencies=["product_catalog"],
                complexity_impact=2,
                estimated_hours=8,
                technologies=["Session", "LocalStorage", "Redis"],
                files_to_generate=["cart/cart.js", "cart/session.js"]
            ),
            "payment_processing": TemplateFeature(
                name="Payment Processing",
                description="Secure payment integration with multiple providers",
                dependencies=["shopping_cart", "user_auth"],
                complexity_impact=4,
                estimated_hours=20,
                technologies=["Stripe", "PayPal", "PCI DSS"],
                files_to_generate=["payments/stripe.js", "payments/paypal.js", "payments/webhook.js"]
            ),
            "order_management": TemplateFeature(
                name="Order Management",
                description="Complete order lifecycle management",
                dependencies=["payment_processing"],
                complexity_impact=3,
                estimated_hours=14,
                technologies=["State Machine", "Email", "PDF"],
                files_to_generate=["orders/orders.js", "orders/status.js", "orders/invoice.js"]
            ),
            "inventory_management": TemplateFeature(
                name="Inventory Management",
                description="Stock tracking, low stock alerts, supplier management",
                dependencies=["product_catalog"],
                complexity_impact=3,
                estimated_hours=12,
                technologies=["Database", "Alerts", "Analytics"],
                files_to_generate=["inventory/stock.js", "inventory/alerts.js"]
            )
        })
        
        # SaaS Features
        self.features_catalog.update({
            "subscription_billing": TemplateFeature(
                name="Subscription Billing",
                description="Recurring billing with multiple plans and usage tracking",
                dependencies=["user_auth"],
                complexity_impact=4,
                estimated_hours=18,
                technologies=["Stripe Billing", "Webhooks", "Metering"],
                files_to_generate=["billing/subscriptions.js", "billing/plans.js", "billing/usage.js"]
            ),
            "multi_tenancy": TemplateFeature(
                name="Multi-Tenancy",
                description="Complete tenant isolation and management",
                dependencies=["user_auth"],
                complexity_impact=4,
                estimated_hours=20,
                technologies=["Database Sharding", "Subdomain Routing"],
                files_to_generate=["tenancy/tenant.js", "tenancy/isolation.js"]
            ),
            "api_management": TemplateFeature(
                name="API Management",
                description="API versioning, rate limiting, documentation",
                complexity_impact=3,
                estimated_hours=12,
                technologies=["OpenAPI", "Rate Limiting", "Swagger"],
                files_to_generate=["api/versioning.js", "api/ratelimit.js", "api/docs.js"]
            ),
            "analytics_dashboard": TemplateFeature(
                name="Analytics Dashboard",
                description="Real-time analytics and reporting dashboard",
                complexity_impact=3,
                estimated_hours=16,
                technologies=["Charts.js", "D3.js", "Real-time"],
                files_to_generate=["analytics/dashboard.js", "analytics/charts.js"]
            )
        })
        
        # Mobile Features
        self.features_catalog.update({
            "offline_sync": TemplateFeature(
                name="Offline Synchronization",
                description="Offline-first architecture with data synchronization",
                complexity_impact=4,
                estimated_hours=20,
                technologies=["SQLite", "Sync Engine", "Conflict Resolution"],
                files_to_generate=["sync/offline.js", "sync/conflict.js"]
            ),
            "push_notifications": TemplateFeature(
                name="Push Notifications",
                description="Cross-platform push notifications",
                complexity_impact=2,
                estimated_hours=8,
                technologies=["FCM", "APNs", "OneSignal"],
                files_to_generate=["notifications/push.js", "notifications/fcm.js"]
            ),
            "camera_integration": TemplateFeature(
                name="Camera Integration",
                description="Camera access with photo/video capture and processing",
                complexity_impact=2,
                estimated_hours=10,
                technologies=["Camera API", "Image Processing"],
                files_to_generate=["camera/capture.js", "camera/processing.js"]
            ),
            "geolocation": TemplateFeature(
                name="Geolocation Services",
                description="GPS tracking, maps integration, location-based features",
                complexity_impact=2,
                estimated_hours=8,
                technologies=["GPS", "Maps API", "Geofencing"],
                files_to_generate=["location/gps.js", "location/maps.js"]
            )
        })
        
        # Common Features
        self.features_catalog.update({
            "real_time_chat": TemplateFeature(
                name="Real-time Chat",
                description="Live messaging with WebSocket support",
                dependencies=["user_auth"],
                complexity_impact=3,
                estimated_hours=14,
                technologies=["WebSocket", "Socket.io", "Message Queue"],
                files_to_generate=["chat/websocket.js", "chat/messages.js"]
            ),
            "file_upload": TemplateFeature(
                name="File Upload & Management",
                description="Secure file upload with cloud storage integration",
                complexity_impact=2,
                estimated_hours=8,
                technologies=["Multer", "AWS S3", "Image Optimization"],
                files_to_generate=["upload/files.js", "upload/storage.js"]
            ),
            "email_system": TemplateFeature(
                name="Email System",
                description="Transactional emails with templates and scheduling",
                complexity_impact=2,
                estimated_hours=6,
                technologies=["Nodemailer", "SendGrid", "Templates"],
                files_to_generate=["email/mailer.js", "email/templates.js"]
            ),
            "search_functionality": TemplateFeature(
                name="Advanced Search",
                description="Full-text search with filters and faceting",
                complexity_impact=3,
                estimated_hours=12,
                technologies=["Elasticsearch", "Algolia", "Full-text Search"],
                files_to_generate=["search/engine.js", "search/filters.js"]
            ),
            "caching_layer": TemplateFeature(
                name="Caching Layer",
                description="Multi-level caching for performance optimization",
                complexity_impact=2,
                estimated_hours=6,
                technologies=["Redis", "Memcached", "CDN"],
                files_to_generate=["cache/redis.js", "cache/strategy.js"]
            ),
            "logging_monitoring": TemplateFeature(
                name="Logging & Monitoring",
                description="Comprehensive logging and application monitoring",
                complexity_impact=2,
                estimated_hours=8,
                technologies=["Winston", "ELK Stack", "Prometheus"],
                files_to_generate=["logging/logger.js", "monitoring/metrics.js"]
            )
        })
    
    def _initialize_templates(self):
        """Initialize predefined project templates"""
        
        # E-commerce Platform Template
        ecommerce_template = ProjectTemplate(
            id="ecommerce_platform",
            name="E-commerce Platform",
            description="Complete e-commerce solution with product catalog, payments, and order management",
            domain=ProjectDomain.ECOMMERCE,
            complexity=ProjectComplexity.ADVANCED,
            tech_stack=TechnologyStack.REACT_NODE,
            features=[
                self.features_catalog["user_auth"],
                self.features_catalog["oauth_integration"],
                self.features_catalog["product_catalog"],
                self.features_catalog["shopping_cart"],
                self.features_catalog["payment_processing"],
                self.features_catalog["order_management"],
                self.features_catalog["inventory_management"],
                self.features_catalog["email_system"],
                self.features_catalog["search_functionality"],
                self.features_catalog["caching_layer"],
                self.features_catalog["logging_monitoring"]
            ],
            base_structure={
                "frontend": {
                    "src": {
                        "components": ["Header", "Footer", "ProductCard", "Cart", "Checkout"],
                        "pages": ["Home", "Products", "ProductDetail", "Cart", "Checkout", "Orders"],
                        "hooks": ["useAuth", "useCart", "useProducts"],
                        "services": ["api", "auth", "cart", "payments"],
                        "utils": ["helpers", "constants", "validators"]
                    },
                    "public": ["images", "icons", "manifest.json"]
                },
                "backend": {
                    "src": {
                        "controllers": ["auth", "products", "orders", "payments"],
                        "models": ["User", "Product", "Order", "Payment"],
                        "middleware": ["auth", "validation", "error"],
                        "routes": ["auth", "products", "orders", "payments"],
                        "services": ["email", "payment", "inventory"],
                        "utils": ["database", "helpers", "validators"]
                    },
                    "config": ["database", "redis", "stripe"]
                }
            },
            configuration={
                "database": "PostgreSQL",
                "cache": "Redis",
                "payment_provider": "Stripe",
                "email_service": "SendGrid",
                "image_storage": "AWS S3",
                "search_engine": "Elasticsearch"
            },
            estimated_hours=120,
            prerequisites=["Node.js", "PostgreSQL", "Redis"],
            tags=["ecommerce", "fullstack", "payments", "inventory"]
        )
        
        self.templates[ecommerce_template.id] = ecommerce_template

        # SaaS Dashboard Template
        saas_template = ProjectTemplate(
            id="saas_dashboard",
            name="SaaS Dashboard Platform",
            description="Multi-tenant SaaS platform with subscription billing and analytics",
            domain=ProjectDomain.SAAS,
            complexity=ProjectComplexity.ENTERPRISE,
            tech_stack=TechnologyStack.NEXT_FULLSTACK,
            features=[
                self.features_catalog["user_auth"],
                self.features_catalog["oauth_integration"],
                self.features_catalog["rbac"],
                self.features_catalog["multi_tenancy"],
                self.features_catalog["subscription_billing"],
                self.features_catalog["api_management"],
                self.features_catalog["analytics_dashboard"],
                self.features_catalog["real_time_chat"],
                self.features_catalog["email_system"],
                self.features_catalog["file_upload"],
                self.features_catalog["caching_layer"],
                self.features_catalog["logging_monitoring"]
            ],
            base_structure={
                "app": {
                    "dashboard": ["page.tsx", "layout.tsx", "loading.tsx"],
                    "auth": ["login", "register", "forgot-password"],
                    "billing": ["plans", "subscription", "invoices"],
                    "settings": ["profile", "team", "integrations"],
                    "api": {
                        "auth": ["route.ts"],
                        "billing": ["route.ts"],
                        "users": ["route.ts"],
                        "analytics": ["route.ts"]
                    }
                },
                "components": {
                    "ui": ["Button", "Input", "Modal", "Table", "Chart"],
                    "dashboard": ["Sidebar", "Header", "Stats", "Charts"],
                    "billing": ["PlanCard", "PaymentForm", "InvoiceList"],
                    "auth": ["LoginForm", "RegisterForm", "ProtectedRoute"]
                },
                "lib": {
                    "auth": ["config.ts", "providers.ts"],
                    "billing": ["stripe.ts", "plans.ts"],
                    "database": ["schema.ts", "queries.ts"],
                    "utils": ["helpers.ts", "validators.ts"]
                }
            },
            configuration={
                "database": "PostgreSQL with Prisma",
                "auth": "NextAuth.js",
                "billing": "Stripe",
                "cache": "Redis",
                "email": "Resend",
                "analytics": "Mixpanel",
                "deployment": "Vercel"
            },
            estimated_hours=160,
            prerequisites=["Node.js", "PostgreSQL", "Redis", "Stripe Account"],
            tags=["saas", "nextjs", "billing", "multi-tenant", "analytics"]
        )

        self.templates[saas_template.id] = saas_template

        # Mobile App Template
        mobile_template = ProjectTemplate(
            id="mobile_productivity_app",
            name="Mobile Productivity App",
            description="Cross-platform mobile app with offline sync and real-time features",
            domain=ProjectDomain.MOBILE,
            complexity=ProjectComplexity.ADVANCED,
            tech_stack=TechnologyStack.REACT_NATIVE_NODE,
            features=[
                self.features_catalog["user_auth"],
                self.features_catalog["oauth_integration"],
                self.features_catalog["offline_sync"],
                self.features_catalog["push_notifications"],
                self.features_catalog["camera_integration"],
                self.features_catalog["geolocation"],
                self.features_catalog["real_time_chat"],
                self.features_catalog["file_upload"],
                self.features_catalog["caching_layer"]
            ],
            base_structure={
                "mobile": {
                    "src": {
                        "screens": ["Home", "Profile", "Chat", "Camera", "Settings"],
                        "components": ["Button", "Input", "Card", "Modal", "List"],
                        "navigation": ["AppNavigator", "AuthNavigator", "TabNavigator"],
                        "services": ["api", "auth", "sync", "notifications"],
                        "hooks": ["useAuth", "useSync", "useLocation", "useCamera"],
                        "utils": ["storage", "permissions", "helpers"]
                    },
                    "assets": ["images", "icons", "fonts"]
                },
                "backend": {
                    "src": {
                        "controllers": ["auth", "sync", "notifications", "files"],
                        "models": ["User", "SyncData", "Notification"],
                        "middleware": ["auth", "validation", "sync"],
                        "routes": ["auth", "sync", "notifications", "files"],
                        "services": ["push", "sync", "storage"]
                    }
                }
            },
            configuration={
                "platform": "React Native",
                "backend": "Node.js with Express",
                "database": "MongoDB",
                "sync": "Custom sync engine",
                "push": "Firebase Cloud Messaging",
                "storage": "AWS S3",
                "maps": "Google Maps API"
            },
            estimated_hours=100,
            prerequisites=["React Native CLI", "Android Studio", "Xcode", "MongoDB"],
            tags=["mobile", "react-native", "offline", "sync", "cross-platform"]
        )

        self.templates[mobile_template.id] = mobile_template

        # Fintech Platform Template
        fintech_template = ProjectTemplate(
            id="fintech_platform",
            name="Fintech Trading Platform",
            description="Secure financial platform with trading, portfolio management, and compliance",
            domain=ProjectDomain.FINTECH,
            complexity=ProjectComplexity.ENTERPRISE,
            tech_stack=TechnologyStack.ANGULAR_NESTJS,
            features=[
                self.features_catalog["user_auth"],
                self.features_catalog["mfa"],
                self.features_catalog["rbac"],
                self.features_catalog["payment_processing"],
                self.features_catalog["analytics_dashboard"],
                self.features_catalog["real_time_chat"],
                self.features_catalog["email_system"],
                self.features_catalog["caching_layer"],
                self.features_catalog["logging_monitoring"]
            ],
            base_structure={
                "frontend": {
                    "src": {
                        "app": {
                            "dashboard": ["dashboard.component.ts", "dashboard.module.ts"],
                            "trading": ["trading.component.ts", "order-book.component.ts"],
                            "portfolio": ["portfolio.component.ts", "holdings.component.ts"],
                            "auth": ["login.component.ts", "mfa.component.ts"],
                            "shared": ["components", "services", "guards", "interceptors"]
                        },
                        "assets": ["styles", "images", "icons"]
                    }
                },
                "backend": {
                    "src": {
                        "modules": {
                            "auth": ["auth.module.ts", "auth.service.ts", "auth.controller.ts"],
                            "trading": ["trading.module.ts", "trading.service.ts"],
                            "portfolio": ["portfolio.module.ts", "portfolio.service.ts"],
                            "market-data": ["market-data.module.ts", "websocket.gateway.ts"]
                        },
                        "common": ["guards", "interceptors", "decorators", "filters"],
                        "config": ["database.config.ts", "redis.config.ts"]
                    }
                }
            },
            configuration={
                "frontend": "Angular with Angular Material",
                "backend": "NestJS with TypeORM",
                "database": "PostgreSQL",
                "cache": "Redis",
                "websocket": "Socket.io",
                "security": "Helmet, CORS, Rate Limiting",
                "compliance": "SOX, PCI DSS",
                "monitoring": "Prometheus + Grafana"
            },
            estimated_hours=200,
            prerequisites=["Node.js", "PostgreSQL", "Redis", "Financial Data API"],
            tags=["fintech", "trading", "security", "compliance", "real-time"]
        )

        self.templates[fintech_template.id] = fintech_template

        # Healthcare Platform Template
        healthcare_template = ProjectTemplate(
            id="healthcare_platform",
            name="Healthcare Management Platform",
            description="HIPAA-compliant healthcare platform with patient management and telemedicine",
            domain=ProjectDomain.HEALTHCARE,
            complexity=ProjectComplexity.ENTERPRISE,
            tech_stack=TechnologyStack.DJANGO_REACT,
            features=[
                self.features_catalog["user_auth"],
                self.features_catalog["mfa"],
                self.features_catalog["rbac"],
                self.features_catalog["real_time_chat"],
                self.features_catalog["file_upload"],
                self.features_catalog["email_system"],
                self.features_catalog["caching_layer"],
                self.features_catalog["logging_monitoring"]
            ],
            base_structure={
                "backend": {
                    "apps": {
                        "patients": ["models.py", "views.py", "serializers.py"],
                        "appointments": ["models.py", "views.py", "serializers.py"],
                        "medical_records": ["models.py", "views.py", "serializers.py"],
                        "telemedicine": ["models.py", "views.py", "consumers.py"],
                        "billing": ["models.py", "views.py", "serializers.py"]
                    },
                    "core": ["settings.py", "urls.py", "wsgi.py", "asgi.py"],
                    "utils": ["permissions.py", "encryption.py", "audit.py"]
                },
                "frontend": {
                    "src": {
                        "components": ["PatientCard", "AppointmentForm", "VideoCall"],
                        "pages": ["Dashboard", "Patients", "Appointments", "Records"],
                        "hooks": ["useAuth", "usePatients", "useAppointments"],
                        "services": ["api", "auth", "encryption", "video"]
                    }
                }
            },
            configuration={
                "backend": "Django with DRF",
                "frontend": "React with TypeScript",
                "database": "PostgreSQL with encryption",
                "compliance": "HIPAA, SOC 2",
                "video": "WebRTC",
                "encryption": "AES-256",
                "audit": "Comprehensive audit logging"
            },
            estimated_hours=180,
            prerequisites=["Python", "PostgreSQL", "Redis", "HIPAA Compliance"],
            tags=["healthcare", "hipaa", "telemedicine", "compliance", "security"]
        )

        self.templates[healthcare_template.id] = healthcare_template

        # AI/ML Platform Template
        ai_ml_template = ProjectTemplate(
            id="ai_ml_platform",
            name="AI/ML Model Platform",
            description="Machine learning platform with model training, deployment, and monitoring",
            domain=ProjectDomain.AI_ML,
            complexity=ProjectComplexity.ADVANCED,
            tech_stack=TechnologyStack.FLASK_VUE,
            features=[
                self.features_catalog["user_auth"],
                self.features_catalog["rbac"],
                self.features_catalog["api_management"],
                self.features_catalog["analytics_dashboard"],
                self.features_catalog["file_upload"],
                self.features_catalog["caching_layer"],
                self.features_catalog["logging_monitoring"]
            ],
            base_structure={
                "backend": {
                    "app": {
                        "models": ["user.py", "dataset.py", "model.py", "experiment.py"],
                        "routes": ["auth.py", "datasets.py", "models.py", "experiments.py"],
                        "services": ["ml_service.py", "training_service.py", "inference_service.py"],
                        "utils": ["data_processing.py", "model_utils.py", "metrics.py"]
                    },
                    "ml": {
                        "pipelines": ["preprocessing.py", "training.py", "evaluation.py"],
                        "models": ["base_model.py", "custom_models.py"],
                        "deployment": ["model_server.py", "api_wrapper.py"]
                    }
                },
                "frontend": {
                    "src": {
                        "components": ["ModelCard", "DatasetViewer", "ExperimentTracker"],
                        "pages": ["Dashboard", "Datasets", "Models", "Experiments"],
                        "services": ["api", "auth", "ml"],
                        "utils": ["charts", "data-processing"]
                    }
                }
            },
            configuration={
                "backend": "Flask with Celery",
                "frontend": "Vue.js with Vuetify",
                "ml_framework": "PyTorch/TensorFlow",
                "database": "PostgreSQL + MongoDB",
                "queue": "Redis + Celery",
                "storage": "AWS S3 + EFS",
                "monitoring": "MLflow + Prometheus"
            },
            estimated_hours=140,
            prerequisites=["Python", "PostgreSQL", "Redis", "GPU Access"],
            tags=["ai", "ml", "pytorch", "tensorflow", "mlops"]
        )

        self.templates[ai_ml_template.id] = ai_ml_template

    def get_template(self, template_id: str) -> Optional[ProjectTemplate]:
        """Get a specific template by ID"""
        return self.templates.get(template_id)

    def get_templates_by_domain(self, domain: ProjectDomain) -> List[ProjectTemplate]:
        """Get all templates for a specific domain"""
        return [template for template in self.templates.values() if template.domain == domain]

    def get_templates_by_complexity(self, complexity: ProjectComplexity) -> List[ProjectTemplate]:
        """Get all templates for a specific complexity level"""
        return [template for template in self.templates.values() if template.complexity == complexity]

    def get_templates_by_tech_stack(self, tech_stack: TechnologyStack) -> List[ProjectTemplate]:
        """Get all templates for a specific technology stack"""
        return [template for template in self.templates.values() if template.tech_stack == tech_stack]

    def search_templates(self, query: str) -> List[ProjectTemplate]:
        """Search templates by name, description, or tags"""
        query_lower = query.lower()
        results = []

        for template in self.templates.values():
            if (query_lower in template.name.lower() or
                query_lower in template.description.lower() or
                any(query_lower in tag.lower() for tag in template.tags)):
                results.append(template)

        return results

    def recommend_templates(self, requirements: Dict[str, Any]) -> List[ProjectTemplate]:
        """Recommend templates based on user requirements"""
        recommendations = []

        # Extract requirements
        domain = requirements.get('domain')
        complexity = requirements.get('complexity')
        tech_stack = requirements.get('tech_stack')
        required_features = requirements.get('features', [])
        max_hours = requirements.get('max_hours')

        for template in self.templates.values():
            score = 0

            # Domain match
            if domain and template.domain.value == domain:
                score += 10

            # Complexity match
            if complexity and template.complexity.value == complexity:
                score += 8

            # Tech stack match
            if tech_stack and template.tech_stack.value == tech_stack:
                score += 6

            # Feature match
            template_features = [f.name for f in template.features]
            feature_matches = sum(1 for feature in required_features if feature in template_features)
            score += feature_matches * 2

            # Time constraint
            if max_hours and template.estimated_hours <= max_hours:
                score += 4

            if score > 0:
                recommendations.append((template, score))

        # Sort by score and return templates
        recommendations.sort(key=lambda x: x[1], reverse=True)
        return [template for template, _ in recommendations]

    def customize_template(self, template_id: str, customizations: Dict[str, Any]) -> Optional[ProjectTemplate]:
        """Create a customized version of a template"""
        base_template = self.get_template(template_id)
        if not base_template:
            return None

        # Create a copy of the template
        customized = ProjectTemplate(
            id=f"{template_id}_custom_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            name=customizations.get('name', f"{base_template.name} (Custom)"),
            description=customizations.get('description', base_template.description),
            domain=base_template.domain,
            complexity=base_template.complexity,
            tech_stack=base_template.tech_stack,
            features=base_template.features.copy(),
            base_structure=base_template.base_structure.copy(),
            configuration=base_template.configuration.copy(),
            estimated_hours=base_template.estimated_hours,
            prerequisites=base_template.prerequisites.copy(),
            tags=base_template.tags.copy()
        )

        # Apply customizations
        if 'additional_features' in customizations:
            for feature_name in customizations['additional_features']:
                if feature_name in self.features_catalog:
                    customized.features.append(self.features_catalog[feature_name])
                    customized.estimated_hours += self.features_catalog[feature_name].estimated_hours

        if 'remove_features' in customizations:
            customized.features = [f for f in customized.features
                                 if f.name not in customizations['remove_features']]

        if 'configuration_overrides' in customizations:
            customized.configuration.update(customizations['configuration_overrides'])

        return customized

    def export_template(self, template_id: str, file_path: Path) -> bool:
        """Export a template to a JSON file"""
        template = self.get_template(template_id)
        if not template:
            return False

        try:
            template_data = {
                "id": template.id,
                "name": template.name,
                "description": template.description,
                "domain": template.domain.value,
                "complexity": template.complexity.value,
                "tech_stack": template.tech_stack.value,
                "features": [
                    {
                        "name": f.name,
                        "description": f.description,
                        "required": f.required,
                        "dependencies": f.dependencies,
                        "complexity_impact": f.complexity_impact,
                        "estimated_hours": f.estimated_hours,
                        "technologies": f.technologies,
                        "files_to_generate": f.files_to_generate
                    }
                    for f in template.features
                ],
                "base_structure": template.base_structure,
                "configuration": template.configuration,
                "estimated_hours": template.estimated_hours,
                "prerequisites": template.prerequisites,
                "tags": template.tags,
                "version": template.version,
                "created_at": template.created_at
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, indent=2, ensure_ascii=False)

            return True
        except Exception as e:
            logger.error(f"Failed to export template {template_id}: {e}")
            return False

    def import_template(self, file_path: Path) -> Optional[ProjectTemplate]:
        """Import a template from a JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)

            # Reconstruct features
            features = []
            for feature_data in template_data.get('features', []):
                feature = TemplateFeature(
                    name=feature_data['name'],
                    description=feature_data['description'],
                    required=feature_data.get('required', False),
                    dependencies=feature_data.get('dependencies', []),
                    complexity_impact=feature_data.get('complexity_impact', 0),
                    estimated_hours=feature_data.get('estimated_hours', 0),
                    technologies=feature_data.get('technologies', []),
                    files_to_generate=feature_data.get('files_to_generate', [])
                )
                features.append(feature)

            # Create template
            template = ProjectTemplate(
                id=template_data['id'],
                name=template_data['name'],
                description=template_data['description'],
                domain=ProjectDomain(template_data['domain']),
                complexity=ProjectComplexity(template_data['complexity']),
                tech_stack=TechnologyStack(template_data['tech_stack']),
                features=features,
                base_structure=template_data['base_structure'],
                configuration=template_data['configuration'],
                estimated_hours=template_data['estimated_hours'],
                prerequisites=template_data.get('prerequisites', []),
                tags=template_data.get('tags', []),
                version=template_data.get('version', '1.0.0'),
                created_at=template_data.get('created_at', datetime.now().isoformat())
            )

            # Add to templates
            self.templates[template.id] = template
            return template

        except Exception as e:
            logger.error(f"Failed to import template from {file_path}: {e}")
            return None

    def get_template_statistics(self) -> Dict[str, Any]:
        """Get statistics about available templates"""
        stats = {
            "total_templates": len(self.templates),
            "domains": {},
            "complexity_levels": {},
            "tech_stacks": {},
            "average_hours": 0,
            "total_features": len(self.features_catalog)
        }

        total_hours = 0
        for template in self.templates.values():
            # Domain stats
            domain = template.domain.value
            stats["domains"][domain] = stats["domains"].get(domain, 0) + 1

            # Complexity stats
            complexity = template.complexity.value
            stats["complexity_levels"][complexity] = stats["complexity_levels"].get(complexity, 0) + 1

            # Tech stack stats
            tech_stack = template.tech_stack.value
            stats["tech_stacks"][tech_stack] = stats["tech_stacks"].get(tech_stack, 0) + 1

            total_hours += template.estimated_hours

        if self.templates:
            stats["average_hours"] = total_hours / len(self.templates)

        return stats

    def validate_template(self, template: ProjectTemplate) -> List[str]:
        """Validate a template and return any issues"""
        issues = []

        # Check required fields
        if not template.name:
            issues.append("Template name is required")
        if not template.description:
            issues.append("Template description is required")
        if not template.features:
            issues.append("Template must have at least one feature")

        # Check feature dependencies
        feature_names = [f.name for f in template.features]
        for feature in template.features:
            for dependency in feature.dependencies:
                if dependency not in feature_names:
                    issues.append(f"Feature '{feature.name}' depends on missing feature '{dependency}'")

        # Check estimated hours
        calculated_hours = sum(f.estimated_hours for f in template.features)
        if abs(template.estimated_hours - calculated_hours) > calculated_hours * 0.2:
            issues.append(f"Estimated hours ({template.estimated_hours}) differs significantly from feature sum ({calculated_hours})")

        return issues

    def list_all_templates(self) -> List[Dict[str, Any]]:
        """Get a summary list of all templates"""
        return [
            {
                "id": template.id,
                "name": template.name,
                "description": template.description,
                "domain": template.domain.value,
                "complexity": template.complexity.value,
                "tech_stack": template.tech_stack.value,
                "estimated_hours": template.estimated_hours,
                "feature_count": len(template.features),
                "tags": template.tags
            }
            for template in self.templates.values()
        ]


# Factory function for easy access
def create_template_manager() -> ProjectTemplateManager:
    """Create and return a configured ProjectTemplateManager instance"""
    return ProjectTemplateManager()


# Example usage and testing
if __name__ == "__main__":
    # Create template manager
    manager = create_template_manager()

    # Print statistics
    stats = manager.get_template_statistics()
    print("Template Statistics:")
    print(f"Total templates: {stats['total_templates']}")
    print(f"Total features: {stats['total_features']}")
    print(f"Average hours: {stats['average_hours']:.1f}")
    print(f"Domains: {list(stats['domains'].keys())}")

    # Test template search
    ecommerce_templates = manager.get_templates_by_domain(ProjectDomain.ECOMMERCE)
    print(f"\nE-commerce templates: {len(ecommerce_templates)}")

    # Test recommendation
    requirements = {
        'domain': 'ecommerce',
        'complexity': 'advanced',
        'features': ['User Authentication', 'Payment Processing'],
        'max_hours': 150
    }

    recommendations = manager.recommend_templates(requirements)
    print(f"\nRecommendations for requirements: {len(recommendations)}")
    for template in recommendations[:3]:
        print(f"- {template.name} ({template.estimated_hours}h)")

    # List all templates
    all_templates = manager.list_all_templates()
    print(f"\nAll available templates:")
    for template in all_templates:
        print(f"- {template['name']} ({template['domain']}, {template['complexity']}, {template['estimated_hours']}h)")
