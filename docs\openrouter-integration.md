# OpenRouter Integration Guide

## Overview

Aetherforge now supports [OpenRouter](https://openrouter.ai), a unified API that provides access to multiple AI models from different providers through a single interface. This integration allows you to access models from OpenAI, Anthropic, Meta, Google, and many other providers with competitive pricing and unified billing.

## What is OpenRouter?

OpenRouter is a service that aggregates multiple AI model providers into a single API endpoint. Key benefits include:

- **Unified API**: Access models from multiple providers with one API key
- **Competitive Pricing**: Often lower costs than direct provider APIs
- **Model Variety**: Access to cutting-edge models from various providers
- **Fallback Support**: Automatic failover between models
- **Usage Analytics**: Detailed usage tracking and analytics

## Supported Models

OpenRouter provides access to models from:

- **OpenAI**: GPT-4, GPT-3.5-turbo, GPT-4-turbo
- **Anthropic**: Claude-3 (Opus, Sonnet, Haiku)
- **Meta**: Llama 2, Code Llama
- **Google**: PaLM, Gemini
- **Mistral**: Mistral 7B, Mixtral
- **And many more**: Cohere, AI21, Together AI, etc.

## Setup Instructions

### 1. Get Your OpenRouter API Key

1. Visit [https://openrouter.ai](https://openrouter.ai)
2. Sign up for an account
3. Navigate to [API Keys](https://openrouter.ai/keys)
4. Create a new API key
5. Copy the key (starts with `sk-or-`)

### 2. Configure in Aetherforge

#### Option A: Interactive Setup Wizard
```bash
python main.py keys setup
# or
python aetherforge-keys setup
```

Follow the prompts and select "y" when asked about OpenRouter configuration.

#### Option B: Direct Configuration
```bash
python main.py keys set openrouter
# or
python aetherforge-keys set openrouter
```

#### Option C: Environment Variable
```bash
export OPENROUTER_API_KEY="sk-or-your-key-here"
```

### 3. Verify Configuration
```bash
python main.py keys test openrouter
# or
python aetherforge-keys test openrouter
```

## Usage Examples

### Basic Text Generation
```python
from api_manager import get_api_manager, APIProvider

# Get the API manager
api_manager = get_api_manager()

# Generate text using OpenRouter
response = await api_manager.generate_text(
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Explain quantum computing in simple terms."}
    ],
    provider=APIProvider.OPENROUTER,
    model="openai/gpt-4",  # OpenRouter model format
    max_tokens=500
)

print(response)
```

### Model Selection
OpenRouter uses a specific format for model names: `provider/model-name`

Popular models:
- `openai/gpt-4` - GPT-4 via OpenRouter
- `openai/gpt-3.5-turbo` - GPT-3.5 Turbo
- `anthropic/claude-3-sonnet` - Claude 3 Sonnet
- `anthropic/claude-3-haiku` - Claude 3 Haiku (faster, cheaper)
- `meta-llama/llama-2-70b-chat` - Llama 2 70B
- `mistralai/mixtral-8x7b-instruct` - Mixtral 8x7B

### Configuration Options

You can customize OpenRouter settings via environment variables:

```bash
# Base URL (usually don't need to change)
export OPENROUTER_BASE_URL="https://openrouter.ai/api/v1"

# Default model
export OPENROUTER_MODEL="openai/gpt-3.5-turbo"

# Rate limiting (requests per minute)
export OPENROUTER_RATE_LIMIT="60"

# Max tokens for responses
export OPENROUTER_MAX_TOKENS="2000"
```

## VS Code Extension Integration

OpenRouter is fully integrated into the VS Code extension:

1. Open Command Palette (`Ctrl+Shift+P`)
2. Run "Aetherforge: Configure API Keys"
3. Enter your OpenRouter API key in the configuration panel
4. Select OpenRouter as your preferred provider

## Pricing and Billing

OpenRouter offers competitive pricing:

- **Pay-per-use**: Only pay for what you use
- **Transparent pricing**: Clear pricing for each model
- **Volume discounts**: Lower rates for higher usage
- **No monthly fees**: No subscription required

Check current pricing at [https://openrouter.ai/docs#models](https://openrouter.ai/docs#models)

## Advanced Features

### Fallback Configuration

OpenRouter is included in Aetherforge's automatic fallback system:

```python
# Fallback order (can be customized)
fallback_order = [
    APIProvider.OPENAI,      # Try OpenAI first
    APIProvider.AZURE,       # Then Azure OpenAI
    APIProvider.OPENROUTER,  # Then OpenRouter
    APIProvider.ANTHROPIC,   # Then Anthropic
    APIProvider.LOCAL        # Finally local models
]
```

### Custom Headers

OpenRouter supports additional headers for enhanced functionality:

```python
# Set custom headers (if needed)
headers = {
    "HTTP-Referer": "https://your-app.com",  # Optional: for analytics
    "X-Title": "Your App Name"               # Optional: for analytics
}
```

### Model Routing

OpenRouter can automatically route to the best available model:

```python
# Use auto-routing (let OpenRouter choose the best model)
response = await api_manager.generate_text(
    messages=messages,
    provider=APIProvider.OPENROUTER,
    model="openrouter/auto",  # Auto-select best model
    max_tokens=500
)
```

## Troubleshooting

### Common Issues

1. **Invalid API Key**
   ```
   Error: Invalid API key
   Solution: Verify your API key at https://openrouter.ai/keys
   ```

2. **Model Not Available**
   ```
   Error: Model not available
   Solution: Check available models at https://openrouter.ai/docs#models
   ```

3. **Rate Limiting**
   ```
   Error: Rate limit exceeded
   Solution: Reduce request frequency or upgrade your OpenRouter plan
   ```

4. **Quota Exceeded**
   ```
   Error: Quota exceeded
   Solution: Add credits to your OpenRouter account
   ```

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.getLogger('api_manager').setLevel(logging.DEBUG)
```

### Testing Connection

Test your OpenRouter connection:

```bash
# Test with validation
python main.py keys test openrouter

# Test without validation (faster)
python main.py keys test openrouter --no-validate
```

## Best Practices

1. **Model Selection**: Choose models based on your use case
   - Use `claude-3-haiku` for fast, simple tasks
   - Use `gpt-4` for complex reasoning
   - Use `llama-2-70b` for open-source requirements

2. **Cost Optimization**: 
   - Monitor usage in OpenRouter dashboard
   - Use cheaper models for simple tasks
   - Implement caching for repeated requests

3. **Error Handling**: Always implement proper error handling
   ```python
   try:
       response = await api_manager.generate_text(...)
   except APIError as e:
       if e.retryable:
           # Retry with exponential backoff
           pass
       else:
           # Handle non-retryable error
           pass
   ```

4. **Security**: 
   - Never commit API keys to version control
   - Use environment variables or secure storage
   - Rotate keys regularly

## Support

- **OpenRouter Documentation**: [https://openrouter.ai/docs](https://openrouter.ai/docs)
- **OpenRouter Discord**: [https://discord.gg/openrouter](https://discord.gg/openrouter)
- **Aetherforge Issues**: Create an issue in the Aetherforge repository

## Migration from Direct APIs

If you're currently using OpenAI or Anthropic directly, migrating to OpenRouter is simple:

1. Get an OpenRouter API key
2. Configure it in Aetherforge
3. Update model names to OpenRouter format:
   - `gpt-4` → `openai/gpt-4`
   - `claude-3-sonnet-20240229` → `anthropic/claude-3-sonnet`
4. Test your integration
5. Optionally remove direct provider keys

OpenRouter maintains compatibility with OpenAI's API format, making migration seamless.
