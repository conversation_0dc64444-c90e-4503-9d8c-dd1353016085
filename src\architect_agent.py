"""
Comprehensive Architect Agent for Aetherforge
Designs system architecture, selects appropriate technologies, and creates detailed 
technical specifications based on the analyst's output.
"""

import asyncio
import aiohttp
import json
import logging
import os
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

# Import analyst components for integration
try:
    from src.analyst_agent import ProjectSpecification, MCPRAGClient
    from src.analyst_config import AnalystIntegration
except ImportError:
    from analyst_agent import ProjectSpecification, MCPRAGClient
    from analyst_config import AnalystIntegration

logger = logging.getLogger(__name__)

class ArchitecturePattern(Enum):
    """Supported architecture patterns"""
    LAYERED = "layered"
    MICROSERVICES = "microservices"
    EVENT_DRIVEN = "event_driven"
    HEXAGONAL = "hexagonal"
    CLEAN = "clean_architecture"
    MVC = "model_view_controller"
    SERVERLESS = "serverless"

class ScalabilityTier(Enum):
    """Scalability requirements tiers"""
    SMALL = "small"      # < 1K users
    MEDIUM = "medium"    # 1K - 100K users
    LARGE = "large"      # 100K - 1M users
    ENTERPRISE = "enterprise"  # > 1M users

@dataclass
class TechnologyChoice:
    """Represents a technology choice with justification"""
    name: str
    version: str
    category: str
    justification: str
    alternatives: List[str]
    pros: List[str]
    cons: List[str]
    learning_curve: str  # "low", "medium", "high"
    community_support: str  # "excellent", "good", "fair", "poor"

@dataclass
class ComponentSpecification:
    """Detailed specification for a system component"""
    name: str
    description: str
    responsibilities: List[str]
    technologies: List[TechnologyChoice]
    interfaces: List[Dict[str, Any]]
    dependencies: List[str]
    scalability_requirements: Dict[str, Any]
    security_requirements: List[str]
    performance_requirements: Dict[str, Any]

@dataclass
class SystemArchitecture:
    """Complete system architecture specification"""
    project_name: str
    architecture_pattern: ArchitecturePattern
    scalability_tier: ScalabilityTier
    components: List[ComponentSpecification]
    data_architecture: Dict[str, Any]
    security_architecture: Dict[str, Any]
    deployment_architecture: Dict[str, Any]
    integration_patterns: List[Dict[str, Any]]
    technology_stack: Dict[str, List[TechnologyChoice]]
    quality_attributes: Dict[str, Any]
    constraints: List[str]
    assumptions: List[str]
    risks: List[Dict[str, Any]]

class ArchitectAgent:
    """Comprehensive architect agent for system design and technology selection"""

    def __init__(self, mcp_url: str = None, api_manager=None):
        self.mcp_client = MCPRAGClient(mcp_url)

        # Use provided API manager or create new one
        if api_manager:
            self.api_manager = api_manager
        else:
            try:
                from .api_manager import APIManager
            except ImportError:
                from api_manager import APIManager
            self.api_manager = APIManager()
        
        # Architecture pattern templates
        self.pattern_templates = {
            ArchitecturePattern.LAYERED: {
                "description": "Traditional layered architecture with clear separation of concerns",
                "layers": ["Presentation", "Business", "Data", "Infrastructure"],
                "best_for": ["Traditional web applications", "CRUD applications", "Monolithic systems"],
                "scalability": "Medium",
                "complexity": "Low"
            },
            ArchitecturePattern.MICROSERVICES: {
                "description": "Distributed architecture with independently deployable services",
                "components": ["API Gateway", "Service Registry", "Config Server", "Circuit Breaker"],
                "best_for": ["Large scale applications", "Team autonomy", "Technology diversity"],
                "scalability": "High",
                "complexity": "High"
            },
            ArchitecturePattern.EVENT_DRIVEN: {
                "description": "Architecture based on event production, detection, and consumption",
                "components": ["Event Bus", "Event Store", "Event Processors", "Saga Orchestrator"],
                "best_for": ["Real-time systems", "Loose coupling", "Asynchronous processing"],
                "scalability": "High",
                "complexity": "Medium"
            },
            ArchitecturePattern.HEXAGONAL: {
                "description": "Ports and adapters architecture for testability and flexibility",
                "components": ["Core Domain", "Ports", "Adapters", "Infrastructure"],
                "best_for": ["Domain-driven design", "Testing", "Technology independence"],
                "scalability": "Medium",
                "complexity": "Medium"
            }
        }
        
        # Technology selection criteria
        self.technology_criteria = {
            "performance": {"weight": 0.25, "factors": ["throughput", "latency", "resource_usage"]},
            "scalability": {"weight": 0.20, "factors": ["horizontal_scaling", "vertical_scaling", "load_handling"]},
            "maintainability": {"weight": 0.20, "factors": ["code_quality", "documentation", "community"]},
            "security": {"weight": 0.15, "factors": ["vulnerability_history", "security_features", "compliance"]},
            "cost": {"weight": 0.10, "factors": ["licensing", "infrastructure", "development_time"]},
            "team_expertise": {"weight": 0.10, "factors": ["learning_curve", "existing_knowledge", "training_needs"]}
        }
    
    async def design_architecture(self, analyst_output: Dict[str, Any]) -> SystemArchitecture:
        """Main method to design system architecture based on analyst output"""
        logger.info(f"Starting architecture design for: {analyst_output.get('project_name', 'Unknown Project')}")
        
        async with self.mcp_client:
            # Step 1: Analyze requirements and constraints
            requirements_analysis = await self._analyze_requirements(analyst_output)
            
            # Step 2: Select architecture pattern
            architecture_pattern = await self._select_architecture_pattern(analyst_output, requirements_analysis)
            
            # Step 3: Determine scalability tier
            scalability_tier = await self._determine_scalability_tier(analyst_output)
            
            # Step 4: Design system components
            components = await self._design_system_components(analyst_output, architecture_pattern, scalability_tier)
            
            # Step 5: Select and justify technologies
            technology_stack = await self._select_technologies(analyst_output, components, requirements_analysis)
            
            # Step 6: Design data architecture
            data_architecture = await self._design_data_architecture(analyst_output, components, technology_stack)
            
            # Step 7: Design security architecture
            security_architecture = await self._design_security_architecture(analyst_output, components)
            
            # Step 8: Design deployment architecture
            deployment_architecture = await self._design_deployment_architecture(analyst_output, scalability_tier, technology_stack)
            
            # Step 9: Define integration patterns
            integration_patterns = await self._define_integration_patterns(analyst_output, architecture_pattern, components)
            
            # Step 10: Define quality attributes
            quality_attributes = await self._define_quality_attributes(analyst_output, requirements_analysis)
            
            # Step 11: Identify risks and constraints
            constraints, assumptions, risks = await self._identify_architecture_risks(analyst_output, technology_stack)
            
            return SystemArchitecture(
                project_name=analyst_output.get('project_name', 'Unknown Project'),
                architecture_pattern=architecture_pattern,
                scalability_tier=scalability_tier,
                components=components,
                data_architecture=data_architecture,
                security_architecture=security_architecture,
                deployment_architecture=deployment_architecture,
                integration_patterns=integration_patterns,
                technology_stack=technology_stack,
                quality_attributes=quality_attributes,
                constraints=constraints,
                assumptions=assumptions,
                risks=risks
            )
    
    async def _analyze_requirements(self, analyst_output: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze requirements to inform architecture decisions"""
        functional_reqs = analyst_output.get('functional_requirements', [])
        non_functional_reqs = analyst_output.get('non_functional_requirements', [])
        user_stories = analyst_output.get('user_stories', [])
        
        # Analyze complexity indicators
        complexity_indicators = {
            "user_story_count": len(user_stories),
            "functional_requirement_count": len(functional_reqs),
            "has_real_time_features": any("real-time" in str(story).lower() for story in user_stories),
            "has_payment_processing": any("payment" in str(story).lower() for story in user_stories),
            "has_file_upload": any("upload" in str(story).lower() for story in user_stories),
            "has_notifications": any("notification" in str(story).lower() for story in user_stories),
            "has_analytics": any("analytics" in str(story).lower() for story in user_stories),
            "has_admin_features": any("admin" in str(story).lower() for story in user_stories)
        }
        
        # Determine complexity level
        complexity_score = sum([
            complexity_indicators["user_story_count"] * 0.1,
            complexity_indicators["functional_requirement_count"] * 0.2,
            complexity_indicators["has_real_time_features"] * 2,
            complexity_indicators["has_payment_processing"] * 3,
            complexity_indicators["has_file_upload"] * 1,
            complexity_indicators["has_notifications"] * 1,
            complexity_indicators["has_analytics"] * 2,
            complexity_indicators["has_admin_features"] * 1
        ])
        
        if complexity_score < 5:
            complexity_level = "simple"
        elif complexity_score < 15:
            complexity_level = "moderate"
        elif complexity_score < 25:
            complexity_level = "complex"
        else:
            complexity_level = "enterprise"
        
        return {
            "complexity_indicators": complexity_indicators,
            "complexity_score": complexity_score,
            "complexity_level": complexity_level,
            "functional_requirements": functional_reqs,
            "non_functional_requirements": non_functional_reqs,
            "user_stories": user_stories
        }
    
    async def _select_architecture_pattern(self, analyst_output: Dict[str, Any], 
                                         requirements_analysis: Dict[str, Any]) -> ArchitecturePattern:
        """Select the most appropriate architecture pattern"""
        complexity_level = requirements_analysis["complexity_level"]
        complexity_indicators = requirements_analysis["complexity_indicators"]
        
        # Pattern selection logic based on requirements
        if complexity_level == "enterprise" or complexity_indicators["user_story_count"] > 50:
            return ArchitecturePattern.MICROSERVICES
        elif complexity_indicators["has_real_time_features"]:
            return ArchitecturePattern.EVENT_DRIVEN
        elif complexity_level == "complex":
            return ArchitecturePattern.HEXAGONAL
        else:
            return ArchitecturePattern.LAYERED
    
    async def _determine_scalability_tier(self, analyst_output: Dict[str, Any]) -> ScalabilityTier:
        """Determine the appropriate scalability tier"""
        success_metrics = analyst_output.get('success_metrics', [])
        description = analyst_output.get('description', '').lower()
        
        # Look for scalability indicators in success metrics and description
        if any("million" in str(metric).lower() for metric in success_metrics):
            return ScalabilityTier.ENTERPRISE
        elif any("thousand" in str(metric).lower() for metric in success_metrics):
            return ScalabilityTier.LARGE
        elif "enterprise" in description or "large scale" in description:
            return ScalabilityTier.ENTERPRISE
        elif "startup" in description or "mvp" in description:
            return ScalabilityTier.SMALL
        else:
            return ScalabilityTier.MEDIUM

    async def _design_system_components(self, analyst_output: Dict[str, Any],
                                      architecture_pattern: ArchitecturePattern,
                                      scalability_tier: ScalabilityTier) -> List[ComponentSpecification]:
        """Design detailed system components based on architecture pattern"""
        components = []

        if architecture_pattern == ArchitecturePattern.LAYERED:
            components = await self._design_layered_components(analyst_output, scalability_tier)
        elif architecture_pattern == ArchitecturePattern.MICROSERVICES:
            components = await self._design_microservice_components(analyst_output, scalability_tier)
        elif architecture_pattern == ArchitecturePattern.EVENT_DRIVEN:
            components = await self._design_event_driven_components(analyst_output, scalability_tier)
        elif architecture_pattern == ArchitecturePattern.HEXAGONAL:
            components = await self._design_hexagonal_components(analyst_output, scalability_tier)

        return components

    async def _design_layered_components(self, analyst_output: Dict[str, Any],
                                       scalability_tier: ScalabilityTier) -> List[ComponentSpecification]:
        """Design components for layered architecture"""
        components = []

        # Presentation Layer
        presentation_layer = ComponentSpecification(
            name="Presentation Layer",
            description="Handles user interface and user experience",
            responsibilities=[
                "User interface rendering",
                "User input handling",
                "Client-side validation",
                "State management",
                "Routing and navigation",
                "Responsive design implementation"
            ],
            technologies=[],  # Will be filled by technology selection
            interfaces=[
                {
                    "name": "HTTP API Interface",
                    "type": "REST",
                    "description": "Communication with business layer via HTTP APIs",
                    "protocols": ["HTTP/HTTPS", "WebSocket"]
                }
            ],
            dependencies=["Business Layer"],
            scalability_requirements={
                "concurrent_users": self._get_user_capacity(scalability_tier),
                "response_time": "< 2 seconds",
                "availability": "99.9%"
            },
            security_requirements=[
                "Input sanitization",
                "XSS protection",
                "CSRF protection",
                "Content Security Policy",
                "Secure authentication flow"
            ],
            performance_requirements={
                "page_load_time": "< 3 seconds",
                "bundle_size": "< 1MB",
                "lighthouse_score": "> 90"
            }
        )
        components.append(presentation_layer)

        # Business Layer
        business_layer = ComponentSpecification(
            name="Business Layer",
            description="Contains business logic and application services",
            responsibilities=[
                "Business rule enforcement",
                "Data validation and processing",
                "API endpoint management",
                "Authentication and authorization",
                "Business workflow orchestration",
                "External service integration"
            ],
            technologies=[],
            interfaces=[
                {
                    "name": "REST API",
                    "type": "HTTP",
                    "description": "RESTful API endpoints for client communication",
                    "specifications": ["OpenAPI 3.0", "JSON Schema"]
                },
                {
                    "name": "Database Interface",
                    "type": "ORM",
                    "description": "Data access layer interface",
                    "patterns": ["Repository Pattern", "Unit of Work"]
                }
            ],
            dependencies=["Data Layer", "Infrastructure Layer"],
            scalability_requirements={
                "throughput": f"{self._get_throughput_requirement(scalability_tier)} requests/second",
                "response_time": "< 500ms",
                "availability": "99.95%"
            },
            security_requirements=[
                "JWT token validation",
                "Role-based access control",
                "API rate limiting",
                "Input validation",
                "Audit logging"
            ],
            performance_requirements={
                "api_response_time": "< 500ms",
                "database_query_time": "< 100ms",
                "memory_usage": "< 512MB per instance"
            }
        )
        components.append(business_layer)

        # Data Layer
        data_layer = ComponentSpecification(
            name="Data Layer",
            description="Manages data persistence and retrieval",
            responsibilities=[
                "Data persistence",
                "Data integrity enforcement",
                "Query optimization",
                "Transaction management",
                "Data backup and recovery",
                "Cache management"
            ],
            technologies=[],
            interfaces=[
                {
                    "name": "Database Connection",
                    "type": "SQL",
                    "description": "Primary database connection interface",
                    "features": ["Connection pooling", "Transaction support"]
                },
                {
                    "name": "Cache Interface",
                    "type": "Key-Value",
                    "description": "Caching layer for performance optimization",
                    "patterns": ["Cache-aside", "Write-through"]
                }
            ],
            dependencies=["Infrastructure Layer"],
            scalability_requirements={
                "concurrent_connections": self._get_db_connections(scalability_tier),
                "query_performance": "< 100ms for 95% of queries",
                "storage_capacity": f"{self._get_storage_requirement(scalability_tier)}GB"
            },
            security_requirements=[
                "Data encryption at rest",
                "Data encryption in transit",
                "Database access control",
                "SQL injection prevention",
                "Data anonymization for non-production"
            ],
            performance_requirements={
                "read_latency": "< 50ms",
                "write_latency": "< 100ms",
                "backup_window": "< 4 hours",
                "recovery_time": "< 1 hour"
            }
        )
        components.append(data_layer)

        # Infrastructure Layer
        infrastructure_layer = ComponentSpecification(
            name="Infrastructure Layer",
            description="Provides cross-cutting concerns and system services",
            responsibilities=[
                "Logging and monitoring",
                "Configuration management",
                "Error handling",
                "Health checks",
                "Metrics collection",
                "External service clients"
            ],
            technologies=[],
            interfaces=[
                {
                    "name": "Monitoring Interface",
                    "type": "Metrics",
                    "description": "System metrics and health monitoring",
                    "protocols": ["Prometheus", "StatsD"]
                },
                {
                    "name": "Logging Interface",
                    "type": "Structured Logging",
                    "description": "Centralized logging system",
                    "formats": ["JSON", "ELK Stack compatible"]
                }
            ],
            dependencies=[],
            scalability_requirements={
                "log_throughput": f"{self._get_log_throughput(scalability_tier)} logs/second",
                "metric_collection": "Real-time",
                "alert_response": "< 5 minutes"
            },
            security_requirements=[
                "Secure log transmission",
                "Log data encryption",
                "Access control for monitoring",
                "Audit trail maintenance"
            ],
            performance_requirements={
                "monitoring_overhead": "< 5% CPU",
                "log_processing_delay": "< 10 seconds",
                "metric_accuracy": "99.9%"
            }
        )
        components.append(infrastructure_layer)

        return components

    def _get_user_capacity(self, tier: ScalabilityTier) -> str:
        """Get user capacity based on scalability tier"""
        capacity_map = {
            ScalabilityTier.SMALL: "100-1,000",
            ScalabilityTier.MEDIUM: "1,000-10,000",
            ScalabilityTier.LARGE: "10,000-100,000",
            ScalabilityTier.ENTERPRISE: "100,000+"
        }
        return capacity_map[tier]

    def _get_throughput_requirement(self, tier: ScalabilityTier) -> str:
        """Get throughput requirement based on scalability tier"""
        throughput_map = {
            ScalabilityTier.SMALL: "100",
            ScalabilityTier.MEDIUM: "1,000",
            ScalabilityTier.LARGE: "10,000",
            ScalabilityTier.ENTERPRISE: "50,000"
        }
        return throughput_map[tier]

    def _get_db_connections(self, tier: ScalabilityTier) -> str:
        """Get database connection requirement based on scalability tier"""
        connection_map = {
            ScalabilityTier.SMALL: "10-50",
            ScalabilityTier.MEDIUM: "50-200",
            ScalabilityTier.LARGE: "200-1,000",
            ScalabilityTier.ENTERPRISE: "1,000+"
        }
        return connection_map[tier]

    def _get_storage_requirement(self, tier: ScalabilityTier) -> str:
        """Get storage requirement based on scalability tier"""
        storage_map = {
            ScalabilityTier.SMALL: "10",
            ScalabilityTier.MEDIUM: "100",
            ScalabilityTier.LARGE: "1,000",
            ScalabilityTier.ENTERPRISE: "10,000"
        }
        return storage_map[tier]

    def _get_log_throughput(self, tier: ScalabilityTier) -> str:
        """Get log throughput requirement based on scalability tier"""
        log_map = {
            ScalabilityTier.SMALL: "1,000",
            ScalabilityTier.MEDIUM: "10,000",
            ScalabilityTier.LARGE: "100,000",
            ScalabilityTier.ENTERPRISE: "1,000,000"
        }
        return log_map[tier]

    async def _design_microservice_components(self, analyst_output: Dict[str, Any],
                                            scalability_tier: ScalabilityTier) -> List[ComponentSpecification]:
        """Design components for microservices architecture"""
        components = []

        # API Gateway
        api_gateway = ComponentSpecification(
            name="API Gateway",
            description="Central entry point for all client requests with routing and cross-cutting concerns",
            responsibilities=[
                "Request routing and load balancing",
                "Authentication and authorization",
                "Rate limiting and throttling",
                "Request/response transformation",
                "API versioning and documentation",
                "Monitoring and logging"
            ],
            technologies=[],
            interfaces=[
                {
                    "name": "Client Interface",
                    "type": "HTTP/REST",
                    "description": "External API interface for client applications",
                    "protocols": ["HTTP/HTTPS", "WebSocket"]
                },
                {
                    "name": "Service Discovery",
                    "type": "Service Registry",
                    "description": "Dynamic service discovery and health checking",
                    "protocols": ["HTTP", "gRPC"]
                }
            ],
            dependencies=["Service Registry", "Authentication Service"],
            scalability_requirements={
                "concurrent_requests": f"{self._get_throughput_requirement(scalability_tier)} requests/second",
                "response_time": "< 100ms routing overhead",
                "availability": "99.99%"
            },
            security_requirements=[
                "TLS termination",
                "JWT token validation",
                "Rate limiting per client",
                "DDoS protection",
                "API key management"
            ],
            performance_requirements={
                "routing_latency": "< 50ms",
                "throughput": f"{self._get_throughput_requirement(scalability_tier)} RPS",
                "memory_usage": "< 256MB per instance"
            }
        )
        components.append(api_gateway)

        # User Service
        user_service = ComponentSpecification(
            name="User Service",
            description="Microservice responsible for user management and authentication",
            responsibilities=[
                "User registration and profile management",
                "Authentication and session management",
                "User preferences and settings",
                "User data validation",
                "Password management and recovery",
                "User activity tracking"
            ],
            technologies=[],
            interfaces=[
                {
                    "name": "User API",
                    "type": "REST",
                    "description": "RESTful API for user operations",
                    "protocols": ["HTTP/HTTPS"]
                },
                {
                    "name": "Authentication Events",
                    "type": "Event Stream",
                    "description": "User authentication and activity events",
                    "protocols": ["Message Queue"]
                }
            ],
            dependencies=["Database", "Message Queue"],
            scalability_requirements={
                "concurrent_users": self._get_user_capacity(scalability_tier),
                "response_time": "< 200ms",
                "availability": "99.95%"
            },
            security_requirements=[
                "Password hashing and salting",
                "JWT token generation",
                "PII data encryption",
                "Audit logging",
                "Input validation"
            ],
            performance_requirements={
                "authentication_time": "< 100ms",
                "database_queries": "< 50ms",
                "memory_usage": "< 512MB per instance"
            }
        )
        components.append(user_service)

        # Business Logic Service
        business_service = ComponentSpecification(
            name="Business Logic Service",
            description="Core business logic and domain-specific operations",
            responsibilities=[
                "Business rule enforcement",
                "Domain entity management",
                "Workflow orchestration",
                "Data validation and processing",
                "Business event generation",
                "Integration with external services"
            ],
            technologies=[],
            interfaces=[
                {
                    "name": "Business API",
                    "type": "REST",
                    "description": "Business operations API",
                    "protocols": ["HTTP/HTTPS"]
                },
                {
                    "name": "Event Publisher",
                    "type": "Event Stream",
                    "description": "Business event publishing",
                    "protocols": ["Message Queue"]
                }
            ],
            dependencies=["Database", "Message Queue", "User Service"],
            scalability_requirements={
                "throughput": f"{self._get_throughput_requirement(scalability_tier)} operations/second",
                "response_time": "< 500ms",
                "availability": "99.9%"
            },
            security_requirements=[
                "Authorization checks",
                "Data encryption",
                "Audit logging",
                "Input sanitization",
                "Business rule validation"
            ],
            performance_requirements={
                "processing_time": "< 300ms",
                "database_operations": "< 100ms",
                "memory_usage": "< 1GB per instance"
            }
        )
        components.append(business_service)

        return components

    async def _design_event_driven_components(self, analyst_output: Dict[str, Any],
                                            scalability_tier: ScalabilityTier) -> List[ComponentSpecification]:
        """Design components for event-driven architecture"""
        components = []

        # Event Bus
        event_bus = ComponentSpecification(
            name="Event Bus",
            description="Central event distribution and routing system",
            responsibilities=[
                "Event routing and distribution",
                "Event persistence and replay",
                "Dead letter queue management",
                "Event schema validation",
                "Event ordering and partitioning",
                "Monitoring and metrics"
            ],
            technologies=[],
            interfaces=[
                {
                    "name": "Event Publisher Interface",
                    "type": "Message Queue",
                    "description": "Interface for publishing events",
                    "protocols": ["AMQP", "Apache Kafka"]
                },
                {
                    "name": "Event Subscriber Interface",
                    "type": "Message Queue",
                    "description": "Interface for consuming events",
                    "protocols": ["AMQP", "Apache Kafka"]
                }
            ],
            dependencies=["Message Broker"],
            scalability_requirements={
                "event_throughput": f"{self._get_log_throughput(scalability_tier)} events/second",
                "latency": "< 10ms",
                "availability": "99.99%"
            },
            security_requirements=[
                "Event encryption",
                "Access control",
                "Audit logging",
                "Message authentication",
                "Schema validation"
            ],
            performance_requirements={
                "message_latency": "< 5ms",
                "throughput": f"{self._get_log_throughput(scalability_tier)} messages/second",
                "storage": "Persistent with configurable retention"
            }
        )
        components.append(event_bus)

        # Event Processors
        event_processor = ComponentSpecification(
            name="Event Processors",
            description="Stateless event processing components",
            responsibilities=[
                "Event consumption and processing",
                "Business logic execution",
                "State updates",
                "Event transformation",
                "Error handling and retry",
                "Metrics and monitoring"
            ],
            technologies=[],
            interfaces=[
                {
                    "name": "Event Consumer",
                    "type": "Message Queue",
                    "description": "Consumes events from event bus",
                    "protocols": ["AMQP", "Apache Kafka"]
                },
                {
                    "name": "State Store",
                    "type": "Database",
                    "description": "Persistent state storage",
                    "protocols": ["SQL", "NoSQL"]
                }
            ],
            dependencies=["Event Bus", "Database"],
            scalability_requirements={
                "processing_rate": f"{self._get_throughput_requirement(scalability_tier)} events/second",
                "response_time": "< 100ms per event",
                "availability": "99.9%"
            },
            security_requirements=[
                "Event validation",
                "Access control",
                "Audit logging",
                "Data encryption",
                "Error handling"
            ],
            performance_requirements={
                "processing_latency": "< 50ms",
                "memory_usage": "< 512MB per processor",
                "cpu_usage": "< 70% under normal load"
            }
        )
        components.append(event_processor)

        return components

    async def _design_hexagonal_components(self, analyst_output: Dict[str, Any],
                                         scalability_tier: ScalabilityTier) -> List[ComponentSpecification]:
        """Design components for hexagonal (ports and adapters) architecture"""
        components = []

        # Domain Core
        domain_core = ComponentSpecification(
            name="Domain Core",
            description="Pure business logic without external dependencies",
            responsibilities=[
                "Business rule implementation",
                "Domain entity management",
                "Business invariant enforcement",
                "Domain event generation",
                "Use case orchestration",
                "Business validation"
            ],
            technologies=[],
            interfaces=[
                {
                    "name": "Domain Ports",
                    "type": "Interface",
                    "description": "Abstract interfaces for external dependencies",
                    "protocols": ["In-process calls"]
                }
            ],
            dependencies=[],  # No external dependencies
            scalability_requirements={
                "processing_speed": "In-memory operations",
                "response_time": "< 10ms",
                "availability": "99.99%"
            },
            security_requirements=[
                "Business rule validation",
                "Input sanitization",
                "Domain invariant enforcement",
                "Audit event generation"
            ],
            performance_requirements={
                "execution_time": "< 5ms",
                "memory_usage": "< 100MB",
                "cpu_efficiency": "Optimized algorithms"
            }
        )
        components.append(domain_core)

        # Adapters
        adapters = ComponentSpecification(
            name="Infrastructure Adapters",
            description="Concrete implementations of domain ports",
            responsibilities=[
                "Database access implementation",
                "External service integration",
                "Message queue handling",
                "File system operations",
                "Caching implementation",
                "Monitoring and logging"
            ],
            technologies=[],
            interfaces=[
                {
                    "name": "Port Implementations",
                    "type": "Interface Implementation",
                    "description": "Concrete implementations of domain ports",
                    "protocols": ["Various"]
                }
            ],
            dependencies=["Database", "External Services", "Message Queue"],
            scalability_requirements={
                "adapter_performance": "Optimized for specific technology",
                "response_time": "< 100ms",
                "availability": "99.9%"
            },
            security_requirements=[
                "Connection security",
                "Data encryption",
                "Access control",
                "Error handling",
                "Audit logging"
            ],
            performance_requirements={
                "operation_latency": "< 50ms",
                "connection_pooling": "Efficient resource usage",
                "caching": "Optimized data access"
            }
        )
        components.append(adapters)

        return components

    async def _select_technologies(self, analyst_output: Dict[str, Any],
                                 components: List[ComponentSpecification],
                                 requirements_analysis: Dict[str, Any]) -> Dict[str, List[TechnologyChoice]]:
        """Select and justify technology choices for each component"""
        technology_stack = {
            "frontend": [],
            "backend": [],
            "database": [],
            "infrastructure": [],
            "security": [],
            "testing": []
        }

        # Get existing preferences from analyst
        existing_stack = analyst_output.get('technical_preferences', {})
        complexity_level = requirements_analysis["complexity_level"]

        # Frontend Technologies
        frontend_choices = await self._select_frontend_technologies(existing_stack, complexity_level)
        technology_stack["frontend"] = frontend_choices

        # Backend Technologies
        backend_choices = await self._select_backend_technologies(existing_stack, complexity_level)
        technology_stack["backend"] = backend_choices

        # Database Technologies
        database_choices = await self._select_database_technologies(existing_stack, complexity_level, requirements_analysis)
        technology_stack["database"] = database_choices

        # Infrastructure Technologies
        infrastructure_choices = await self._select_infrastructure_technologies(complexity_level)
        technology_stack["infrastructure"] = infrastructure_choices

        # Security Technologies
        security_choices = await self._select_security_technologies(complexity_level, requirements_analysis)
        technology_stack["security"] = security_choices

        # Testing Technologies
        testing_choices = await self._select_testing_technologies(existing_stack, complexity_level)
        technology_stack["testing"] = testing_choices

        # Update component technologies
        await self._assign_technologies_to_components(components, technology_stack)

        return technology_stack

    async def _select_frontend_technologies(self, existing_stack: Dict[str, Any],
                                          complexity_level: str) -> List[TechnologyChoice]:
        """Select frontend technologies with detailed justification"""
        choices = []

        # Framework selection
        existing_frontend = existing_stack.get('frontend', {})
        framework = existing_frontend.get('framework', 'React')

        if framework == 'React':
            react_choice = TechnologyChoice(
                name="React",
                version="18.x",
                category="Frontend Framework",
                justification="Mature ecosystem, excellent performance with hooks and concurrent features, strong TypeScript support",
                alternatives=["Vue.js", "Angular", "Svelte"],
                pros=[
                    "Large ecosystem and community",
                    "Excellent performance with virtual DOM",
                    "Strong TypeScript integration",
                    "Extensive third-party library support",
                    "Good testing tools and practices"
                ],
                cons=[
                    "Steep learning curve for beginners",
                    "Rapid ecosystem changes",
                    "Bundle size can be large"
                ],
                learning_curve="medium",
                community_support="excellent"
            )
            choices.append(react_choice)

        # State Management
        if complexity_level in ["complex", "enterprise"]:
            redux_choice = TechnologyChoice(
                name="Redux Toolkit",
                version="1.9.x",
                category="State Management",
                justification="Predictable state management for complex applications with time-travel debugging",
                alternatives=["Zustand", "Jotai", "Context API"],
                pros=[
                    "Predictable state updates",
                    "Excellent debugging tools",
                    "Time-travel debugging",
                    "Middleware ecosystem"
                ],
                cons=[
                    "Boilerplate code",
                    "Learning curve",
                    "Overkill for simple apps"
                ],
                learning_curve="high",
                community_support="excellent"
            )
            choices.append(redux_choice)
        else:
            context_choice = TechnologyChoice(
                name="React Context + useReducer",
                version="Built-in",
                category="State Management",
                justification="Built-in React state management sufficient for moderate complexity",
                alternatives=["Redux", "Zustand", "Jotai"],
                pros=[
                    "No additional dependencies",
                    "Simple to understand",
                    "Good for moderate complexity"
                ],
                cons=[
                    "Can cause unnecessary re-renders",
                    "Not suitable for complex state"
                ],
                learning_curve="low",
                community_support="excellent"
            )
            choices.append(context_choice)

        # Styling
        styling_choice = TechnologyChoice(
            name="Tailwind CSS",
            version="3.x",
            category="CSS Framework",
            justification="Utility-first CSS framework for rapid UI development with excellent customization",
            alternatives=["Styled Components", "Emotion", "CSS Modules"],
            pros=[
                "Rapid development",
                "Consistent design system",
                "Small production bundle",
                "Excellent customization"
            ],
            cons=[
                "Learning curve for utility classes",
                "Can lead to verbose HTML"
            ],
            learning_curve="medium",
            community_support="excellent"
        )
        choices.append(styling_choice)

        # Build Tool
        build_choice = TechnologyChoice(
            name="Vite",
            version="4.x",
            category="Build Tool",
            justification="Fast build tool with excellent development experience and modern features",
            alternatives=["Webpack", "Parcel", "Rollup"],
            pros=[
                "Extremely fast development server",
                "Fast builds",
                "Modern ES modules support",
                "Excellent plugin ecosystem"
            ],
            cons=[
                "Relatively new ecosystem",
                "Some legacy compatibility issues"
            ],
            learning_curve="low",
            community_support="good"
        )
        choices.append(build_choice)

        return choices

    async def _select_backend_technologies(self, existing_stack: Dict[str, Any],
                                         complexity_level: str) -> List[TechnologyChoice]:
        """Select backend technologies with detailed justification"""
        choices = []

        # Runtime
        runtime_choice = TechnologyChoice(
            name="Node.js",
            version="18.x LTS",
            category="Runtime",
            justification="JavaScript runtime with excellent performance, large ecosystem, and unified language stack",
            alternatives=["Python", "Java", "Go", ".NET"],
            pros=[
                "Unified language with frontend",
                "Excellent performance for I/O operations",
                "Large package ecosystem (npm)",
                "Strong async/await support"
            ],
            cons=[
                "Single-threaded nature",
                "CPU-intensive task limitations",
                "Callback complexity in legacy code"
            ],
            learning_curve="low",
            community_support="excellent"
        )
        choices.append(runtime_choice)

        # Framework
        if complexity_level in ["complex", "enterprise"]:
            nestjs_choice = TechnologyChoice(
                name="NestJS",
                version="9.x",
                category="Backend Framework",
                justification="Enterprise-grade framework with TypeScript, decorators, and modular architecture",
                alternatives=["Express.js", "Fastify", "Koa"],
                pros=[
                    "TypeScript first",
                    "Modular architecture",
                    "Built-in dependency injection",
                    "Excellent testing support",
                    "OpenAPI integration"
                ],
                cons=[
                    "Steeper learning curve",
                    "More opinionated",
                    "Larger bundle size"
                ],
                learning_curve="high",
                community_support="good"
            )
            choices.append(nestjs_choice)
        else:
            express_choice = TechnologyChoice(
                name="Express.js",
                version="4.x",
                category="Backend Framework",
                justification="Minimal and flexible web framework with extensive middleware ecosystem",
                alternatives=["Fastify", "Koa", "NestJS"],
                pros=[
                    "Minimal and flexible",
                    "Large middleware ecosystem",
                    "Easy to learn",
                    "Excellent documentation"
                ],
                cons=[
                    "Requires more setup",
                    "Less opinionated structure",
                    "Manual security configuration"
                ],
                learning_curve="low",
                community_support="excellent"
            )
            choices.append(express_choice)

        # Language
        typescript_choice = TechnologyChoice(
            name="TypeScript",
            version="5.x",
            category="Programming Language",
            justification="Type safety, better IDE support, and improved maintainability for large codebases",
            alternatives=["JavaScript", "Flow"],
            pros=[
                "Static type checking",
                "Better IDE support",
                "Improved refactoring",
                "Enhanced code documentation"
            ],
            cons=[
                "Additional compilation step",
                "Learning curve for type system",
                "Some library compatibility issues"
            ],
            learning_curve="medium",
            community_support="excellent"
        )
        choices.append(typescript_choice)

        return choices

    async def _select_database_technologies(self, existing_stack: Dict[str, Any],
                                          complexity_level: str,
                                          requirements_analysis: Dict[str, Any]) -> List[TechnologyChoice]:
        """Select database technologies with detailed justification"""
        choices = []

        # Primary Database
        postgresql_choice = TechnologyChoice(
            name="PostgreSQL",
            version="15.x",
            category="Primary Database",
            justification="Robust ACID-compliant database with excellent performance, JSON support, and extensibility",
            alternatives=["MySQL", "MongoDB", "SQLite"],
            pros=[
                "ACID compliance",
                "Excellent performance",
                "JSON/JSONB support",
                "Extensible with custom functions",
                "Strong consistency guarantees",
                "Excellent tooling ecosystem"
            ],
            cons=[
                "More complex setup than MySQL",
                "Higher memory usage",
                "Steeper learning curve"
            ],
            learning_curve="medium",
            community_support="excellent"
        )
        choices.append(postgresql_choice)

        # ORM/Query Builder
        if complexity_level in ["complex", "enterprise"]:
            prisma_choice = TechnologyChoice(
                name="Prisma",
                version="4.x",
                category="ORM",
                justification="Type-safe database client with excellent TypeScript integration and migration system",
                alternatives=["TypeORM", "Sequelize", "Knex.js"],
                pros=[
                    "Type-safe database access",
                    "Excellent TypeScript integration",
                    "Auto-generated client",
                    "Built-in migration system",
                    "Introspection capabilities"
                ],
                cons=[
                    "Relatively new ecosystem",
                    "Limited advanced query capabilities",
                    "Vendor lock-in concerns"
                ],
                learning_curve="medium",
                community_support="good"
            )
            choices.append(prisma_choice)

        # Caching
        redis_choice = TechnologyChoice(
            name="Redis",
            version="7.x",
            category="Caching/Session Store",
            justification="High-performance in-memory data store for caching, sessions, and real-time features",
            alternatives=["Memcached", "DragonflyDB", "KeyDB"],
            pros=[
                "Excellent performance",
                "Rich data structures",
                "Pub/Sub capabilities",
                "Persistence options",
                "Clustering support"
            ],
            cons=[
                "Memory-only storage",
                "Single-threaded nature",
                "Complexity in clustering"
            ],
            learning_curve="medium",
            community_support="excellent"
        )
        choices.append(redis_choice)

        return choices

    async def _select_infrastructure_technologies(self, complexity_level: str) -> List[TechnologyChoice]:
        """Select infrastructure technologies"""
        choices = []

        # Containerization
        docker_choice = TechnologyChoice(
            name="Docker",
            version="24.x",
            category="Containerization",
            justification="Industry-standard containerization for consistent deployment across environments",
            alternatives=["Podman", "containerd", "LXC"],
            pros=[
                "Consistent environments",
                "Easy deployment",
                "Resource isolation",
                "Excellent ecosystem",
                "CI/CD integration"
            ],
            cons=[
                "Learning curve",
                "Resource overhead",
                "Security considerations"
            ],
            learning_curve="medium",
            community_support="excellent"
        )
        choices.append(docker_choice)

        # Orchestration
        if complexity_level in ["complex", "enterprise"]:
            kubernetes_choice = TechnologyChoice(
                name="Kubernetes",
                version="1.28.x",
                category="Container Orchestration",
                justification="Production-grade container orchestration for scalable, resilient deployments",
                alternatives=["Docker Swarm", "Nomad", "ECS"],
                pros=[
                    "Auto-scaling capabilities",
                    "Self-healing systems",
                    "Rolling deployments",
                    "Service discovery",
                    "Extensive ecosystem"
                ],
                cons=[
                    "High complexity",
                    "Steep learning curve",
                    "Resource overhead"
                ],
                learning_curve="high",
                community_support="excellent"
            )
            choices.append(kubernetes_choice)
        else:
            compose_choice = TechnologyChoice(
                name="Docker Compose",
                version="2.x",
                category="Container Orchestration",
                justification="Simple multi-container application orchestration for development and small deployments",
                alternatives=["Docker Swarm", "Kubernetes"],
                pros=[
                    "Simple configuration",
                    "Easy local development",
                    "Good for small deployments",
                    "Minimal learning curve"
                ],
                cons=[
                    "Limited scaling capabilities",
                    "Single-host limitation",
                    "Basic health checking"
                ],
                learning_curve="low",
                community_support="good"
            )
            choices.append(compose_choice)

        # Monitoring
        prometheus_choice = TechnologyChoice(
            name="Prometheus",
            version="2.x",
            category="Monitoring",
            justification="Open-source monitoring system with dimensional data model and powerful query language",
            alternatives=["Grafana Cloud", "DataDog", "New Relic"],
            pros=[
                "Powerful query language (PromQL)",
                "Pull-based architecture",
                "Excellent alerting",
                "Service discovery",
                "Large ecosystem"
            ],
            cons=[
                "Storage limitations",
                "Complex setup",
                "Resource intensive"
            ],
            learning_curve="high",
            community_support="excellent"
        )
        choices.append(prometheus_choice)

        return choices

    async def _select_security_technologies(self, complexity_level: str,
                                          requirements_analysis: Dict[str, Any]) -> List[TechnologyChoice]:
        """Select security technologies"""
        choices = []

        # Authentication
        jwt_choice = TechnologyChoice(
            name="JSON Web Tokens (JWT)",
            version="9.x (jsonwebtoken)",
            category="Authentication",
            justification="Stateless authentication tokens for scalable, distributed systems",
            alternatives=["Session-based auth", "OAuth 2.0", "SAML"],
            pros=[
                "Stateless authentication",
                "Cross-domain support",
                "Scalable architecture",
                "Standard format"
            ],
            cons=[
                "Token size",
                "Revocation complexity",
                "Security considerations"
            ],
            learning_curve="medium",
            community_support="excellent"
        )
        choices.append(jwt_choice)

        # Password Hashing
        bcrypt_choice = TechnologyChoice(
            name="bcrypt",
            version="5.x",
            category="Password Security",
            justification="Adaptive hashing function designed for password storage with salt and cost factor",
            alternatives=["Argon2", "scrypt", "PBKDF2"],
            pros=[
                "Adaptive cost factor",
                "Built-in salt generation",
                "Time-tested security",
                "Wide adoption"
            ],
            cons=[
                "CPU intensive",
                "Limited password length",
                "Blocking operations"
            ],
            learning_curve="low",
            community_support="excellent"
        )
        choices.append(bcrypt_choice)

        # Security Headers
        helmet_choice = TechnologyChoice(
            name="Helmet.js",
            version="7.x",
            category="Security Middleware",
            justification="Express middleware for setting security-related HTTP headers",
            alternatives=["Custom middleware", "NGINX configuration"],
            pros=[
                "Easy implementation",
                "Comprehensive security headers",
                "Regular updates",
                "Configurable options"
            ],
            cons=[
                "Express-specific",
                "May break some functionality",
                "Requires configuration"
            ],
            learning_curve="low",
            community_support="good"
        )
        choices.append(helmet_choice)

        return choices

    async def _select_testing_technologies(self, existing_stack: Dict[str, Any],
                                         complexity_level: str) -> List[TechnologyChoice]:
        """Select testing technologies"""
        choices = []

        # Unit Testing
        jest_choice = TechnologyChoice(
            name="Jest",
            version="29.x",
            category="Unit Testing",
            justification="Comprehensive testing framework with built-in mocking, coverage, and snapshot testing",
            alternatives=["Vitest", "Mocha", "Jasmine"],
            pros=[
                "Zero configuration",
                "Built-in mocking",
                "Snapshot testing",
                "Code coverage",
                "Parallel test execution"
            ],
            cons=[
                "Can be slow for large test suites",
                "Memory usage",
                "Limited ES modules support"
            ],
            learning_curve="low",
            community_support="excellent"
        )
        choices.append(jest_choice)

        # Integration Testing
        supertest_choice = TechnologyChoice(
            name="Supertest",
            version="6.x",
            category="API Testing",
            justification="HTTP assertion library for testing Node.js HTTP servers",
            alternatives=["Postman/Newman", "Insomnia", "Artillery"],
            pros=[
                "Simple API testing",
                "Good Express integration",
                "Fluent assertion API",
                "No server startup required"
            ],
            cons=[
                "Limited to HTTP testing",
                "Basic assertion capabilities",
                "No GUI interface"
            ],
            learning_curve="low",
            community_support="good"
        )
        choices.append(supertest_choice)

        return choices

    async def _assign_technologies_to_components(self, components: List[ComponentSpecification],
                                               technology_stack: Dict[str, List[TechnologyChoice]]):
        """Assign selected technologies to appropriate components"""
        for component in components:
            if "Presentation" in component.name:
                component.technologies = technology_stack["frontend"]
            elif "Business" in component.name:
                component.technologies = technology_stack["backend"] + technology_stack["security"]
            elif "Data" in component.name:
                component.technologies = technology_stack["database"]
            elif "Infrastructure" in component.name:
                component.technologies = technology_stack["infrastructure"]

    async def _design_data_architecture(self, analyst_output: Dict[str, Any],
                                      components: List[ComponentSpecification],
                                      technology_stack: Dict[str, List[TechnologyChoice]]) -> Dict[str, Any]:
        """Design comprehensive data architecture"""
        return {
            "data_storage_strategy": {
                "primary_database": "PostgreSQL",
                "caching_layer": "Redis",
                "file_storage": "AWS S3 / Local filesystem",
                "backup_strategy": "Automated daily backups with point-in-time recovery"
            },
            "data_modeling": {
                "approach": "Domain-driven design with normalized relational model",
                "schema_management": "Migration-based with version control",
                "data_validation": "Application-level and database constraints",
                "audit_trail": "Temporal tables for critical data changes"
            },
            "data_flow": {
                "read_patterns": [
                    "Cache-first for frequently accessed data",
                    "Database direct for complex queries",
                    "Read replicas for reporting workloads"
                ],
                "write_patterns": [
                    "Write-through cache for critical data",
                    "Batch processing for analytics",
                    "Event sourcing for audit requirements"
                ]
            },
            "data_security": {
                "encryption_at_rest": "AES-256 encryption for sensitive data",
                "encryption_in_transit": "TLS 1.3 for all connections",
                "access_control": "Role-based access with principle of least privilege",
                "data_masking": "Automated masking for non-production environments"
            },
            "scalability": {
                "horizontal_scaling": "Read replicas and connection pooling",
                "vertical_scaling": "Resource scaling based on performance metrics",
                "partitioning": "Table partitioning for large datasets",
                "archiving": "Automated archiving of historical data"
            }
        }

    async def _design_security_architecture(self, analyst_output: Dict[str, Any],
                                          components: List[ComponentSpecification]) -> Dict[str, Any]:
        """Design comprehensive security architecture"""
        return {
            "authentication_strategy": {
                "method": "JWT-based stateless authentication",
                "token_lifecycle": "Access tokens (15min) + Refresh tokens (7 days)",
                "multi_factor": "TOTP-based 2FA for admin accounts",
                "password_policy": "Minimum 8 characters, complexity requirements"
            },
            "authorization_model": {
                "approach": "Role-Based Access Control (RBAC)",
                "roles": ["Admin", "User", "Guest"],
                "permissions": "Fine-grained resource-based permissions",
                "inheritance": "Hierarchical role inheritance"
            },
            "data_protection": {
                "encryption": {
                    "at_rest": "AES-256 for database and file storage",
                    "in_transit": "TLS 1.3 for all communications",
                    "key_management": "Automated key rotation every 90 days"
                },
                "privacy": {
                    "data_classification": "Public, Internal, Confidential, Restricted",
                    "retention_policy": "Automated deletion based on data classification",
                    "anonymization": "PII anonymization for analytics"
                }
            },
            "application_security": {
                "input_validation": "Comprehensive validation at all entry points",
                "output_encoding": "Context-aware output encoding",
                "csrf_protection": "CSRF tokens for state-changing operations",
                "xss_protection": "Content Security Policy and input sanitization"
            },
            "infrastructure_security": {
                "network_security": "VPC with private subnets and security groups",
                "container_security": "Image scanning and runtime protection",
                "secrets_management": "Encrypted secrets with rotation",
                "monitoring": "Security event logging and alerting"
            },
            "compliance": {
                "standards": ["OWASP Top 10", "GDPR", "SOC 2"],
                "auditing": "Comprehensive audit logging",
                "vulnerability_management": "Regular security scans and penetration testing"
            }
        }

    async def _design_deployment_architecture(self, analyst_output: Dict[str, Any],
                                            scalability_tier: ScalabilityTier,
                                            technology_stack: Dict[str, List[TechnologyChoice]]) -> Dict[str, Any]:
        """Design deployment architecture based on scalability requirements"""

        if scalability_tier in [ScalabilityTier.LARGE, ScalabilityTier.ENTERPRISE]:
            return {
                "deployment_strategy": "Blue-Green deployment with canary releases",
                "infrastructure": {
                    "cloud_provider": "AWS / Azure / GCP",
                    "compute": "Kubernetes cluster with auto-scaling",
                    "networking": "Load balancer with SSL termination",
                    "storage": "Managed database with read replicas"
                },
                "environments": {
                    "development": "Local Docker Compose setup",
                    "staging": "Kubernetes cluster mirroring production",
                    "production": "Multi-AZ Kubernetes with high availability"
                },
                "ci_cd_pipeline": {
                    "source_control": "Git with feature branch workflow",
                    "build": "Automated builds on pull requests",
                    "testing": "Automated unit, integration, and e2e tests",
                    "deployment": "GitOps with ArgoCD or Flux",
                    "monitoring": "Comprehensive observability stack"
                },
                "scalability": {
                    "horizontal_scaling": "Pod auto-scaling based on CPU/memory",
                    "load_balancing": "Application load balancer with health checks",
                    "database_scaling": "Read replicas and connection pooling",
                    "caching": "Distributed Redis cluster"
                }
            }
        else:
            return {
                "deployment_strategy": "Rolling deployment with health checks",
                "infrastructure": {
                    "cloud_provider": "AWS / Vercel / DigitalOcean",
                    "compute": "Container instances or serverless functions",
                    "networking": "CDN with SSL termination",
                    "storage": "Managed database service"
                },
                "environments": {
                    "development": "Local Docker Compose setup",
                    "staging": "Single instance with production data subset",
                    "production": "Container service with auto-scaling"
                },
                "ci_cd_pipeline": {
                    "source_control": "Git with GitHub/GitLab",
                    "build": "GitHub Actions or GitLab CI",
                    "testing": "Automated testing on pull requests",
                    "deployment": "Automated deployment to staging and production",
                    "monitoring": "Basic monitoring and alerting"
                },
                "scalability": {
                    "horizontal_scaling": "Container auto-scaling",
                    "load_balancing": "Application load balancer",
                    "database_scaling": "Vertical scaling with read replicas",
                    "caching": "Single Redis instance"
                }
            }

    async def _define_integration_patterns(self, analyst_output: Dict[str, Any],
                                         architecture_pattern: ArchitecturePattern,
                                         components: List[ComponentSpecification]) -> List[Dict[str, Any]]:
        """Define integration patterns between components"""
        patterns = []

        # API Integration Pattern
        api_pattern = {
            "name": "RESTful API Integration",
            "description": "HTTP-based API communication between frontend and backend",
            "components": ["Presentation Layer", "Business Layer"],
            "protocol": "HTTP/HTTPS",
            "data_format": "JSON",
            "authentication": "JWT Bearer tokens",
            "error_handling": "Standardized error responses with HTTP status codes",
            "versioning": "URL path versioning (e.g., /api/v1/)",
            "documentation": "OpenAPI 3.0 specification"
        }
        patterns.append(api_pattern)

        # Database Integration Pattern
        db_pattern = {
            "name": "Database Access Pattern",
            "description": "ORM-based database access with connection pooling",
            "components": ["Business Layer", "Data Layer"],
            "protocol": "TCP/PostgreSQL Wire Protocol",
            "connection_management": "Connection pooling with automatic failover",
            "transaction_management": "ACID transactions with rollback support",
            "query_optimization": "Query analysis and index optimization",
            "migration_strategy": "Version-controlled schema migrations"
        }
        patterns.append(db_pattern)

        # Caching Integration Pattern
        cache_pattern = {
            "name": "Caching Integration",
            "description": "Redis-based caching for performance optimization",
            "components": ["Business Layer", "Data Layer"],
            "protocol": "Redis Protocol (RESP)",
            "cache_strategies": ["Cache-aside", "Write-through", "Write-behind"],
            "invalidation": "TTL-based and manual invalidation",
            "data_structures": "Strings, Hashes, Sets, Sorted Sets",
            "clustering": "Redis Cluster for high availability"
        }
        patterns.append(cache_pattern)

        # Event-driven patterns for real-time features
        if any("real-time" in str(story).lower() for story in analyst_output.get('user_stories', [])):
            event_pattern = {
                "name": "Event-Driven Integration",
                "description": "WebSocket-based real-time communication",
                "components": ["Presentation Layer", "Business Layer"],
                "protocol": "WebSocket",
                "event_types": ["User actions", "System notifications", "Data updates"],
                "scaling": "Socket.IO with Redis adapter for multi-instance support",
                "fallback": "HTTP polling for unsupported clients"
            }
            patterns.append(event_pattern)

        return patterns

    async def _define_quality_attributes(self, analyst_output: Dict[str, Any],
                                       requirements_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Define quality attributes and their requirements"""
        return {
            "performance": {
                "response_time": "< 500ms for API calls, < 2s for page loads",
                "throughput": f"{self._get_throughput_requirement(ScalabilityTier.MEDIUM)} requests/second",
                "resource_utilization": "< 80% CPU and memory under normal load",
                "scalability": "Horizontal scaling to handle 10x traffic spikes"
            },
            "reliability": {
                "availability": "99.9% uptime (8.76 hours downtime/year)",
                "fault_tolerance": "Graceful degradation during component failures",
                "recovery_time": "< 1 hour for critical system recovery",
                "data_durability": "99.*********% (11 9's) data durability"
            },
            "security": {
                "authentication": "Multi-factor authentication for admin accounts",
                "authorization": "Role-based access control with audit logging",
                "data_protection": "Encryption at rest and in transit",
                "compliance": "GDPR, SOC 2, and OWASP Top 10 compliance"
            },
            "maintainability": {
                "code_quality": "80%+ test coverage, static analysis compliance",
                "documentation": "Comprehensive API and architecture documentation",
                "modularity": "Loosely coupled, highly cohesive components",
                "deployment": "Automated CI/CD with rollback capabilities"
            },
            "usability": {
                "user_experience": "Intuitive interface with < 3 clicks to core features",
                "accessibility": "WCAG 2.1 AA compliance",
                "performance": "< 3 second page load times",
                "mobile_support": "Responsive design for all screen sizes"
            }
        }

    async def _identify_architecture_risks(self, analyst_output: Dict[str, Any],
                                         technology_stack: Dict[str, List[TechnologyChoice]]) -> Tuple[List[str], List[str], List[Dict[str, Any]]]:
        """Identify architecture constraints, assumptions, and risks"""

        constraints = [
            "Development team size and expertise limitations",
            "Budget constraints for cloud infrastructure and third-party services",
            "Timeline constraints for MVP delivery",
            "Compliance requirements (GDPR, security standards)",
            "Technology stack consistency across frontend and backend",
            "Scalability requirements within cost constraints"
        ]

        assumptions = [
            "Development team has TypeScript and React experience",
            "Cloud infrastructure (AWS/Azure/GCP) is available and approved",
            "Database performance requirements can be met with PostgreSQL",
            "Redis caching will provide sufficient performance improvements",
            "Third-party services (payment, email) will maintain SLA commitments",
            "User load will grow gradually, allowing for iterative scaling"
        ]

        risks = [
            {
                "id": "ARCH-001",
                "title": "Technology Learning Curve",
                "description": "Team may need significant time to learn new technologies",
                "probability": "Medium",
                "impact": "Medium",
                "mitigation": "Provide comprehensive training and documentation, start with simpler implementations",
                "category": "Technical"
            },
            {
                "id": "ARCH-002",
                "title": "Scalability Bottlenecks",
                "description": "Database or application layer may not scale as expected",
                "probability": "Medium",
                "impact": "High",
                "mitigation": "Implement monitoring, load testing, and horizontal scaling capabilities",
                "category": "Performance"
            },
            {
                "id": "ARCH-003",
                "title": "Third-party Service Dependencies",
                "description": "External services may become unavailable or change pricing",
                "probability": "Low",
                "impact": "High",
                "mitigation": "Implement circuit breakers, fallback mechanisms, and vendor diversification",
                "category": "Operational"
            },
            {
                "id": "ARCH-004",
                "title": "Security Vulnerabilities",
                "description": "Security flaws in architecture or implementation",
                "probability": "Medium",
                "impact": "High",
                "mitigation": "Regular security audits, penetration testing, and security-first development",
                "category": "Security"
            },
            {
                "id": "ARCH-005",
                "title": "Data Migration Complexity",
                "description": "Database schema changes may be complex and risky",
                "probability": "Medium",
                "impact": "Medium",
                "mitigation": "Implement robust migration testing and rollback procedures",
                "category": "Technical"
            }
        ]

        return constraints, assumptions, risks

    async def generate_architecture_documents(self, architecture: SystemArchitecture,
                                            output_path: Path) -> List[str]:
        """Generate comprehensive architecture documentation"""
        output_path.mkdir(parents=True, exist_ok=True)
        docs_path = output_path / "architecture"
        docs_path.mkdir(exist_ok=True)

        generated_files = []

        # Generate system architecture document
        arch_content = self._generate_system_architecture_document(architecture)
        arch_file = docs_path / "system_architecture.md"
        arch_file.write_text(arch_content, encoding='utf-8')
        generated_files.append("architecture/system_architecture.md")

        # Generate technology selection document
        tech_content = self._generate_technology_selection_document(architecture)
        tech_file = docs_path / "technology_selection.md"
        tech_file.write_text(tech_content, encoding='utf-8')
        generated_files.append("architecture/technology_selection.md")

        # Generate component specifications
        comp_content = self._generate_component_specifications_document(architecture)
        comp_file = docs_path / "component_specifications.md"
        comp_file.write_text(comp_content, encoding='utf-8')
        generated_files.append("architecture/component_specifications.md")

        # Generate deployment architecture
        deploy_content = self._generate_deployment_architecture_document(architecture)
        deploy_file = docs_path / "deployment_architecture.md"
        deploy_file.write_text(deploy_content, encoding='utf-8')
        generated_files.append("architecture/deployment_architecture.md")

        # Generate security architecture
        security_content = self._generate_security_architecture_document(architecture)
        security_file = docs_path / "security_architecture.md"
        security_file.write_text(security_content, encoding='utf-8')
        generated_files.append("architecture/security_architecture.md")

        # Generate data architecture
        data_content = self._generate_data_architecture_document(architecture)
        data_file = docs_path / "data_architecture.md"
        data_file.write_text(data_content, encoding='utf-8')
        generated_files.append("architecture/data_architecture.md")

        # Generate integration patterns
        integration_content = self._generate_integration_patterns_document(architecture)
        integration_file = docs_path / "integration_patterns.md"
        integration_file.write_text(integration_content, encoding='utf-8')
        generated_files.append("architecture/integration_patterns.md")

        # Generate quality attributes
        quality_content = self._generate_quality_attributes_document(architecture)
        quality_file = docs_path / "quality_attributes.md"
        quality_file.write_text(quality_content, encoding='utf-8')
        generated_files.append("architecture/quality_attributes.md")

        # Generate architecture decision records
        adr_content = self._generate_architecture_decisions_document(architecture)
        adr_file = docs_path / "architecture_decisions.md"
        adr_file.write_text(adr_content, encoding='utf-8')
        generated_files.append("architecture/architecture_decisions.md")

        return generated_files

    def _generate_system_architecture_document(self, architecture: SystemArchitecture) -> str:
        """Generate comprehensive system architecture document"""
        return f"""# System Architecture: {architecture.project_name}

Generated: {datetime.now().isoformat()}

## 1. Architecture Overview

### 1.1 Architecture Pattern
**Pattern**: {architecture.architecture_pattern.value.replace('_', ' ').title()}

**Description**: {self.pattern_templates.get(architecture.architecture_pattern, {}).get('description', 'Custom architecture pattern')}

**Scalability Tier**: {architecture.scalability_tier.value.title()}

### 1.2 Quality Attributes

{chr(10).join(f"""#### {attr.title()}
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in details.items())}
""" for attr, details in architecture.quality_attributes.items())}

## 2. System Components

{chr(10).join(f"""### 2.{i+1} {component.name}

**Description**: {component.description}

**Core Responsibilities**:
{chr(10).join(f"- {resp}" for resp in component.responsibilities)}

**Key Technologies**:
{chr(10).join(f"- **{tech.name} {tech.version}** ({tech.category}): {tech.justification}" for tech in component.technologies)}

**Interfaces**:
{chr(10).join(f"- **{interface.get('name', 'Interface')}** ({interface.get('type', 'Unknown')}): {interface.get('description', 'No description')}" for interface in component.interfaces)}

**Dependencies**: {', '.join(component.dependencies) if component.dependencies else 'None'}

**Scalability Requirements**:
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in component.scalability_requirements.items())}

**Security Requirements**:
{chr(10).join(f"- {req}" for req in component.security_requirements)}

**Performance Requirements**:
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in component.performance_requirements.items())}

---
""" for i, component in enumerate(architecture.components))}

## 3. Integration Patterns

{chr(10).join(f"""### 3.{i+1} {pattern.get('name', 'Integration Pattern')}

**Description**: {pattern.get('description', 'No description')}

**Components**: {', '.join(pattern.get('components', []))}

**Protocol**: {pattern.get('protocol', 'Not specified')}

**Key Features**:
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in pattern.items() if key not in ['name', 'description', 'components', 'protocol'])}

---
""" for i, pattern in enumerate(architecture.integration_patterns))}

## 4. Constraints and Assumptions

### 4.1 Constraints
{chr(10).join(f"- {constraint}" for constraint in architecture.constraints)}

### 4.2 Assumptions
{chr(10).join(f"- {assumption}" for assumption in architecture.assumptions)}

## 5. Architecture Risks

{chr(10).join(f"""### {risk.get('id', 'RISK-XXX')}: {risk.get('title', 'Risk')}

**Category**: {risk.get('category', 'Unknown')}
**Probability**: {risk.get('probability', 'Unknown')}
**Impact**: {risk.get('impact', 'Unknown')}

**Description**: {risk.get('description', 'No description')}

**Mitigation**: {risk.get('mitigation', 'No mitigation strategy defined')}

---
""" for risk in architecture.risks)}
"""

    def _generate_technology_selection_document(self, architecture: SystemArchitecture) -> str:
        """Generate detailed technology selection document"""
        return f"""# Technology Selection: {architecture.project_name}

Generated: {datetime.now().isoformat()}

## 1. Technology Selection Criteria

The following criteria were used to evaluate and select technologies:

- **Performance** (25%): Throughput, latency, resource usage
- **Scalability** (20%): Horizontal scaling, vertical scaling, load handling
- **Maintainability** (20%): Code quality, documentation, community support
- **Security** (15%): Vulnerability history, security features, compliance
- **Cost** (10%): Licensing, infrastructure, development time
- **Team Expertise** (10%): Learning curve, existing knowledge, training needs

## 2. Technology Stack

{chr(10).join(f"""### 2.{i+1} {category.title()} Technologies

{chr(10).join(f'''#### {tech.name} {tech.version}

**Category**: {tech.category}
**Learning Curve**: {tech.learning_curve.title()}
**Community Support**: {tech.community_support.title()}

**Justification**: {tech.justification}

**Pros**:
{chr(10).join(f"- {pro}" for pro in tech.pros)}

**Cons**:
{chr(10).join(f"- {con}" for con in tech.cons)}

**Alternatives Considered**: {', '.join(tech.alternatives)}

---
''' for tech in technologies)}
""" for i, (category, technologies) in enumerate(architecture.technology_stack.items()))}

## 3. Technology Integration

### 3.1 Frontend-Backend Integration
- **Communication**: RESTful APIs with JSON data format
- **Authentication**: JWT tokens for stateless authentication
- **Error Handling**: Standardized error responses with HTTP status codes
- **Validation**: Client-side validation with server-side verification

### 3.2 Backend-Database Integration
- **ORM**: Type-safe database access with automated migrations
- **Connection Management**: Connection pooling with automatic failover
- **Transaction Management**: ACID transactions with rollback support
- **Performance**: Query optimization and caching strategies

### 3.3 Infrastructure Integration
- **Containerization**: Docker for consistent deployment environments
- **Orchestration**: Container orchestration for scalability and reliability
- **Monitoring**: Comprehensive observability with metrics and logging
- **Security**: Automated security scanning and compliance checking

## 4. Technology Roadmap

### 4.1 Phase 1: MVP Development
- Core technology stack implementation
- Basic monitoring and logging
- Essential security measures
- Development and staging environments

### 4.2 Phase 2: Production Optimization
- Performance optimization and caching
- Advanced monitoring and alerting
- Security hardening and compliance
- Production deployment automation

### 4.3 Phase 3: Scale and Enhance
- Horizontal scaling implementation
- Advanced features and integrations
- Comprehensive testing automation
- Disaster recovery and backup systems
"""

    def _generate_component_specifications_document(self, architecture: SystemArchitecture) -> str:
        """Generate detailed component specifications document"""
        return f"""# Component Specifications: {architecture.project_name}

Generated: {datetime.now().isoformat()}

## Overview

This document provides detailed specifications for each system component, including responsibilities, interfaces, dependencies, and requirements.

{chr(10).join(f"""## {i+1}. {component.name}

### {i+1}.1 Overview
{component.description}

### {i+1}.2 Responsibilities
{chr(10).join(f"- {resp}" for resp in component.responsibilities)}

### {i+1}.3 Technology Stack
{chr(10).join(f"- **{tech.name} {tech.version}**: {tech.justification}" for tech in component.technologies)}

### {i+1}.4 Interfaces
{chr(10).join(f'''#### {interface.get('name', 'Interface')}
- **Type**: {interface.get('type', 'Unknown')}
- **Description**: {interface.get('description', 'No description')}
- **Protocol**: {interface.get('protocol', interface.get('protocols', 'Not specified'))}
''' for interface in component.interfaces)}

### {i+1}.5 Dependencies
{chr(10).join(f"- {dep}" for dep in component.dependencies) if component.dependencies else "No external dependencies"}

### {i+1}.6 Scalability Requirements
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in component.scalability_requirements.items())}

### {i+1}.7 Security Requirements
{chr(10).join(f"- {req}" for req in component.security_requirements)}

### {i+1}.8 Performance Requirements
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in component.performance_requirements.items())}

---
""" for i, component in enumerate(architecture.components))}
"""

    def _generate_deployment_architecture_document(self, architecture: SystemArchitecture) -> str:
        """Generate deployment architecture document"""
        return f"""# Deployment Architecture: {architecture.project_name}

Generated: {datetime.now().isoformat()}

## 1. Deployment Strategy

**Strategy**: {architecture.deployment_architecture.get('deployment_strategy', 'Standard deployment')}

## 2. Infrastructure Architecture

### 2.1 Cloud Infrastructure
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in architecture.deployment_architecture.get('infrastructure', {}).items())}

### 2.2 Environment Strategy
{chr(10).join(f"- **{env.title()}**: {desc}" for env, desc in architecture.deployment_architecture.get('environments', {}).items())}

## 3. CI/CD Pipeline

### 3.1 Pipeline Overview
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in architecture.deployment_architecture.get('ci_cd_pipeline', {}).items())}

### 3.2 Deployment Process
1. **Source Control**: Code changes pushed to feature branches
2. **Build**: Automated builds triggered on pull requests
3. **Testing**: Comprehensive test suite execution
4. **Security Scanning**: Automated security and vulnerability scanning
5. **Staging Deployment**: Automatic deployment to staging environment
6. **Production Deployment**: Manual approval and deployment to production
7. **Monitoring**: Post-deployment monitoring and alerting

## 4. Scalability Architecture

### 4.1 Scaling Strategy
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in architecture.deployment_architecture.get('scalability', {}).items())}

### 4.2 Load Balancing
- **Application Load Balancer**: Distributes traffic across multiple instances
- **Health Checks**: Automatic removal of unhealthy instances
- **SSL Termination**: Centralized SSL certificate management
- **Sticky Sessions**: Session affinity for stateful applications

### 4.3 Auto-scaling Configuration
- **CPU Threshold**: Scale out at 70% CPU utilization
- **Memory Threshold**: Scale out at 80% memory utilization
- **Request Rate**: Scale out at high request rates
- **Custom Metrics**: Application-specific scaling triggers

## 5. Disaster Recovery

### 5.1 Backup Strategy
- **Database Backups**: Automated daily backups with point-in-time recovery
- **File Storage Backups**: Regular backups of user-uploaded content
- **Configuration Backups**: Version-controlled infrastructure as code
- **Cross-Region Replication**: Geographic distribution for disaster recovery

### 5.2 Recovery Procedures
- **RTO (Recovery Time Objective)**: < 4 hours for critical systems
- **RPO (Recovery Point Objective)**: < 1 hour data loss maximum
- **Failover Process**: Automated failover to secondary region
- **Testing**: Regular disaster recovery testing and validation

## 6. Security Considerations

### 6.1 Network Security
- **VPC Configuration**: Private subnets for application and database tiers
- **Security Groups**: Restrictive firewall rules with least privilege
- **Network ACLs**: Additional layer of network security
- **VPN Access**: Secure access for administrative tasks

### 6.2 Container Security
- **Image Scanning**: Automated vulnerability scanning of container images
- **Runtime Security**: Container runtime protection and monitoring
- **Secrets Management**: Secure handling of sensitive configuration
- **Network Policies**: Micro-segmentation for container communication
"""

    def _generate_security_architecture_document(self, architecture: SystemArchitecture) -> str:
        """Generate security architecture document"""
        return f"""# Security Architecture: {architecture.project_name}

Generated: {datetime.now().isoformat()}

## 1. Security Overview

This document outlines the comprehensive security architecture designed to protect the system, data, and users from various security threats while maintaining usability and performance.

## 2. Authentication Architecture

### 2.1 Authentication Strategy
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in architecture.security_architecture.get('authentication_strategy', {}).items())}

### 2.2 Authentication Flow
1. **User Registration**: Secure account creation with email verification
2. **Login Process**: Credential validation with rate limiting
3. **Token Generation**: JWT access and refresh token creation
4. **Token Validation**: Middleware-based token verification
5. **Token Refresh**: Automatic token renewal process
6. **Logout**: Secure token invalidation

## 3. Authorization Model

### 3.1 Authorization Strategy
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in architecture.security_architecture.get('authorization_model', {}).items())}

### 3.2 Permission Matrix
| Role | User Management | Content Management | System Administration |
|------|----------------|-------------------|---------------------|
| Admin | Full Access | Full Access | Full Access |
| User | Self Only | Own Content | No Access |
| Guest | No Access | Read Only | No Access |

## 4. Data Protection

### 4.1 Encryption Strategy
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in architecture.security_architecture.get('data_protection', {}).get('encryption', {}).items())}

### 4.2 Privacy Controls
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in architecture.security_architecture.get('data_protection', {}).get('privacy', {}).items())}

## 5. Application Security

### 5.1 Security Controls
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in architecture.security_architecture.get('application_security', {}).items())}

### 5.2 OWASP Top 10 Mitigation
1. **Injection**: Parameterized queries and input validation
2. **Broken Authentication**: Secure session management and MFA
3. **Sensitive Data Exposure**: Encryption and secure transmission
4. **XML External Entities**: Disable XML external entity processing
5. **Broken Access Control**: Implement proper authorization checks
6. **Security Misconfiguration**: Automated security configuration
7. **Cross-Site Scripting**: Input sanitization and CSP headers
8. **Insecure Deserialization**: Avoid untrusted deserialization
9. **Known Vulnerabilities**: Regular dependency updates and scanning
10. **Insufficient Logging**: Comprehensive security event logging

## 6. Infrastructure Security

### 6.1 Security Controls
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in architecture.security_architecture.get('infrastructure_security', {}).items())}

### 6.2 Security Monitoring
- **SIEM Integration**: Security Information and Event Management
- **Intrusion Detection**: Network and host-based intrusion detection
- **Vulnerability Scanning**: Regular automated security scans
- **Penetration Testing**: Periodic third-party security assessments

## 7. Compliance and Governance

### 7.1 Compliance Standards
{chr(10).join(f"- **{standard}**: Implementation and regular auditing" for standard in architecture.security_architecture.get('compliance', {}).get('standards', []))}

### 7.2 Security Governance
- **Security Policies**: Comprehensive security policy documentation
- **Risk Assessment**: Regular security risk assessments
- **Incident Response**: Defined incident response procedures
- **Security Training**: Regular security awareness training for team
"""

    def _generate_data_architecture_document(self, architecture: SystemArchitecture) -> str:
        """Generate data architecture document"""
        return f"""# Data Architecture: {architecture.project_name}

Generated: {datetime.now().isoformat()}

## 1. Data Architecture Overview

This document describes the data architecture, including storage strategies, data modeling approaches, and data flow patterns.

## 2. Data Storage Strategy

### 2.1 Storage Components
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in architecture.data_architecture.get('data_storage_strategy', {}).items())}

### 2.2 Data Distribution
- **Primary Database**: Handles all transactional data with ACID compliance
- **Caching Layer**: Stores frequently accessed data for performance optimization
- **File Storage**: Manages user uploads and static assets
- **Backup Storage**: Maintains data backups and disaster recovery copies

## 3. Data Modeling

### 3.1 Modeling Approach
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in architecture.data_architecture.get('data_modeling', {}).items())}

### 3.2 Schema Design Principles
- **Normalization**: Third normal form for transactional data
- **Denormalization**: Strategic denormalization for read-heavy operations
- **Indexing**: Comprehensive indexing strategy for query optimization
- **Constraints**: Database-level constraints for data integrity

## 4. Data Flow Architecture

### 4.1 Read Patterns
{chr(10).join(f"- {pattern}" for pattern in architecture.data_architecture.get('data_flow', {}).get('read_patterns', []))}

### 4.2 Write Patterns
{chr(10).join(f"- {pattern}" for pattern in architecture.data_architecture.get('data_flow', {}).get('write_patterns', []))}

### 4.3 Data Synchronization
- **Real-time Sync**: WebSocket-based real-time data updates
- **Batch Processing**: Scheduled batch jobs for data aggregation
- **Event-driven Updates**: Event-based data synchronization
- **Conflict Resolution**: Strategies for handling data conflicts

## 5. Data Security

### 5.1 Security Measures
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in architecture.data_architecture.get('data_security', {}).items())}

### 5.2 Data Classification
- **Public**: Non-sensitive data accessible to all users
- **Internal**: Data restricted to authenticated users
- **Confidential**: Sensitive data with restricted access
- **Restricted**: Highly sensitive data with strict access controls

## 6. Data Scalability

### 6.1 Scaling Strategies
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in architecture.data_architecture.get('scalability', {}).items())}

### 6.2 Performance Optimization
- **Query Optimization**: Regular query performance analysis and optimization
- **Connection Pooling**: Efficient database connection management
- **Caching Strategy**: Multi-level caching for performance improvement
- **Data Archiving**: Automated archiving of historical data

## 7. Data Governance

### 7.1 Data Quality
- **Validation Rules**: Comprehensive data validation at all entry points
- **Data Cleansing**: Automated data cleansing and normalization
- **Quality Metrics**: Regular data quality assessment and reporting
- **Error Handling**: Robust error handling and data recovery procedures

### 7.2 Data Lifecycle Management
- **Data Retention**: Automated data retention policies
- **Data Purging**: Secure data deletion procedures
- **Data Migration**: Strategies for data migration and upgrades
- **Compliance**: GDPR and other regulatory compliance measures
"""

    def _generate_integration_patterns_document(self, architecture: SystemArchitecture) -> str:
        """Generate integration patterns document"""
        return f"""# Integration Patterns: {architecture.project_name}

Generated: {datetime.now().isoformat()}

## 1. Integration Overview

This document describes the integration patterns used to connect different system components and external services.

## 2. Integration Patterns

{chr(10).join(f"""### 2.{i+1} {pattern.get('name', 'Integration Pattern')}

**Description**: {pattern.get('description', 'No description')}

**Components**: {', '.join(pattern.get('components', []))}

**Protocol**: {pattern.get('protocol', 'Not specified')}

**Implementation Details**:
{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in pattern.items() if key not in ['name', 'description', 'components', 'protocol'])}

---
""" for i, pattern in enumerate(architecture.integration_patterns))}

## 3. API Design Standards

### 3.1 RESTful API Guidelines
- **Resource Naming**: Use nouns for resources, verbs for actions
- **HTTP Methods**: GET (read), POST (create), PUT (update), DELETE (remove)
- **Status Codes**: Appropriate HTTP status codes for all responses
- **Versioning**: URL path versioning (e.g., /api/v1/)
- **Documentation**: OpenAPI 3.0 specification for all endpoints

### 3.2 Request/Response Format
```json
// Request Format
{{
  "data": {{
    // Request payload
  }},
  "metadata": {{
    "requestId": "uuid",
    "timestamp": "ISO 8601",
    "version": "1.0"
  }}
}}

// Response Format
{{
  "data": {{
    // Response payload
  }},
  "metadata": {{
    "requestId": "uuid",
    "timestamp": "ISO 8601",
    "version": "1.0"
  }},
  "errors": [
    // Error details if any
  ]
}}
```

### 3.3 Error Handling
- **Consistent Format**: Standardized error response format
- **Error Codes**: Application-specific error codes
- **Localization**: Multi-language error messages
- **Logging**: Comprehensive error logging for debugging

## 4. Event-Driven Integration

### 4.1 Event Architecture
- **Event Bus**: Central event distribution mechanism
- **Event Types**: Domain events, integration events, system events
- **Event Schema**: Standardized event structure and versioning
- **Event Sourcing**: Event-based state reconstruction

### 4.2 Message Patterns
- **Publish-Subscribe**: Asynchronous event distribution
- **Request-Reply**: Synchronous request-response patterns
- **Message Queuing**: Reliable message delivery with persistence
- **Dead Letter Queue**: Handling of failed message processing

## 5. External Service Integration

### 5.1 Third-Party APIs
- **Authentication**: OAuth 2.0 and API key management
- **Rate Limiting**: Respect for third-party rate limits
- **Circuit Breaker**: Fault tolerance for external service failures
- **Retry Logic**: Exponential backoff for transient failures

### 5.2 Webhook Integration
- **Webhook Security**: Signature verification and HTTPS enforcement
- **Idempotency**: Handling of duplicate webhook deliveries
- **Retry Mechanism**: Automatic retry for failed webhook deliveries
- **Monitoring**: Webhook delivery monitoring and alerting

## 6. Data Integration

### 6.1 Data Synchronization
- **Real-time Sync**: WebSocket-based real-time data updates
- **Batch Sync**: Scheduled batch data synchronization
- **Change Data Capture**: Database change event streaming
- **Conflict Resolution**: Strategies for handling data conflicts

### 6.2 Data Transformation
- **ETL Processes**: Extract, Transform, Load data pipelines
- **Data Mapping**: Field mapping between different data formats
- **Data Validation**: Comprehensive data validation rules
- **Error Handling**: Robust error handling and data recovery
"""

    def _generate_quality_attributes_document(self, architecture: SystemArchitecture) -> str:
        """Generate quality attributes document"""
        return f"""# Quality Attributes: {architecture.project_name}

Generated: {datetime.now().isoformat()}

## 1. Quality Attributes Overview

This document defines the quality attributes (non-functional requirements) that the system must satisfy.

## 2. Quality Attributes

{chr(10).join(f"""### 2.{i+1} {attribute.title()}

{chr(10).join(f"- **{key.replace('_', ' ').title()}**: {value}" for key, value in details.items())}

""" for i, (attribute, details) in enumerate(architecture.quality_attributes.items()))}

## 3. Quality Attribute Scenarios

### 3.1 Performance Scenarios
- **Normal Load**: System handles expected user load with acceptable response times
- **Peak Load**: System maintains performance during traffic spikes
- **Stress Test**: System gracefully degrades under extreme load
- **Recovery**: System quickly recovers from performance degradation

### 3.2 Reliability Scenarios
- **Component Failure**: System continues operating when individual components fail
- **Data Corruption**: System detects and recovers from data corruption
- **Network Partition**: System handles network connectivity issues
- **Disaster Recovery**: System recovers from catastrophic failures

### 3.3 Security Scenarios
- **Authentication Attack**: System prevents unauthorized access attempts
- **Data Breach**: System minimizes impact of potential data breaches
- **Injection Attack**: System prevents SQL injection and XSS attacks
- **Privilege Escalation**: System prevents unauthorized privilege escalation

### 3.4 Usability Scenarios
- **New User**: New users can complete core tasks within specified time
- **Expert User**: Expert users can efficiently perform complex tasks
- **Error Recovery**: Users can easily recover from errors
- **Accessibility**: System is accessible to users with disabilities

## 4. Quality Metrics and Monitoring

### 4.1 Performance Metrics
- **Response Time**: 95th percentile response time for all API endpoints
- **Throughput**: Requests per second under normal and peak load
- **Resource Utilization**: CPU, memory, and disk usage metrics
- **Error Rate**: Percentage of failed requests and error types

### 4.2 Reliability Metrics
- **Uptime**: System availability percentage (99.9% target)
- **MTBF**: Mean Time Between Failures for system components
- **MTTR**: Mean Time To Recovery from failures
- **Data Integrity**: Data consistency and corruption detection

### 4.3 Security Metrics
- **Authentication Success Rate**: Percentage of successful authentications
- **Security Incidents**: Number and severity of security incidents
- **Vulnerability Scan Results**: Regular security vulnerability assessments
- **Compliance Score**: Adherence to security standards and regulations

### 4.4 Usability Metrics
- **Task Completion Rate**: Percentage of users completing core tasks
- **User Satisfaction**: User satisfaction scores and feedback
- **Error Recovery Time**: Time required for users to recover from errors
- **Accessibility Compliance**: WCAG 2.1 compliance assessment

## 5. Quality Assurance Strategy

### 5.1 Testing Strategy
- **Unit Testing**: 80%+ code coverage with automated unit tests
- **Integration Testing**: Comprehensive API and component integration tests
- **Performance Testing**: Regular load and stress testing
- **Security Testing**: Automated security scanning and penetration testing

### 5.2 Monitoring and Alerting
- **Real-time Monitoring**: Continuous monitoring of all quality metrics
- **Alerting**: Automated alerts for quality attribute violations
- **Dashboards**: Real-time dashboards for quality metrics visualization
- **Reporting**: Regular quality reports and trend analysis
"""

    def _generate_architecture_decisions_document(self, architecture: SystemArchitecture) -> str:
        """Generate architecture decision records document"""
        return f"""# Architecture Decision Records: {architecture.project_name}

Generated: {datetime.now().isoformat()}

## 1. Architecture Decision Records (ADRs)

This document contains the key architectural decisions made during the system design process.

## ADR-001: Architecture Pattern Selection

**Status**: Accepted
**Date**: {datetime.now().strftime('%Y-%m-%d')}

### Context
The system requires an architecture pattern that balances simplicity, scalability, and maintainability while considering team expertise and project constraints.

### Decision
Selected **{architecture.architecture_pattern.value.replace('_', ' ').title()}** architecture pattern.

### Rationale
{self.pattern_templates.get(architecture.architecture_pattern, {}).get('description', 'Custom architecture pattern selected based on specific requirements')}

**Pros**:
- {chr(10).join(f"- {pro}" for pro in self.pattern_templates.get(architecture.architecture_pattern, {}).get('best_for', ['Suitable for project requirements']))}

**Cons**:
- Complexity: {self.pattern_templates.get(architecture.architecture_pattern, {}).get('complexity', 'Medium')}
- Learning curve for team members

### Consequences
- Development approach will follow the selected pattern
- Team training may be required
- Architecture documentation must reflect this pattern

---

## ADR-002: Technology Stack Selection

**Status**: Accepted
**Date**: {datetime.now().strftime('%Y-%m-%d')}

### Context
Technology selection must balance performance, maintainability, team expertise, and ecosystem maturity.

### Decision
Selected the following core technologies:
{chr(10).join(f"- **{category.title()}**: {', '.join(tech.name for tech in technologies)}" for category, technologies in architecture.technology_stack.items() if technologies)}

### Rationale
Each technology was selected based on:
- Performance requirements and benchmarks
- Team expertise and learning curve
- Community support and ecosystem maturity
- Long-term maintainability and support

### Consequences
- Development team training on selected technologies
- Dependency on chosen technology ecosystems
- Migration complexity if technology changes are needed

---

## ADR-003: Scalability Approach

**Status**: Accepted
**Date**: {datetime.now().strftime('%Y-%m-%d')}

### Context
System must handle expected user load while maintaining performance and cost efficiency.

### Decision
Implement **{architecture.scalability_tier.value.title()}** scalability tier with horizontal scaling capabilities.

### Rationale
- Expected user load: {self._get_user_capacity(architecture.scalability_tier)} concurrent users
- Performance requirements: {self._get_throughput_requirement(architecture.scalability_tier)} requests/second
- Cost optimization through auto-scaling
- Future growth accommodation

### Consequences
- Infrastructure complexity increases
- Monitoring and alerting requirements
- Auto-scaling configuration and testing needed

---

## ADR-004: Security Architecture

**Status**: Accepted
**Date**: {datetime.now().strftime('%Y-%m-%d')}

### Context
System handles sensitive user data and requires comprehensive security measures.

### Decision
Implement defense-in-depth security architecture with:
- JWT-based stateless authentication
- Role-based access control (RBAC)
- Encryption at rest and in transit
- Comprehensive security monitoring

### Rationale
- Scalable authentication without server-side sessions
- Fine-grained access control
- Data protection compliance (GDPR, etc.)
- Proactive security monitoring

### Consequences
- Additional complexity in authentication flow
- Security monitoring infrastructure required
- Regular security audits and updates needed

---

## ADR-005: Data Architecture

**Status**: Accepted
**Date**: {datetime.now().strftime('%Y-%m-%d')}

### Context
System requires reliable data storage with good performance and scalability.

### Decision
Implement PostgreSQL as primary database with Redis caching layer.

### Rationale
- ACID compliance for data integrity
- Excellent performance and scalability
- Strong ecosystem and tooling
- JSON support for flexible data models

### Consequences
- Database administration expertise required
- Backup and recovery procedures needed
- Cache invalidation strategies required

---

## ADR-006: Deployment Strategy

**Status**: Accepted
**Date**: {datetime.now().strftime('%Y-%m-%d')}

### Context
System requires reliable, scalable deployment with minimal downtime.

### Decision
Implement containerized deployment with {architecture.deployment_architecture.get('deployment_strategy', 'rolling deployment')}.

### Rationale
- Consistent deployment environments
- Easy scaling and rollback capabilities
- Infrastructure as code
- Automated deployment pipeline

### Consequences
- Container orchestration complexity
- DevOps expertise required
- Monitoring and logging infrastructure needed

---

## Decision Review Process

### Review Schedule
- Monthly architecture review meetings
- Quarterly technology assessment
- Annual architecture strategy review

### Review Criteria
- Technical debt assessment
- Performance metrics analysis
- Security posture evaluation
- Cost optimization opportunities

### Change Management
- All architectural changes require ADR documentation
- Impact assessment for major changes
- Stakeholder approval for significant decisions
- Migration planning for technology changes
"""

# Main execution function for standalone usage
async def main():
    """Main function for standalone execution"""
    import sys

    if len(sys.argv) < 2:
        print("Usage: python architect_agent.py '<analyst_output_file>' [output_path]")
        print("Example: python architect_agent.py analyst_output.json ./architecture_output")
        return

    analyst_output_file = sys.argv[1]
    output_path = Path(sys.argv[2]) if len(sys.argv) > 2 else Path("./architect_output")

    try:
        # Load analyst output
        with open(analyst_output_file, 'r') as f:
            analyst_output = json.load(f)

        print(f"🏗️ Starting architecture design for: {analyst_output.get('project_name', 'Unknown Project')}")
        print(f"📁 Output path: {output_path}")

        # Initialize architect agent
        architect = ArchitectAgent()

        # Design architecture
        architecture = await architect.design_architecture(analyst_output)

        print(f"✅ Architecture design complete!")
        print(f"   Architecture Pattern: {architecture.architecture_pattern.value}")
        print(f"   Scalability Tier: {architecture.scalability_tier.value}")
        print(f"   Components: {len(architecture.components)}")
        print(f"   Technology Categories: {len(architecture.technology_stack)}")

        # Generate documentation
        print("\n📄 Generating architecture documentation...")
        generated_files = await architect.generate_architecture_documents(architecture, output_path)

        print(f"✅ Generated {len(generated_files)} documentation files:")
        for file in generated_files:
            print(f"   - {file}")

        print(f"🎉 Architecture design complete! Check {output_path} for all generated documents.")

    except FileNotFoundError:
        print(f"❌ Error: Analyst output file '{analyst_output_file}' not found")
    except json.JSONDecodeError:
        print(f"❌ Error: Invalid JSON in analyst output file")
    except Exception as e:
        print(f"❌ Error during architecture design: {e}")
        logger.error(f"Architecture design failed: {e}", exc_info=True)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
