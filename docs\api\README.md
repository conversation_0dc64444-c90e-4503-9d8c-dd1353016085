# TaoForge API Documentation

The TaoForge API provides comprehensive programmatic access to all autonomous software creation capabilities. This RESTful API allows you to integrate TaoForge into your existing workflows, tools, and applications.

## 🌐 Base URL

```
Production: https://api.taoforge.dev
Development: http://localhost:8000
Staging: https://staging-api.taoforge.dev
```

## 🔐 Authentication

TaoForge supports multiple authentication methods for different use cases.

### API Key Authentication (Recommended)

Include your API key in the request headers:

```http
Authorization: Bearer your_api_key_here
Content-Type: application/json
```

### JWT Token Authentication

For user-specific operations:

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

### Getting an API Key

```bash
# Generate a new API key
taoforge auth create-key --name "My Integration" --scope "projects:create,projects:read"

# List existing keys
taoforge auth list-keys

# Revoke a key
taoforge auth revoke-key <key-id>

# Rotate a key
taoforge auth rotate-key <key-id>
```

### AI Provider Configuration

TaoForge supports multiple AI providers for project generation. Configure your preferred providers:

```bash
# Set up AI provider API keys
taoforge keys setup

# Or configure individual providers
taoforge keys set openai sk-your-openai-key
taoforge keys set openrouter sk-or-your-openrouter-key
taoforge keys set anthropic sk-ant-your-anthropic-key
taoforge keys set azure your-azure-key

# List configured providers
taoforge keys list

# Test provider connections
taoforge keys test openai
taoforge keys test openrouter
```

**Supported AI Providers:**
- **OpenAI**: Best performance and reliability (GPT-4, GPT-3.5-turbo)
- **OpenRouter**: Access to multiple AI models through one API
- **Anthropic**: Claude models (Claude-3-Sonnet, Claude-3-Haiku)
- **Azure OpenAI**: Enterprise-grade deployment with enhanced security

### API Key Scopes

| Scope | Description |
|-------|-------------|
| `projects:create` | Create new projects |
| `projects:read` | Read project information |
| `projects:update` | Update existing projects |
| `projects:delete` | Delete projects |
| `templates:read` | Access project templates |
| `templates:write` | Create/modify templates |
| `system:read` | Read system status |
| `system:admin` | Administrative operations |

## 📊 API Overview

### Core Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/health` | System health check |
| `GET` | `/version` | API version information |
| `POST` | `/projects` | Create a new project |
| `GET` | `/projects` | List all projects |
| `GET` | `/projects/{id}` | Get project details |
| `PUT` | `/projects/{id}` | Update project |
| `DELETE` | `/projects/{id}` | Delete project |
| `GET` | `/projects/{id}/status` | Get project status |
| `POST` | `/projects/{id}/regenerate` | Regenerate project files |

| Endpoint | Description |
|----------|-------------|
| `POST /projects` | Create a new project |
| `GET /projects` | List all projects |
| `GET /projects/{id}` | Get project details |
| `DELETE /projects/{id}` | Delete a project |
| `GET /projects/{id}/status` | Get project status |
| `POST /projects/{id}/regenerate` | Regenerate project components |

### System Endpoints

| Endpoint | Description |
|----------|-------------|
| `GET /health` | System health check |
| `GET /status` | Detailed system status |
| `GET /agents` | List available agents |
| `GET /workflows` | List available workflows |
| `GET /templates` | List project templates |

### Monitoring Endpoints

| Endpoint | Description |
|----------|-------------|
| `GET /pheromones` | Get pheromone data |
| `GET /projects/{id}/pheromones` | Get project pheromones |
| `GET /projects/{id}/logs` | Get project logs |
| `GET /metrics` | System metrics |

## 🚀 Quick Start Example

### Create a Project

```bash
curl -X POST https://api.taoforge.dev/projects \
  -H "Authorization: Bearer your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A task management application with user authentication and real-time collaboration",
    "project_name": "TaskMaster",
    "project_type": "fullstack",
    "features": ["authentication", "real_time", "collaboration"],
    "tech_preferences": {
      "frontend": "react",
      "backend": "nodejs",
      "database": "postgresql"
    }
  }'
```

### Response

```json
{
  "status": "success",
  "project_id": "proj_abc123def456",
  "project_slug": "taskmaster",
  "estimated_completion": "2024-01-15T14:30:00Z",
  "workflow": "greenfield-fullstack",
  "agents_assigned": [
    {"role": "analyst", "id": "agent_001"},
    {"role": "architect", "id": "agent_002"},
    {"role": "developer", "id": "agent_003"},
    {"role": "qa", "id": "agent_004"}
  ],
  "next_steps": [
    "Requirements analysis in progress",
    "Monitor progress at /projects/proj_abc123def456/status"
  ]
}
```

## 📋 Detailed Endpoints

### Projects

#### Create Project

```http
POST /projects
```

**Request Body:**

```json
{
  "prompt": "string (required) - Natural language description",
  "project_name": "string (required) - Project name",
  "project_type": "string (required) - fullstack|api|frontend|mobile|desktop",
  "features": ["array of strings (optional) - Specific features"],
  "tech_preferences": {
    "frontend": "string (optional) - react|vue|angular",
    "backend": "string (optional) - nodejs|python|java",
    "database": "string (optional) - postgresql|mysql|mongodb",
    "ai_provider": "string (optional) - openai|openrouter|anthropic|azure"
  },
  "workflow": "string (optional) - Custom workflow name",
  "priority": "string (optional) - low|normal|high",
  "deadline": "string (optional) - ISO 8601 date",
  "team_size": "number (optional) - Number of developers",
  "complexity": "string (optional) - simple|medium|complex"
}
```

**Response:**

```json
{
  "status": "success|error",
  "project_id": "string",
  "project_slug": "string",
  "estimated_completion": "ISO 8601 date",
  "workflow": "string",
  "agents_assigned": [
    {
      "role": "string",
      "id": "string",
      "status": "assigned|active|completed"
    }
  ],
  "error": "string (if status is error)"
}
```

#### Get Project Status

```http
GET /projects/{project_id}/status
```

**Response:**

```json
{
  "project_id": "string",
  "status": "initializing|in_progress|completed|failed",
  "progress": {
    "overall": 65,
    "phases": {
      "requirements_analysis": {"status": "completed", "progress": 100},
      "architecture_design": {"status": "completed", "progress": 100},
      "development": {"status": "in_progress", "progress": 60},
      "quality_assurance": {"status": "pending", "progress": 0}
    }
  },
  "agents": [
    {
      "role": "developer",
      "status": "active",
      "current_task": "Implementing user authentication",
      "progress": 60,
      "estimated_completion": "2024-01-15T16:00:00Z"
    }
  ],
  "outputs": [
    {
      "type": "document",
      "name": "requirements.md",
      "path": "/docs/requirements.md",
      "created_at": "2024-01-15T10:00:00Z"
    },
    {
      "type": "code",
      "name": "User model",
      "path": "/server/models/User.js",
      "created_at": "2024-01-15T12:30:00Z"
    }
  ],
  "next_milestones": [
    {
      "name": "Complete user authentication",
      "estimated_completion": "2024-01-15T16:00:00Z"
    },
    {
      "name": "Implement task management",
      "estimated_completion": "2024-01-15T18:00:00Z"
    }
  ]
}
```

#### List Projects

```http
GET /projects?status=all&limit=20&offset=0&sort=created_at&order=desc
```

**Query Parameters:**

- `status` - Filter by status: `all|active|completed|failed`
- `limit` - Number of results (default: 20, max: 100)
- `offset` - Pagination offset
- `sort` - Sort field: `created_at|updated_at|name|progress`
- `order` - Sort order: `asc|desc`
- `search` - Search in project names and descriptions

**Response:**

```json
{
  "projects": [
    {
      "project_id": "string",
      "name": "string",
      "description": "string",
      "status": "string",
      "progress": 65,
      "created_at": "ISO 8601 date",
      "updated_at": "ISO 8601 date",
      "estimated_completion": "ISO 8601 date"
    }
  ],
  "pagination": {
    "total": 150,
    "limit": 20,
    "offset": 0,
    "has_more": true
  }
}
```

### System Information

#### Health Check

```http
GET /health
```

**Response:**

```json
{
  "status": "healthy|degraded|unhealthy",
  "timestamp": "ISO 8601 date",
  "version": "1.0.0",
  "uptime": 3600,
  "components": {
    "orchestrator": {"status": "healthy", "response_time": 15},
    "pheromone_system": {"status": "healthy", "active_projects": 5},
    "workflow_engine": {"status": "healthy", "active_workflows": 3},
    "agent_executors": {"status": "healthy", "available_agents": 4}
  }
}
```

#### System Status

```http
GET /status
```

**Response:**

```json
{
  "system": {
    "version": "1.0.0",
    "environment": "production",
    "uptime": 86400,
    "memory_usage": "512MB",
    "cpu_usage": "25%"
  },
  "statistics": {
    "total_projects": 1250,
    "active_projects": 15,
    "completed_projects": 1200,
    "failed_projects": 35,
    "success_rate": 97.2
  },
  "agents": {
    "analyst": {"active": 3, "queue": 2},
    "architect": {"active": 2, "queue": 1},
    "developer": {"active": 8, "queue": 5},
    "qa": {"active": 2, "queue": 1}
  },
  "performance": {
    "avg_project_time": "45 minutes",
    "avg_response_time": "120ms",
    "throughput": "15 projects/hour"
  }
}
```

### Pheromone System

#### Get Pheromones

```http
GET /pheromones?project_id=proj_123&signal=all&limit=50&since=2024-01-15T10:00:00Z
```

**Query Parameters:**

- `project_id` - Filter by project ID
- `signal` - Filter by signal type
- `agent_id` - Filter by agent ID
- `limit` - Number of results (default: 50, max: 200)
- `since` - Get pheromones since timestamp
- `intensity_min` - Minimum intensity threshold

**Response:**

```json
{
  "pheromones": [
    {
      "id": "pheromone_xyz789",
      "signal": "requirements_completed",
      "payload": {
        "outputs": ["requirements.md", "user_stories.md"],
        "next_phase": "architecture_design"
      },
      "project_id": "proj_123",
      "agent_id": "agent_001",
      "intensity": 0.9,
      "decay_rate": 0.1,
      "timestamp": "2024-01-15T12:00:00Z"
    }
  ],
  "statistics": {
    "total_count": 150,
    "unique_signals": 25,
    "active_projects": 5,
    "avg_intensity": 0.7
  }
}
```

### WebSocket API

For real-time updates, connect to the WebSocket endpoint:

```javascript
const ws = new WebSocket('wss://api.taoforge.dev/ws');

// Subscribe to project updates
ws.send(JSON.stringify({
  type: 'subscribe',
  channel: 'project_updates',
  project_id: 'proj_123'
}));

// Receive real-time updates
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('Update:', data);
};
```

## 🔧 Error Handling

### Error Response Format

```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "The request is invalid",
    "details": "Project name must be between 3 and 50 characters",
    "timestamp": "2024-01-15T12:00:00Z",
    "request_id": "req_abc123"
  }
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `INVALID_REQUEST` | 400 | Request validation failed |
| `UNAUTHORIZED` | 401 | Invalid or missing API key |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `RATE_LIMITED` | 429 | Too many requests |
| `INTERNAL_ERROR` | 500 | Internal server error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |

## 📊 Rate Limiting

API requests are rate limited to ensure fair usage:

- **Free Tier:** 100 requests/hour, 5 concurrent projects
- **Pro Tier:** 1,000 requests/hour, 20 concurrent projects
- **Enterprise:** Custom limits

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248000
```

## 🔗 SDKs and Libraries

### Official SDKs

- **Python:** `pip install taoforge-python`
- **Node.js:** `npm install taoforge-js`
- **Go:** `go get github.com/taoforge/taoforge-go`
- **Java:** Maven/Gradle dependency available

### Example Usage (Python)

```python
from taoforge import TaoForgeClient

client = TaoForgeClient(api_key="your_api_key")

# Create a project
project = client.projects.create(
    prompt="A blog application with CMS features",
    project_name="BlogCMS",
    project_type="fullstack"
)

# Monitor progress
status = client.projects.get_status(project.id)
print(f"Progress: {status.progress.overall}%")

# Get real-time updates
for update in client.projects.stream_updates(project.id):
    print(f"Update: {update.message}")
```

## 📚 More Resources

- **[API Examples](examples.md)** - Detailed code examples
- **[WebSocket Documentation](websockets.md)** - Real-time API guide
- **[SDK Documentation](sdks.md)** - Language-specific guides
- **[Postman Collection](postman.md)** - Ready-to-use API collection
