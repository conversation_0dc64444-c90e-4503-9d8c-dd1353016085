import * as vscode from 'vscode';
import * as path from 'path';

export class EnhancedWebviewProvider {
    private static readonly viewType = 'aetherforge.enhancedView';
    private panel: vscode.WebviewPanel | undefined;
    private context: vscode.ExtensionContext;
    private disposables: vscode.Disposable[] = [];

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
    }

    public show(): void {
        if (this.panel) {
            this.panel.reveal(vscode.ViewColumn.One);
            return;
        }

        this.panel = vscode.window.createWebviewPanel(
            EnhancedWebviewProvider.viewType,
            'Aetherforge Dashboard',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.file(path.join(this.context.extensionPath, 'webview', 'dist'))
                ]
            }
        );

        this.panel.webview.html = this.getWebviewContent();
        this.setupMessageHandling();

        this.panel.onDidDispose(() => {
            this.panel = undefined;
            this.dispose();
        }, null, this.disposables);
    }

    private getWebviewContent(): string {
        const webviewUri = this.panel!.webview.asWebviewUri(
            vscode.Uri.file(path.join(this.context.extensionPath, 'webview', 'dist'))
        );

        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${this.panel!.webview.cspSource} 'unsafe-inline'; script-src ${this.panel!.webview.cspSource} 'unsafe-inline'; img-src ${this.panel!.webview.cspSource} data:;">
    <title>Aetherforge Dashboard</title>
    <link rel="stylesheet" href="${webviewUri}/main.css">
</head>
<body>
    <div id="root"></div>
    <script>
        const vscode = acquireVsCodeApi();
        window.vscode = vscode;
    </script>
    <script src="${webviewUri}/main.js"></script>
</body>
</html>`;
    }

    private setupMessageHandling(): void {
        this.panel!.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.type) {
                    case 'createProject':
                        await this.handleCreateProject(message.data);
                        break;
                    case 'getProjectStatus':
                        await this.handleGetProjectStatus(message.data);
                        break;
                    case 'getSystemHealth':
                        await this.handleGetSystemHealth();
                        break;
                    case 'configureSettings':
                        await this.handleConfigureSettings(message.data);
                        break;
                    case 'openProject':
                        await this.handleOpenProject(message.data);
                        break;
                    case 'deleteProject':
                        await this.handleDeleteProject(message.data);
                        break;
                    case 'exportProject':
                        await this.handleExportProject(message.data);
                        break;
                    case 'viewLogs':
                        await this.handleViewLogs(message.data);
                        break;
                }
            },
            undefined,
            this.disposables
        );
    }

    private async handleCreateProject(data: any): Promise<void> {
        try {
            // Send initial status
            this.sendMessage({
                type: 'projectCreationStarted',
                data: { projectId: data.projectId }
            });

            // Simulate project creation with progress updates
            const steps = [
                'Analyzing requirements...',
                'Generating architecture...',
                'Creating project structure...',
                'Implementing core features...',
                'Running quality checks...',
                'Finalizing project...'
            ];

            for (let i = 0; i < steps.length; i++) {
                await new Promise(resolve => setTimeout(resolve, 2000));
                this.sendMessage({
                    type: 'projectCreationProgress',
                    data: {
                        projectId: data.projectId,
                        step: steps[i],
                        progress: ((i + 1) / steps.length) * 100
                    }
                });
            }

            // Send completion
            this.sendMessage({
                type: 'projectCreationCompleted',
                data: {
                    projectId: data.projectId,
                    projectPath: `/projects/${data.projectName}`,
                    success: true
                }
            });

        } catch (error) {
            this.sendMessage({
                type: 'projectCreationError',
                data: {
                    projectId: data.projectId,
                    error: error instanceof Error ? error.message : 'Unknown error'
                }
            });
        }
    }

    private async handleGetProjectStatus(data: any): Promise<void> {
        // Mock project status data
        const projects = [
            {
                id: '1',
                name: 'E-commerce Platform',
                type: 'fullstack',
                status: 'completed',
                progress: 100,
                createdAt: new Date(Date.now() - 86400000).toISOString(),
                lastModified: new Date(Date.now() - 3600000).toISOString(),
                technologies: ['React', 'Node.js', 'MongoDB'],
                metrics: {
                    files: 156,
                    lines: 12450,
                    tests: 89,
                    coverage: 85
                }
            },
            {
                id: '2',
                name: 'Mobile Task Manager',
                type: 'mobile',
                status: 'in_progress',
                progress: 65,
                createdAt: new Date(Date.now() - 43200000).toISOString(),
                lastModified: new Date(Date.now() - 1800000).toISOString(),
                technologies: ['React Native', 'Firebase'],
                metrics: {
                    files: 78,
                    lines: 6200,
                    tests: 34,
                    coverage: 72
                }
            }
        ];

        this.sendMessage({
            type: 'projectStatusUpdate',
            data: { projects }
        });
    }

    private async handleGetSystemHealth(): Promise<void> {
        // Mock system health data
        const systemHealth = {
            orchestrator: {
                status: 'healthy',
                uptime: '2d 14h 32m',
                version: '1.0.0',
                lastCheck: new Date().toISOString()
            },
            agents: {
                analyst: { status: 'healthy', load: 15 },
                architect: { status: 'healthy', load: 23 },
                developer: { status: 'healthy', load: 45 },
                qa: { status: 'healthy', load: 12 }
            },
            resources: {
                cpu: 34,
                memory: 67,
                disk: 23,
                network: 12
            },
            apiConnections: {
                openai: { status: 'connected', latency: 120 },
                anthropic: { status: 'connected', latency: 95 },
                local: { status: 'disconnected', latency: 0 }
            }
        };

        this.sendMessage({
            type: 'systemHealthUpdate',
            data: systemHealth
        });
    }

    private async handleConfigureSettings(data: any): Promise<void> {
        const config = vscode.workspace.getConfiguration('aetherforge');
        
        for (const [key, value] of Object.entries(data)) {
            await config.update(key, value, vscode.ConfigurationTarget.Global);
        }

        this.sendMessage({
            type: 'settingsUpdated',
            data: { success: true }
        });
    }

    private async handleOpenProject(data: any): Promise<void> {
        const projectPath = data.projectPath;
        const projectUri = vscode.Uri.file(projectPath);
        
        try {
            await vscode.commands.executeCommand('vscode.openFolder', projectUri, true);
            this.sendMessage({
                type: 'projectOpened',
                data: { success: true, projectPath }
            });
        } catch (error) {
            this.sendMessage({
                type: 'projectOpenError',
                data: { error: error instanceof Error ? error.message : 'Failed to open project' }
            });
        }
    }

    private async handleDeleteProject(data: any): Promise<void> {
        const result = await vscode.window.showWarningMessage(
            `Are you sure you want to delete project "${data.projectName}"?`,
            { modal: true },
            'Delete',
            'Cancel'
        );

        if (result === 'Delete') {
            // In a real implementation, this would delete the project
            this.sendMessage({
                type: 'projectDeleted',
                data: { projectId: data.projectId, success: true }
            });
        }
    }

    private async handleExportProject(data: any): Promise<void> {
        const options: vscode.SaveDialogOptions = {
            defaultUri: vscode.Uri.file(`${data.projectName}.zip`),
            filters: {
                'ZIP files': ['zip']
            }
        };

        const fileUri = await vscode.window.showSaveDialog(options);
        if (fileUri) {
            // In a real implementation, this would export the project
            this.sendMessage({
                type: 'projectExported',
                data: { projectId: data.projectId, exportPath: fileUri.fsPath, success: true }
            });
        }
    }

    private async handleViewLogs(data: any): Promise<void> {
        // Mock log data
        const logs = [
            {
                timestamp: new Date().toISOString(),
                level: 'info',
                source: 'orchestrator',
                message: 'Project creation started',
                details: { projectId: data.projectId }
            },
            {
                timestamp: new Date(Date.now() - 5000).toISOString(),
                level: 'debug',
                source: 'analyst',
                message: 'Requirements analysis completed',
                details: { requirements: 15, complexity: 'medium' }
            },
            {
                timestamp: new Date(Date.now() - 10000).toISOString(),
                level: 'warn',
                source: 'developer',
                message: 'Dependency conflict detected',
                details: { package: 'react', versions: ['17.0.2', '18.2.0'] }
            }
        ];

        this.sendMessage({
            type: 'logsUpdate',
            data: { logs }
        });
    }

    private sendMessage(message: any): void {
        if (this.panel) {
            this.panel.webview.postMessage(message);
        }
    }

    public dispose(): void {
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
