#!/usr/bin/env python3
"""
End-to-end test demonstrating OpenRouter integration functionality
This test shows how OpenRouter can be used in the Aetherforge system
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_openrouter_end_to_end():
    """Demonstrate OpenRouter integration end-to-end"""
    print("🌐 OpenRouter End-to-End Integration Test")
    print("=" * 50)
    
    try:
        from api_manager import APIManager, APIProvider
        
        # Test 1: Create API Manager
        print("\n1. 🔧 Creating API Manager...")
        api_manager = APIManager()
        print("   ✅ API Manager created successfully")
        
        # Test 2: Check OpenRouter Configuration
        print("\n2. 📋 Checking OpenRouter Configuration...")
        providers = api_manager.list_configured_providers()
        openrouter_config = next((p for p in providers if p["provider"] == "openrouter"), None)
        
        if openrouter_config:
            print(f"   ✅ OpenRouter configured")
            print(f"   📋 Model: {openrouter_config['model']}")
            print(f"   🌐 Base URL: {openrouter_config.get('base_url', 'Default')}")
            print(f"   🔑 Has API Key: {openrouter_config['has_key']}")
        else:
            print("   ❌ OpenRouter not configured")
            return False
        
        # Test 3: Check Provider Enumeration
        print("\n3. 📝 Testing Provider Enumeration...")
        try:
            openrouter_provider = APIProvider.OPENROUTER
            print(f"   ✅ OpenRouter provider enum: {openrouter_provider.value}")
        except AttributeError:
            print("   ❌ OpenRouter provider enum not found")
            return False
        
        # Test 4: Test Environment Variable Support
        print("\n4. 🌍 Testing Environment Variable Support...")
        env_key = os.getenv("OPENROUTER_API_KEY")
        if env_key:
            print("   ✅ OPENROUTER_API_KEY environment variable is set")
            print(f"   🔑 Key format: {env_key[:8]}...{env_key[-4:] if len(env_key) > 12 else 'short'}")
        else:
            print("   ⚠️  OPENROUTER_API_KEY environment variable not set")
            print("   💡 Set OPENROUTER_API_KEY=sk-or-your-key to test with real API")
        
        # Test 5: Test Fallback Order
        print("\n5. 🔄 Testing Fallback Order...")
        fallback_order = api_manager.fallback_order
        if APIProvider.OPENROUTER in fallback_order:
            position = fallback_order.index(APIProvider.OPENROUTER) + 1
            print(f"   ✅ OpenRouter in fallback order at position {position}")
        else:
            print("   ⚠️  OpenRouter not in current fallback order")
            print("   💡 OpenRouter will be added to fallback when API key is configured")
        
        # Test 6: Test API Key Validation (Mock)
        print("\n6. 🔍 Testing API Key Validation...")
        from api_manager import APIKeyValidator
        validator = APIKeyValidator()
        
        # Test with invalid key to verify validation works
        mock_key = "sk-or-invalid-test-key-12345"
        try:
            result = await validator.validate_openrouter_key(mock_key)
            if not result["valid"]:
                print("   ✅ Validation correctly identifies invalid keys")
                print(f"   📝 Validation message: {result.get('message', 'No message')}")
            else:
                print("   ⚠️  Validation unexpectedly passed with mock key")
        except Exception as e:
            print(f"   ✅ Validation properly handles errors: {type(e).__name__}")
        
        # Test 7: Test Client Initialization
        print("\n7. 🔌 Testing Client Initialization...")
        if APIProvider.OPENROUTER in api_manager.providers:
            print("   ✅ OpenRouter provider is configured")
            
            # Check if client can be initialized
            try:
                api_manager._initialize_clients()
                if APIProvider.OPENROUTER in api_manager.clients:
                    print("   ✅ OpenRouter client initialized successfully")
                else:
                    print("   ⚠️  OpenRouter client not in clients dict (may need valid API key)")
            except Exception as e:
                print(f"   ⚠️  Client initialization issue: {e}")
        else:
            print("   ❌ OpenRouter provider not configured")
        
        # Test 8: Test Model Configuration
        print("\n8. 🤖 Testing Model Configuration...")
        if openrouter_config:
            model = openrouter_config.get('model', 'Unknown')
            if model.startswith('openai/') or model.startswith('anthropic/'):
                print(f"   ✅ Valid OpenRouter model format: {model}")
            else:
                print(f"   ⚠️  Unexpected model format: {model}")
        
        # Test 9: Test Rate Limiting Configuration
        print("\n9. ⏱️  Testing Rate Limiting...")
        if APIProvider.OPENROUTER in api_manager.rate_limiters:
            rate_limiter = api_manager.rate_limiters[APIProvider.OPENROUTER]
            print(f"   ✅ Rate limiter configured for OpenRouter")
            print(f"   📊 Rate limit: {rate_limiter.requests_per_minute} requests/minute")
        else:
            print("   ⚠️  Rate limiter not configured for OpenRouter")
        
        # Test 10: Integration Summary
        print("\n10. 📊 Integration Summary...")
        integration_score = 0
        total_checks = 8
        
        checks = [
            openrouter_config is not None,  # Configuration exists
            APIProvider.OPENROUTER in [p for p in APIProvider],  # Enum exists
            hasattr(validator, 'validate_openrouter_key'),  # Validation method exists
            APIProvider.OPENROUTER in api_manager.providers,  # Provider configured
            openrouter_config and openrouter_config.get('model', '').startswith(('openai/', 'anthropic/')),  # Valid model
            APIProvider.OPENROUTER in api_manager.rate_limiters,  # Rate limiter
            'openrouter' in [p['provider'] for p in providers],  # Listed in providers
            openrouter_config and 'base_url' in openrouter_config  # Base URL configured
        ]
        
        integration_score = sum(checks)
        percentage = (integration_score / total_checks) * 100
        
        print(f"   📈 Integration Score: {integration_score}/{total_checks} ({percentage:.1f}%)")
        
        if percentage >= 90:
            print("   🎉 Excellent! OpenRouter integration is fully functional")
        elif percentage >= 70:
            print("   ✅ Good! OpenRouter integration is mostly working")
        else:
            print("   ⚠️  OpenRouter integration needs attention")
        
        return percentage >= 70
        
    except Exception as e:
        print(f"\n❌ End-to-end test failed: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False

async def main():
    """Run the end-to-end test"""
    print("🧪 Starting OpenRouter End-to-End Test Suite")
    print("This test demonstrates the complete OpenRouter integration")
    print()
    
    success = await test_openrouter_end_to_end()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 END-TO-END TEST PASSED!")
        print("🌐 OpenRouter integration is working correctly")
        print()
        print("Next steps:")
        print("1. Set OPENROUTER_API_KEY environment variable for full functionality")
        print("2. Run: python aetherforge-keys set openrouter")
        print("3. Test with: python aetherforge-keys test openrouter")
    else:
        print("❌ END-TO-END TEST FAILED!")
        print("Please check the issues above and fix them")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
