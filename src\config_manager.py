"""
Configuration Manager for Aetherforge
Handles all configuration, workflows, and customization settings
"""

import json
import os
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class WorkflowConfig:
    """Configuration for a specific workflow"""
    name: str
    description: str
    phases: List[str]
    agents: List[str]
    parallel_execution: bool = False
    timeout_minutes: int = 30
    retry_attempts: int = 3

@dataclass
class AgentConfig:
    """Configuration for an AI agent"""
    name: str
    role: str
    model: str = "gpt-4"
    temperature: float = 0.7
    max_tokens: int = 2000
    custom_instructions: str = ""
    capabilities: List[str] = None
    
    def __post_init__(self):
        if self.capabilities is None:
            self.capabilities = []

@dataclass
class TechnologyStack:
    """Technology stack configuration"""
    name: str
    project_types: List[str]
    frontend: List[str] = None
    backend: List[str] = None
    database: List[str] = None
    deployment: List[str] = None
    testing: List[str] = None
    
    def __post_init__(self):
        if self.frontend is None:
            self.frontend = []
        if self.backend is None:
            self.backend = []
        if self.database is None:
            self.database = []
        if self.deployment is None:
            self.deployment = []
        if self.testing is None:
            self.testing = []

@dataclass
class AetherforgeConfig:
    """Main Aetherforge configuration"""
    version: str = "1.0.0"
    environment: str = "development"
    debug: bool = False
    
    # API Configuration (managed by APIManager)
    # Note: API keys are now managed by the secure APIManager
    # These fields are kept for backward compatibility but deprecated
    openai_api_key: str = ""  # DEPRECATED: Use APIManager instead
    anthropic_api_key: str = ""  # DEPRECATED: Use APIManager instead
    
    # Service URLs
    orchestrator_url: str = "http://localhost:8000"
    archon_url: str = "http://localhost:8100"
    mcp_url: str = "http://localhost:8051"
    pheromind_url: str = "http://localhost:8502"
    bmad_url: str = "http://localhost:8503"
    
    # Project Settings
    projects_dir: str = "./projects"
    max_concurrent_projects: int = 5
    project_timeout_minutes: int = 30
    
    # Performance Settings
    log_level: str = "info"
    enable_pheromone_persistence: bool = True
    pheromone_file: str = "./pheromones.json"
    
    # Feature Flags
    enable_mcp_research: bool = True
    enable_pheromind_coordination: bool = True
    enable_archon_generation: bool = True
    enable_bmad_methodology: bool = True
    
    # Monitoring
    enable_monitoring: bool = False
    prometheus_port: int = 9090
    grafana_port: int = 3001

class ConfigurationManager:
    """Manages all Aetherforge configuration"""
    
    def __init__(self, config_file: str = "aetherforge_config.json"):
        self.config_file = Path(config_file)
        self.config = AetherforgeConfig()
        self.workflows: Dict[str, WorkflowConfig] = {}
        self.agents: Dict[str, AgentConfig] = {}
        self.tech_stacks: Dict[str, TechnologyStack] = {}
        
        self._load_configuration()
        self._initialize_default_workflows()
        self._initialize_default_agents()
        self._initialize_default_tech_stacks()
    
    def _load_configuration(self):
        """Load configuration from file"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    data = json.load(f)
                    
                    # Load main config
                    if 'config' in data:
                        config_dict = data['config']
                        for key, value in config_dict.items():
                            if hasattr(self.config, key):
                                setattr(self.config, key, value)
                    
                    # Load workflows
                    if 'workflows' in data:
                        for name, workflow_data in data['workflows'].items():
                            self.workflows[name] = WorkflowConfig(**workflow_data)
                    
                    # Load agents
                    if 'agents' in data:
                        for name, agent_data in data['agents'].items():
                            self.agents[name] = AgentConfig(**agent_data)
                    
                    # Load tech stacks
                    if 'tech_stacks' in data:
                        for name, stack_data in data['tech_stacks'].items():
                            self.tech_stacks[name] = TechnologyStack(**stack_data)
                            
                logger.info("Configuration loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load configuration: {e}")
            logger.info("Using default configuration")
    
    def save_configuration(self):
        """Save configuration to file"""
        try:
            data = {
                'config': asdict(self.config),
                'workflows': {name: asdict(workflow) for name, workflow in self.workflows.items()},
                'agents': {name: asdict(agent) for name, agent in self.agents.items()},
                'tech_stacks': {name: asdict(stack) for name, stack in self.tech_stacks.items()},
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(data, f, indent=2)
                
            logger.info("Configuration saved successfully")
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
    
    def _initialize_default_workflows(self):
        """Initialize default workflows if not already configured"""
        if not self.workflows:
            self.workflows = {
                "greenfield-fullstack": WorkflowConfig(
                    name="Greenfield Full Stack",
                    description="Complete full-stack application development",
                    phases=["requirements_analysis", "architecture_design", "development", "testing", "deployment"],
                    agents=["analyst", "architect", "developer", "qa"],
                    timeout_minutes=30
                ),
                "greenfield-frontend": WorkflowConfig(
                    name="Greenfield Frontend",
                    description="Frontend-only application development",
                    phases=["requirements_analysis", "ui_design", "frontend_development", "testing"],
                    agents=["analyst", "designer", "frontend_developer", "qa"],
                    timeout_minutes=20
                ),
                "greenfield-service": WorkflowConfig(
                    name="Greenfield Service",
                    description="Backend service or API development",
                    phases=["requirements_analysis", "api_design", "backend_development", "testing"],
                    agents=["analyst", "architect", "backend_developer", "qa"],
                    timeout_minutes=25
                ),
                "greenfield-mobile": WorkflowConfig(
                    name="Greenfield Mobile",
                    description="Mobile application development",
                    phases=["requirements_analysis", "mobile_design", "mobile_development", "testing"],
                    agents=["analyst", "mobile_designer", "mobile_developer", "qa"],
                    timeout_minutes=35
                )
            }
    
    def _initialize_default_agents(self):
        """Initialize default agent configurations"""
        if not self.agents:
            self.agents = {
                "analyst": AgentConfig(
                    name="Requirements Analyst",
                    role="analyst",
                    model="gpt-4",
                    temperature=0.3,
                    max_tokens=2000,
                    custom_instructions="Focus on thorough requirements analysis and user story creation",
                    capabilities=["requirements_analysis", "user_story_creation", "stakeholder_analysis"]
                ),
                "architect": AgentConfig(
                    name="System Architect",
                    role="architect",
                    model="gpt-4",
                    temperature=0.5,
                    max_tokens=3000,
                    custom_instructions="Design scalable, maintainable system architectures",
                    capabilities=["system_design", "technology_selection", "architecture_documentation"]
                ),
                "developer": AgentConfig(
                    name="Full Stack Developer",
                    role="developer",
                    model="gpt-4",
                    temperature=0.7,
                    max_tokens=4000,
                    custom_instructions="Write clean, well-documented, production-ready code",
                    capabilities=["frontend_development", "backend_development", "database_design", "api_development"]
                ),
                "qa": AgentConfig(
                    name="Quality Assurance",
                    role="qa",
                    model="gpt-4",
                    temperature=0.3,
                    max_tokens=2000,
                    custom_instructions="Ensure high quality through comprehensive testing and validation",
                    capabilities=["testing", "validation", "documentation", "quality_assurance"]
                )
            }
    
    def _initialize_default_tech_stacks(self):
        """Initialize default technology stacks"""
        if not self.tech_stacks:
            self.tech_stacks = {
                "modern-fullstack": TechnologyStack(
                    name="Modern Full Stack",
                    project_types=["fullstack"],
                    frontend=["React", "TypeScript", "Tailwind CSS", "Vite"],
                    backend=["Node.js", "Express", "TypeScript"],
                    database=["PostgreSQL", "Prisma"],
                    deployment=["Docker", "Nginx"],
                    testing=["Jest", "Cypress", "Supertest"]
                ),
                "react-frontend": TechnologyStack(
                    name="React Frontend",
                    project_types=["frontend"],
                    frontend=["React", "TypeScript", "Tailwind CSS", "React Router"],
                    testing=["Jest", "React Testing Library"],
                    deployment=["Vite", "Netlify"]
                ),
                "node-api": TechnologyStack(
                    name="Node.js API",
                    project_types=["backend", "api"],
                    backend=["Node.js", "Express", "TypeScript"],
                    database=["PostgreSQL", "MongoDB"],
                    testing=["Jest", "Supertest"],
                    deployment=["Docker", "PM2"]
                ),
                "react-native-mobile": TechnologyStack(
                    name="React Native Mobile",
                    project_types=["mobile"],
                    frontend=["React Native", "TypeScript", "Expo"],
                    testing=["Jest", "Detox"],
                    deployment=["Expo Build", "App Store Connect"]
                )
            }
    
    def get_workflow(self, name: str) -> Optional[WorkflowConfig]:
        """Get workflow configuration by name"""
        return self.workflows.get(name)
    
    def get_agent(self, name: str) -> Optional[AgentConfig]:
        """Get agent configuration by name"""
        return self.agents.get(name)
    
    def get_tech_stack(self, name: str) -> Optional[TechnologyStack]:
        """Get technology stack by name"""
        return self.tech_stacks.get(name)
    
    def get_tech_stack_for_project_type(self, project_type: str) -> Optional[TechnologyStack]:
        """Get the best technology stack for a project type"""
        for stack in self.tech_stacks.values():
            if project_type in stack.project_types:
                return stack
        return None
    
    def update_config(self, **kwargs):
        """Update main configuration"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        self.save_configuration()
    
    def add_workflow(self, workflow: WorkflowConfig):
        """Add or update a workflow"""
        self.workflows[workflow.name] = workflow
        self.save_configuration()
    
    def add_agent(self, agent: AgentConfig):
        """Add or update an agent"""
        self.agents[agent.name] = agent
        self.save_configuration()
    
    def add_tech_stack(self, stack: TechnologyStack):
        """Add or update a technology stack"""
        self.tech_stacks[stack.name] = stack
        self.save_configuration()
    
    def get_environment_config(self) -> Dict[str, Any]:
        """Get environment-specific configuration"""
        env_config = {
            "OPENAI_API_KEY": self.config.openai_api_key,
            "ANTHROPIC_API_KEY": self.config.anthropic_api_key,
            "ORCHESTRATOR_URL": self.config.orchestrator_url,
            "ARCHON_URL": self.config.archon_url,
            "MCP_URL": self.config.mcp_url,
            "PHEROMIND_URL": self.config.pheromind_url,
            "BMAD_URL": self.config.bmad_url,
            "PROJECTS_DIR": self.config.projects_dir,
            "MAX_CONCURRENT_PROJECTS": str(self.config.max_concurrent_projects),
            "PROJECT_TIMEOUT_MINUTES": str(self.config.project_timeout_minutes),
            "LOG_LEVEL": self.config.log_level,
            "PHEROMONE_FILE": self.config.pheromone_file,
            "AETHERFORGE_ENV": self.config.environment,
            "DEBUG": str(self.config.debug).lower()
        }
        
        return {k: v for k, v in env_config.items() if v}

# Global configuration manager instance
_config_manager = None

def get_config_manager() -> ConfigurationManager:
    """Get the global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigurationManager()
    return _config_manager

def load_environment_from_config():
    """Load environment variables from configuration"""
    config_manager = get_config_manager()
    env_config = config_manager.get_environment_config()
    
    for key, value in env_config.items():
        if key not in os.environ:
            os.environ[key] = value
