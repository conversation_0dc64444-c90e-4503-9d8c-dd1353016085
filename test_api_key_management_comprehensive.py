#!/usr/bin/env python3
"""
Comprehensive test suite for the API key management system
Tests all providers, security features, CLI functionality, and integration
"""

import sys
import os
import asyncio
import tempfile
import shutil
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_secure_storage():
    """Test secure storage functionality"""
    print("🔐 Testing Secure Storage")
    print("=" * 30)
    
    try:
        from api_manager import SecureKeyStorage
        
        # Test with temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            storage_path = os.path.join(temp_dir, "test_keys.enc")
            storage = SecureKeyStorage(storage_path)
            
            # Test storing and retrieving keys
            test_key = "sk-test-key-12345"
            storage.store_key("test_provider", test_key)
            retrieved_key = storage.load_key("test_provider")
            
            if retrieved_key == test_key:
                print("   ✅ Key storage and retrieval works")
            else:
                print("   ❌ Key storage/retrieval failed")
                return False
            
            # Test key listing
            keys = storage.load_all_keys()
            if "test_provider" in keys:
                print("   ✅ Key listing works")
            else:
                print("   ❌ Key listing failed")
                return False
            
            # Test key removal
            storage.remove_key("test_provider")
            keys_after_removal = storage.load_all_keys()
            if "test_provider" not in keys_after_removal:
                print("   ✅ Key removal works")
            else:
                print("   ❌ Key removal failed")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Secure storage test failed: {e}")
        return False

async def test_api_providers():
    """Test all API providers"""
    print("\n🌐 Testing API Providers")
    print("=" * 30)
    
    try:
        from api_manager import APIManager, APIProvider
        
        api_manager = APIManager()
        
        # Test provider enumeration
        expected_providers = ["openai", "anthropic", "azure", "openrouter", "local", "ollama"]
        actual_providers = [p.value for p in APIProvider]
        
        missing_providers = set(expected_providers) - set(actual_providers)
        if missing_providers:
            print(f"   ❌ Missing providers: {missing_providers}")
            return False
        else:
            print("   ✅ All expected providers found")
        
        # Test provider configuration
        configured_providers = api_manager.list_configured_providers()
        provider_names = [p["provider"] for p in configured_providers]
        
        if "openrouter" in provider_names:
            print("   ✅ OpenRouter provider configured")
        else:
            print("   ❌ OpenRouter provider not configured")
            return False
        
        # Test default configurations
        for provider in APIProvider:
            if provider in api_manager.providers:
                config = api_manager.providers[provider]
                if config.model and config.base_url:
                    print(f"   ✅ {provider.value}: {config.model} @ {config.base_url}")
                else:
                    print(f"   ⚠️  {provider.value}: incomplete configuration")
        
        return True
        
    except Exception as e:
        print(f"   ❌ API provider test failed: {e}")
        return False

async def test_key_validation():
    """Test API key validation"""
    print("\n🔍 Testing Key Validation")
    print("=" * 30)
    
    try:
        from api_manager import APIKeyValidator, APIProvider
        
        validator = APIKeyValidator()
        
        # Test validation functions exist
        validation_methods = {
            APIProvider.OPENAI: validator.validate_openai_key,
            APIProvider.ANTHROPIC: validator.validate_anthropic_key,
            APIProvider.AZURE: validator.validate_azure_key,
            APIProvider.OPENROUTER: validator.validate_openrouter_key,
        }
        
        for provider, method in validation_methods.items():
            if callable(method):
                print(f"   ✅ {provider.value} validation method exists")
            else:
                print(f"   ❌ {provider.value} validation method missing")
                return False
        
        # Test invalid key handling
        invalid_key = "invalid-key-123"
        result = await validator.validate_openrouter_key(invalid_key)

        if not result["valid"]:
            print("   ✅ Invalid key handling works")
        else:
            print("   ❌ Invalid key handling failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Key validation test failed: {e}")
        return False

async def test_cli_functionality():
    """Test CLI functionality"""
    print("\n💻 Testing CLI Functionality")
    print("=" * 30)
    
    try:
        from api_key_cli import APIKeyCLI
        
        cli = APIKeyCLI()
        
        # Test CLI creation
        print("   ✅ CLI created successfully")
        
        # Test provider validation
        valid_providers = ["openai", "anthropic", "azure", "openrouter", "local", "ollama"]
        for provider in valid_providers:
            try:
                from api_manager import APIProvider
                provider_enum = APIProvider(provider)
                print(f"   ✅ {provider} provider validation works")
            except ValueError:
                print(f"   ❌ {provider} provider validation failed")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ CLI functionality test failed: {e}")
        return False

async def test_integration_features():
    """Test integration features"""
    print("\n🔗 Testing Integration Features")
    print("=" * 30)
    
    try:
        from api_manager import APIManager, APIProvider
        
        api_manager = APIManager()
        
        # Test fallback mechanism
        fallback_order = api_manager.fallback_order
        if APIProvider.OPENROUTER in fallback_order:
            print("   ✅ OpenRouter in fallback order")
        else:
            print("   ❌ OpenRouter not in fallback order")
            return False
        
        # Test client initialization
        api_manager._initialize_clients()
        
        # Check if clients are properly initialized
        initialized_providers = list(api_manager.clients.keys())
        if len(initialized_providers) > 0:
            print(f"   ✅ {len(initialized_providers)} clients initialized")
        else:
            print("   ⚠️  No clients initialized (may need API keys)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Integration features test failed: {e}")
        return False

async def test_environment_variables():
    """Test environment variable handling"""
    print("\n🌍 Testing Environment Variables")
    print("=" * 30)
    
    try:
        # Test environment variable mapping
        env_vars = {
            "OPENAI_API_KEY": "OpenAI",
            "ANTHROPIC_API_KEY": "Anthropic", 
            "AZURE_OPENAI_API_KEY": "Azure OpenAI",
            "OPENROUTER_API_KEY": "OpenRouter",
            "LOCAL_API_URL": "Local API URL",
            "OLLAMA_API_URL": "Ollama API URL"
        }
        
        for env_var, description in env_vars.items():
            value = os.getenv(env_var)
            if value:
                print(f"   ✅ {description}: Set")
            else:
                print(f"   ⚠️  {description}: Not set")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Environment variable test failed: {e}")
        return False

async def run_comprehensive_tests():
    """Run all comprehensive tests"""
    print("🧪 Comprehensive API Key Management Test Suite")
    print("=" * 50)
    
    tests = [
        ("Secure Storage", test_secure_storage),
        ("API Providers", test_api_providers),
        ("Key Validation", test_key_validation),
        ("CLI Functionality", test_cli_functionality),
        ("Integration Features", test_integration_features),
        ("Environment Variables", test_environment_variables),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<20} | {status}")
    
    print("-" * 50)
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All comprehensive tests passed!")
        print("🔐 API key management system is fully functional!")
    else:
        print(f"\n⚠️  {total-passed} test(s) failed. Please review the issues above.")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(run_comprehensive_tests())
