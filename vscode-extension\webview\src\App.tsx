import React, { useState, useEffect } from 'react';
import { Dashboard } from './components/Dashboard';
import { ProjectCreator } from './components/ProjectCreator';
import { ProjectManager } from './components/ProjectManager';
import { SystemMonitor } from './components/SystemMonitor';
import { Settings } from './components/Settings';
import { Logs } from './components/Logs';
import { Navigation } from './components/Navigation';
import { NotificationProvider } from './components/NotificationProvider';
import { LoadingProvider } from './components/LoadingProvider';
import './App.css';

export type ViewType = 'dashboard' | 'create' | 'projects' | 'monitor' | 'settings' | 'logs';

interface AppState {
  currentView: ViewType;
  projects: any[];
  systemHealth: any;
  isLoading: boolean;
  notifications: any[];
}

declare global {
  interface Window {
    vscode: {
      postMessage: (message: any) => void;
      getState: () => any;
      setState: (state: any) => void;
    };
  }
}

export const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    currentView: 'dashboard',
    projects: [],
    systemHealth: null,
    isLoading: false,
    notifications: []
  });

  useEffect(() => {
    // Listen for messages from the extension
    const messageHandler = (event: MessageEvent) => {
      const message = event.data;
      
      switch (message.type) {
        case 'projectStatusUpdate':
          setState(prev => ({ ...prev, projects: message.data.projects }));
          break;
          
        case 'systemHealthUpdate':
          setState(prev => ({ ...prev, systemHealth: message.data }));
          break;
          
        case 'projectCreationStarted':
          addNotification('Project creation started', 'info');
          setState(prev => ({ ...prev, isLoading: true }));
          break;
          
        case 'projectCreationProgress':
          addNotification(`${message.data.step} (${Math.round(message.data.progress)}%)`, 'info');
          break;
          
        case 'projectCreationCompleted':
          addNotification('Project created successfully!', 'success');
          setState(prev => ({ ...prev, isLoading: false }));
          requestProjectUpdate();
          break;
          
        case 'projectCreationError':
          addNotification(`Project creation failed: ${message.data.error}`, 'error');
          setState(prev => ({ ...prev, isLoading: false }));
          break;
          
        case 'settingsUpdated':
          addNotification('Settings updated successfully', 'success');
          break;
          
        case 'projectOpened':
          addNotification('Project opened in new window', 'success');
          break;
          
        case 'projectDeleted':
          addNotification('Project deleted successfully', 'success');
          requestProjectUpdate();
          break;
          
        case 'projectExported':
          addNotification(`Project exported to ${message.data.exportPath}`, 'success');
          break;
      }
    };

    window.addEventListener('message', messageHandler);
    
    // Initial data load
    requestProjectUpdate();
    requestSystemHealth();
    
    return () => window.removeEventListener('message', messageHandler);
  }, []);

  const addNotification = (message: string, type: 'info' | 'success' | 'warning' | 'error') => {
    const notification = {
      id: Date.now(),
      message,
      type,
      timestamp: new Date()
    };
    
    setState(prev => ({
      ...prev,
      notifications: [...prev.notifications, notification]
    }));
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.filter(n => n.id !== notification.id)
      }));
    }, 5000);
  };

  const requestProjectUpdate = () => {
    window.vscode.postMessage({ type: 'getProjectStatus' });
  };

  const requestSystemHealth = () => {
    window.vscode.postMessage({ type: 'getSystemHealth' });
  };

  const setCurrentView = (view: ViewType) => {
    setState(prev => ({ ...prev, currentView: view }));
  };

  const createProject = (projectData: any) => {
    window.vscode.postMessage({
      type: 'createProject',
      data: {
        ...projectData,
        projectId: `project_${Date.now()}`
      }
    });
  };

  const openProject = (projectPath: string) => {
    window.vscode.postMessage({
      type: 'openProject',
      data: { projectPath }
    });
  };

  const deleteProject = (projectId: string, projectName: string) => {
    window.vscode.postMessage({
      type: 'deleteProject',
      data: { projectId, projectName }
    });
  };

  const exportProject = (projectId: string, projectName: string) => {
    window.vscode.postMessage({
      type: 'exportProject',
      data: { projectId, projectName }
    });
  };

  const updateSettings = (settings: any) => {
    window.vscode.postMessage({
      type: 'configureSettings',
      data: settings
    });
  };

  const viewLogs = (projectId?: string) => {
    window.vscode.postMessage({
      type: 'viewLogs',
      data: { projectId }
    });
  };

  const renderCurrentView = () => {
    switch (state.currentView) {
      case 'dashboard':
        return (
          <Dashboard
            projects={state.projects}
            systemHealth={state.systemHealth}
            onNavigate={setCurrentView}
            onRefresh={() => {
              requestProjectUpdate();
              requestSystemHealth();
            }}
          />
        );
        
      case 'create':
        return (
          <ProjectCreator
            onCreateProject={createProject}
            onCancel={() => setCurrentView('dashboard')}
          />
        );
        
      case 'projects':
        return (
          <ProjectManager
            projects={state.projects}
            onOpenProject={openProject}
            onDeleteProject={deleteProject}
            onExportProject={exportProject}
            onRefresh={requestProjectUpdate}
          />
        );
        
      case 'monitor':
        return (
          <SystemMonitor
            systemHealth={state.systemHealth}
            onRefresh={requestSystemHealth}
          />
        );
        
      case 'settings':
        return (
          <Settings
            onUpdateSettings={updateSettings}
            onCancel={() => setCurrentView('dashboard')}
          />
        );
        
      case 'logs':
        return (
          <Logs
            onViewLogs={viewLogs}
            onBack={() => setCurrentView('dashboard')}
          />
        );
        
      default:
        return <Dashboard projects={state.projects} systemHealth={state.systemHealth} onNavigate={setCurrentView} onRefresh={() => {}} />;
    }
  };

  return (
    <NotificationProvider notifications={state.notifications}>
      <LoadingProvider isLoading={state.isLoading}>
        <div className="app">
          <Navigation
            currentView={state.currentView}
            onNavigate={setCurrentView}
            projectCount={state.projects.length}
            systemStatus={state.systemHealth?.orchestrator?.status || 'unknown'}
          />
          
          <main className="app-main">
            {renderCurrentView()}
          </main>
        </div>
      </LoadingProvider>
    </NotificationProvider>
  );
};

export default App;
