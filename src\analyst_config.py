"""
Configuration and integration utilities for the Analyst Agent
"""

import os
from typing import Dict, Any, List
from dataclasses import dataclass
from pathlib import Path

@dataclass
class AnalystConfig:
    """Configuration for the Analyst Agent"""
    
    # MCP-RAG Configuration
    mcp_url: str = "http://localhost:8051"
    mcp_timeout: int = 60
    mcp_enabled: bool = True
    
    # AI Provider Configuration (managed by APIManager)
    preferred_provider: str = "openai"  # openai, anthropic, azure, local
    model: str = "gpt-4"
    max_tokens: int = 3000
    
    # Research Configuration
    max_research_queries: int = 10
    research_depth: str = "comprehensive"
    code_search_enabled: bool = True
    
    # Output Configuration
    output_format: str = "markdown"
    include_timestamps: bool = True
    generate_readme: bool = True
    
    # Project Type Templates
    project_templates: Dict[str, Dict[str, Any]] = None
    
    def __post_init__(self):
        """Initialize configuration from environment variables"""
        self.mcp_url = os.getenv("MCP_URL", self.mcp_url)
        self.preferred_provider = os.getenv("PREFERRED_AI_PROVIDER", self.preferred_provider)
        self.model = os.getenv("AI_MODEL", self.model)
        
        # Initialize project templates if not provided
        if self.project_templates is None:
            self.project_templates = self._get_default_templates()
    
    def _get_default_templates(self) -> Dict[str, Dict[str, Any]]:
        """Get default project templates"""
        return {
            "web_application": {
                "research_focus": [
                    "modern web frameworks",
                    "responsive design",
                    "web security",
                    "performance optimization"
                ],
                "default_stack": {
                    "frontend": "React + TypeScript",
                    "backend": "Node.js + Express",
                    "database": "PostgreSQL",
                    "deployment": "Docker + Cloud"
                },
                "common_features": [
                    "user authentication",
                    "responsive design",
                    "API integration",
                    "data persistence"
                ]
            },
            "mobile_application": {
                "research_focus": [
                    "mobile development frameworks",
                    "mobile UI/UX patterns",
                    "app store guidelines",
                    "mobile performance"
                ],
                "default_stack": {
                    "framework": "React Native",
                    "language": "TypeScript",
                    "navigation": "React Navigation",
                    "state": "Redux Toolkit"
                },
                "common_features": [
                    "offline capability",
                    "push notifications",
                    "device integration",
                    "app store compliance"
                ]
            },
            "api_service": {
                "research_focus": [
                    "REST API design",
                    "API security",
                    "microservices",
                    "API documentation"
                ],
                "default_stack": {
                    "runtime": "Node.js",
                    "framework": "Express.js",
                    "database": "PostgreSQL",
                    "documentation": "OpenAPI"
                },
                "common_features": [
                    "RESTful endpoints",
                    "authentication",
                    "rate limiting",
                    "comprehensive documentation"
                ]
            },
            "data_platform": {
                "research_focus": [
                    "data pipeline architecture",
                    "data processing frameworks",
                    "data storage solutions",
                    "data visualization"
                ],
                "default_stack": {
                    "processing": "Apache Spark",
                    "storage": "PostgreSQL + S3",
                    "visualization": "D3.js",
                    "orchestration": "Apache Airflow"
                },
                "common_features": [
                    "data ingestion",
                    "data transformation",
                    "analytics dashboard",
                    "data quality monitoring"
                ]
            }
        }

class AnalystIntegration:
    """Integration utilities for the Analyst Agent with Aetherforge components"""
    
    @staticmethod
    def create_orchestrator_context(prompt: str, project_id: str, project_path: str) -> Dict[str, Any]:
        """Create context for orchestrator integration"""
        return {
            "prompt": prompt,
            "project_id": project_id,
            "project_path": project_path,
            "agent_role": "analyst",
            "phase": "requirements_analysis",
            "timestamp": "2025-01-20T00:00:00Z"
        }
    
    @staticmethod
    def create_pheromone_data(specification, analysis_status: str = "complete") -> Dict[str, Any]:
        """Create pheromone data for agent coordination"""
        return {
            "agent_type": "analyst",
            "analysis_status": analysis_status,
            "project_name": specification.project_name,
            "requirements_count": len(specification.requirements.get("functional", [])),
            "user_stories_count": len(specification.user_stories),
            "technical_stack": specification.technical_stack,
            "next_phase": "architecture_design",
            "outputs_ready": True
        }
    
    @staticmethod
    def format_for_architect(specification) -> Dict[str, Any]:
        """Format specification data for architect agent"""
        return {
            "project_name": specification.project_name,
            "description": specification.description,
            "functional_requirements": specification.requirements.get("functional", []),
            "non_functional_requirements": specification.requirements.get("non_functional", []),
            "technical_preferences": specification.technical_stack,
            "constraints": specification.constraints,
            "user_stories": specification.user_stories[:5],  # Top 5 stories for context
            "success_metrics": specification.success_metrics
        }
    
    @staticmethod
    def format_for_developer(specification) -> Dict[str, Any]:
        """Format specification data for developer agent"""
        return {
            "project_name": specification.project_name,
            "technical_stack": specification.technical_stack,
            "architecture": specification.architecture,
            "user_stories": specification.user_stories,
            "acceptance_criteria": [
                {
                    "story_id": story.get("id"),
                    "criteria": story.get("acceptance_criteria", [])
                }
                for story in specification.user_stories
            ],
            "development_priorities": [
                story for story in specification.user_stories 
                if story.get("priority") == "High"
            ]
        }
    
    @staticmethod
    def create_bmad_workflow_context(specification) -> Dict[str, Any]:
        """Create context for BMAD workflow integration"""
        return {
            "methodology": "BMAD",
            "phase": "analysis_complete",
            "project_context": {
                "name": specification.project_name,
                "type": "software_development",
                "complexity": "medium",  # Could be calculated based on requirements
                "timeline": "8-12 weeks"  # Could be estimated from story points
            },
            "deliverables": {
                "requirements_document": "docs/requirements.md",
                "user_stories": "docs/user_stories.md",
                "technical_specification": "docs/technical_specification.md",
                "architecture_overview": "docs/architecture.md"
            },
            "next_actions": [
                "Review and approve requirements",
                "Begin system architecture design",
                "Set up development environment",
                "Create project backlog"
            ]
        }

def load_config_from_file(config_path: str = None) -> AnalystConfig:
    """Load configuration from file"""
    if config_path is None:
        config_path = os.getenv("ANALYST_CONFIG_PATH", "analyst_config.json")
    
    config_file = Path(config_path)
    if config_file.exists():
        import json
        with open(config_file, 'r') as f:
            config_data = json.load(f)
        
        return AnalystConfig(**config_data)
    else:
        # Return default configuration
        return AnalystConfig()

def save_config_to_file(config: AnalystConfig, config_path: str = None):
    """Save configuration to file"""
    if config_path is None:
        config_path = "analyst_config.json"
    
    import json
    from dataclasses import asdict
    
    config_data = asdict(config)
    
    with open(config_path, 'w') as f:
        json.dump(config_data, f, indent=2)

# Environment validation
def validate_environment() -> Dict[str, bool]:
    """Validate environment setup for analyst agent"""
    # Import here to avoid circular imports
    try:
        from .api_manager import get_api_manager, APIProvider
        api_manager = get_api_manager()
        available_providers = api_manager.get_available_providers()
        has_api_provider = len(available_providers) > 0
    except Exception:
        has_api_provider = False

    checks = {
        "api_provider_configured": has_api_provider,
        "mcp_url_configured": bool(os.getenv("MCP_URL")),
        "python_version": True,  # Assume Python 3.8+ if running
        "required_packages": True  # Would check for aiohttp, etc.
    }
    
    return checks

def get_environment_status() -> str:
    """Get human-readable environment status"""
    checks = validate_environment()
    
    status_lines = []
    status_lines.append("🔧 Analyst Agent Environment Status:")
    
    for check, passed in checks.items():
        icon = "✅" if passed else "❌"
        status_lines.append(f"   {icon} {check.replace('_', ' ').title()}")
    
    if all(checks.values()):
        status_lines.append("\n🎉 Environment is ready for analyst agent!")
    else:
        status_lines.append("\n⚠️  Some environment checks failed. Please review configuration.")
    
    return "\n".join(status_lines)

# Example usage and testing
if __name__ == "__main__":
    print(get_environment_status())
    
    # Create and save default config
    config = AnalystConfig()
    save_config_to_file(config, "example_analyst_config.json")
    print("\n📄 Example configuration saved to example_analyst_config.json")
    
    # Test integration utilities
    from analyst_agent import ProjectSpecification
    
    # Create a sample specification for testing
    sample_spec = ProjectSpecification(
        project_name="Sample Project",
        description="A sample project for testing",
        requirements={"functional": [], "non_functional": []},
        user_stories=[],
        technical_stack={"frontend": {"framework": "React"}},
        architecture={"pattern": "Layered"},
        constraints=[],
        success_metrics=[],
        risks=[]
    )
    
    # Test integration methods
    orchestrator_context = AnalystIntegration.create_orchestrator_context(
        "Create a web app", "proj_123", "/path/to/project"
    )
    print(f"\n🔗 Orchestrator context: {orchestrator_context}")
    
    pheromone_data = AnalystIntegration.create_pheromone_data(sample_spec)
    print(f"\n🐜 Pheromone data: {pheromone_data}")
