/* VS Code Theme Variables */
:root {
  --vscode-foreground: var(--vscode-editor-foreground, #cccccc);
  --vscode-background: var(--vscode-editor-background, #1e1e1e);
  --vscode-input-background: var(--vscode-input-background, #3c3c3c);
  --vscode-input-border: var(--vscode-input-border, #3c3c3c);
  --vscode-button-background: var(--vscode-button-background, #0e639c);
  --vscode-button-foreground: var(--vscode-button-foreground, #ffffff);
  --vscode-button-hoverBackground: var(--vscode-button-hoverBackground, #1177bb);
  --vscode-textLink-foreground: var(--vscode-textLink-foreground, #3794ff);
  --vscode-panel-background: var(--vscode-panel-background, #252526);
  --vscode-panel-border: var(--vscode-panel-border, #3c3c3c);
  --vscode-list-hoverBackground: var(--vscode-list-hoverBackground, #2a2d2e);
  --vscode-badge-background: var(--vscode-badge-background, #4d4d4d);
  --vscode-progressBar-background: var(--vscode-progressBar-background, #0e70c0);
  --vscode-errorForeground: var(--vscode-errorForeground, #f48771);
  --vscode-warningForeground: var(--vscode-warningForeground, #ffcc02);
  --vscode-successForeground: #89d185;
  
  /* Custom variables */
  --border-radius: 6px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.4);
}

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--vscode-font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
  font-size: var(--vscode-font-size, 13px);
  line-height: 1.5;
  color: var(--vscode-foreground);
  background-color: var(--vscode-background);
  overflow-x: hidden;
}

/* App layout */
.app {
  display: flex;
  height: 100vh;
  width: 100vw;
}

.app-main {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
  background-color: var(--vscode-background);
}

/* Common button styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: inherit;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn.primary {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.btn.primary:hover:not(:disabled) {
  background-color: var(--vscode-button-hoverBackground);
}

.btn.secondary {
  background-color: transparent;
  color: var(--vscode-foreground);
  border: 1px solid var(--vscode-input-border);
}

.btn.secondary:hover:not(:disabled) {
  background-color: var(--vscode-list-hoverBackground);
}

.btn.danger {
  background-color: var(--vscode-errorForeground);
  color: var(--vscode-button-foreground);
}

.btn.success {
  background-color: var(--vscode-successForeground);
  color: var(--vscode-background);
}

/* Form elements */
input, textarea, select {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--vscode-input-border);
  border-radius: var(--border-radius);
  background-color: var(--vscode-input-background);
  color: var(--vscode-foreground);
  font-family: inherit;
  font-size: inherit;
  transition: border-color 0.2s ease;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--vscode-textLink-foreground);
}

textarea {
  resize: vertical;
  min-height: 80px;
}

/* Card styles */
.card {
  background-color: var(--vscode-panel-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

/* Grid layouts */
.grid {
  display: grid;
  gap: var(--spacing-md);
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Flex utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-wrap {
  flex-wrap: wrap;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

/* Text utilities */
.text-center {
  text-align: center;
}

.text-muted {
  color: var(--vscode-descriptionForeground);
}

.text-error {
  color: var(--vscode-errorForeground);
}

.text-warning {
  color: var(--vscode-warningForeground);
}

.text-success {
  color: var(--vscode-successForeground);
}

/* Spacing utilities */
.m-0 { margin: 0; }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

/* Loading states */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Animations */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* Responsive design */
@media (max-width: 768px) {
  .app-main {
    padding: var(--spacing-md);
  }
  
  .grid-2, .grid-3, .grid-4 {
    grid-template-columns: 1fr;
  }
  
  .btn {
    padding: var(--spacing-sm);
    font-size: 12px;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--vscode-scrollbarSlider-background);
}

::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-background);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* Focus styles for accessibility */
.btn:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid var(--vscode-textLink-foreground);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border-width: 2px;
  }
  
  .btn {
    border-width: 2px;
  }
}
